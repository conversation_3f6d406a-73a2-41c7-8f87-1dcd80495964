/**
 * @format
 */

import { AppRegistry, Platform } from 'react-native';
import App from './App';
import { name as appName } from './app.json';
import messaging from '@react-native-firebase/messaging';
import PushNotification from 'react-native-push-notification';
import { NativeModules } from 'react-native';
const { NotificationManager } = NativeModules;

const { AppForegroundModule } = NativeModules;
// Register background handler
// messaging().setBackgroundMessageHandler(async remoteMessage => {
//     console.log('Message handled in the background!', remoteMessage);
// });
// Initialize the library
// PushNotification.configure({
//     onRegister: function (token) {
//         console.log('TOKEN:', token);
//     },
//     onNotification: function (notification) {
//         console.log('NOTIFICATION:', notification);
//     },
//     onAction: function (notification) {
//         console.log('ACTION:', notification.action);
//         console.log('NOTIFICATION:', notification);
//     },
// });

PushNotification.configure({
    onNotification: function (notification) {
        console.log('NOTIFICATION:', notification);
    },
    popInitialNotification: true,
    requestPermissions: true,
});

// Register background handler
messaging().setBackgroundMessageHandler(async remoteMessage => {
    console.log('Message handled in the background!', remoteMessage);
    PushNotification.localNotification({
        /* Android Only Properties */
        channelId: 'default-channel-id', // (required) channelId, if the channel doesn't exist, it will be created
        largeIcon: 'ic_launcher', // (optional) default: "ic_launcher". Use "" for no large icon.
        smallIcon: 'ic_notification', // (optional) default: "ic_notification" with fallback for "ic_launcher". Use "" for default small icon.
        color: 'red', // (optional) default: system default
        vibrate: true, // (optional) default: true
        vibration: 300, // vibration length in milliseconds, ignored if vibrate=false, default: 1000
        /* iOS and Android properties */
        title: remoteMessage.notification.title, // (optional)
        message: remoteMessage.notification.body, // (required)
        playSound: true, // (optional) default: true
        soundName: 'default', // (optional) Sound to play when the notification is shown. Value of 'default' plays the default sound.
    });
    const intent = {
        action: 'OPEN_APP',
    };

    if (Platform.OS === 'android') {
        // await messaging().displayNotification(remoteMessage.notification, intent);
    }
    // clearNotification(remoteMessage);

});
// const clearNotification = (remoteMessage) => {
//     if (NotificationManager && remoteMessage.notification) {
//         const notificationId = remoteMessage.messageId;
//         NotificationManager.clearNotification(notificationId);
//     }
// };

if (Platform.OS == 'ios') {
    AppRegistry.registerComponent('StepAllll', () => App);
}
else {
    AppRegistry.registerComponent('StepAll', () => App);
}

// AppRegistry.registerComponent(appName, () => App);
