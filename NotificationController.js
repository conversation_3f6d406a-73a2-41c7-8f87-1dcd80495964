import React, { useEffect } from 'react';
import { Alert, Platform } from 'react-native';
import messaging from '@react-native-firebase/messaging';
import PushNotification from 'react-native-push-notification';

PushNotification.createChannel({
    channelId: "channel-id", // (required)
    channelName: "My channel", // (required)
    channelDescription: "A channel to categorise your notifications", // (optional) default: undefined.
    playSound: true, // (optional) default: true
    soundName: "default", // (optional) See 'soundName parameter of localNotification function
    importance: 4, // (optional) default: 4. Int value of the Android notification importance
    vibrate: true, // (optional) default: true. Creates the default vibration patten if true.
},

    (created) => console.log(`creatChannel return'${created}'`)

);


const NotificationController = (props) => {
    // useEffect(() => {
    //     // PushNotification.getChannels(function (channel_ids) {
    //     // console log (channel_ids); // ['channel_id_11
    //     // });
    //     const unsubscribe = messaging().onMessage(async (remoteMessage) => {
    //         console.log('remoteMessage', remoteMessage);
    //         PushNotification.localNotification({
    //             message: remoteMessage.notification.body,
    //             title: remoteMessage.notification.title,
    //             // bigPictureUrl: Platform.OS ? remoteMessage.notification.ios.imageUrl : remoteMessage.notification.android.imageUrl,
    //             // smallicon: Platform.OS ? remoteMessage.notification.ios.imageUrl : remoteMessage.notification.android.imageUrl,
    //             // channelId: remoteMessage notification. android. channelld,
    //             channelId: true,
    //             vibrate: true,
    //         });
    //     });
    //     return unsubscribe;
    // }, []);

    useEffect(() => {
        const unsubscribeOnMessage = messaging().onMessage(async remoteMessage => {
            console.log('A new FCM message arrived!', JSON.stringify(remoteMessage));

            // Display a foreground notification manually
            if (remoteMessage.notification) {

                if (Platform.OS == 'ios') {
                    console.log('ios');
                    PushNotification.localNotification({
                        alertTitle: remoteMessage.notification.title,
                        alertBody: remoteMessage.notification.body,
                    });

                }
                else {
                    console.log('android');
                    PushNotification.localNotification({
                        channelId: true,
                        vibrate: true,
                        title: remoteMessage.notification.title, // (optional)
                        message: remoteMessage.notification.body, // (required)
                        playSound: true, // (optional) default: true
                    });
                }
            }
        });

        return () => {
            // Remove event listener when the component unmounts
            unsubscribeOnMessage();

        };
    }, []);

    // Request permission for notifications
    useEffect(() => {
        const requestPermission = async () => {
            try {
                const authStatus = await messaging().requestPermission();
                const enabled =
                    authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
                    authStatus === messaging.AuthorizationStatus.PROVISIONAL;

                if (enabled) {
                    console.log('Authorization status:', authStatus);
                }
            } catch (error) {
                console.error('Error requesting permission:', error);
            }
        };

        requestPermission();
    }, []);
    return null;

};

export default NotificationController;