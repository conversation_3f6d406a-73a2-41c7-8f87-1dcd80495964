# Wheel of Fortune Game

A Python implementation of the classic Wheel of Fortune game with animations.

## Features

- Animated spinning wheel with physics
- Word puzzles from different categories
- Letter guessing mechanics
- Score tracking
- Win/lose conditions
- Particle effects for celebrations

## Requirements

- Python 3.x
- Pygame library

## Installation

1. Make sure you have Python installed on your system.
2. Install Pygame by running:
   ```
   pip install pygame
   ```

## How to Run

Run the game by executing:
```
python wheel_of_fortune.py
```

## How to Play

1. Press ENTER to start the game
2. Press SPACE to spin the wheel
3. After the wheel stops, type a letter to guess
4. If the letter is in the puzzle, you earn points based on the wheel value
5. If you guess all letters in the puzzle, you win!

## Special Wheel Segments

- **BANKRUPT**: Lose all your points
- **LOSE A TURN**: Skip your turn

## Controls

- **ENTER**: Start game / Restart after winning
- **SPACE**: Spin the wheel
- **A-Z keys**: Guess letters
- **ESC or close window**: Quit the game

Enjoy playing Wheel of Fortune!
