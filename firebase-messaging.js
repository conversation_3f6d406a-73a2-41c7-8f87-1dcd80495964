import messaging from '@react-native-firebase/messaging';
import { NativeModules } from 'react-native';

const { NotificationManager } = NativeModules;

// Handle background notifications
messaging().setBackgroundMessageHandler(async (remoteMessage) => {
    console.log('Received background notification:', remoteMessage);

    // Automatically clear the notification
    clearNotification(remoteMessage);
});

// Function to clear the notification
const clearNotification = (remoteMessage) => {
    if (NotificationManager && remoteMessage.notification) {
        const notificationId = remoteMessage.messageId;
        NotificationManager.clearNotification(notificationId);
    }
};