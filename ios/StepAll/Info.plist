<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		</array>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>Step</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(PRODUCT_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array />
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.319192357229-2ppioc941ofseq5foo4asp5b7f15hmns</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>fb8658304740908446</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>StepAll</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>admin.StepAll</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>FacebookAppID</key>
		<string>8658304740908446</string>
		<key>FacebookDisplayName</key>
		<string>StepAll</string>
		<key>FirebaseAppDelegateProxyEnabled</key>
		<true />
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>tel</string>
			<string>fbapi</string>
			<string>fbapi20130214</string>
			<string>fbapi20130410</string>
			<string>fbapi20130702</string>
			<string>fbapi20131010</string>
			<string>fbapi20131219</string>
			<string>fbapi20140410</string>
			<string>fbapi20140116</string>
			<string>fbapi20150313</string>
			<string>fbapi20150629</string>
			<string>fbapi20160328</string>
			<string>fbauth</string>
			<string>fb-messenger-share-api</string>
			<string>fbauth2</string>
			<string>fbshareextension</string>
			<string>fb-messenger-api</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true />
			<key>NSExceptionDomains</key>
			<dict>
				<key>localhost</key>
				<dict>
					<key>NSExceptionAllowsInsecureHTTPLoads</key>
					<true />
				</dict>
			</dict>
		</dict>
		<key>NSCameraUsageDescription</key>
		<string>Step wants to access your camera to upload your profile image</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Step wants to access your location to attach your ride with it knowing that the app
			won't function without access to the location</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>Step wants to access your location to attach your ride with it knowing that the app
			won't function without access to the location</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Step wants to access your location to attach your ride with it knowing that the app
			won't function without access to the location</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>Allow microphone to upload audios</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>Step wants to access your camera to upload your profile image</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Step wants to access your camera to upload your profile image</string>
		<key>UIAppFonts</key>
		<array>
			<string>Cairo-Bold.ttf</string>
			<string>Cairo-Medium.ttf</string>
			<string>Cairo-Regular.ttf</string>
			<string>AntDesign.ttf</string>
			<string>Entypo.ttf</string>
			<string>EvilIcons.ttf</string>
			<string>Feather.ttf</string>
			<string>FontAwesome.ttf</string>
			<string>FontAwesome5_Brands.ttf</string>
			<string>FontAwesome5_Regular.ttf</string>
			<string>FontAwesome5_Solid.ttf</string>
			<string>Fontisto.ttf</string>
			<string>Foundation.ttf</string>
			<string>Ionicons.ttf</string>
			<string>MaterialCommunityIcons.ttf</string>
			<string>MaterialIcons.ttf</string>
			<string>Octicons.ttf</string>
			<string>Roboto_medium.ttf</string>
			<string>Roboto.ttf</string>
			<string>rubicon-icon-font.ttf</string>
			<string>SimpleLineIcons.ttf</string>
			<string>Zocial.ttf</string>
		</array>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>processing</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>armv7</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
	</dict>
</plist>