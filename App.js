import { NavigationContainer, useNavigation } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import React, { useState } from 'react';
import { Provider, useSelector } from 'react-redux';
import { applyMiddleware, combineReducers, createStore } from 'redux';
import ReduxThunk from 'redux-thunk';


import authReducer from './Store/Reducers/auth';
import messageReducer from './Store/Reducers/chat';
import generalReducer from './Store/Reducers/general';
import profileReducer from './Store/Reducers/profile';
import tripsReducer from './Store/Reducers/trips';

import ConfirmationCode from './src/screens/ConfirmationCode';
import Intro1 from './src/screens/Intro1';
import Intro2 from './src/screens/Intro2';
import Intro3 from './src/screens/Intro3';
import Login from './src/screens/Login';
import Login1 from './src/screens/Login1';
import Registration from './src/screens/Registration';
import Splash from './src/screens/Splash';


import DeliveryCompanies from './src/screens/DeliveryCompanies';
import MyProfileDriver from './src/screens/MyProfileDriver';
import PaymentMethods from './src/screens/PaymentMethods';
import Search from './src/screens/Search';
import TermsAndConditions from './src/screens/TermsAndConditions';


import { Alert, Linking, LogBox, PermissionsAndroid, Platform, StyleSheet, useColorScheme } from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import MapAddress from './src/screens/MapAddress';

import { createDrawerNavigator } from '@react-navigation/drawer';
import { Root } from 'native-base';
import { appFontBold, Black, screenHeight, screenWidth, White } from './src/components/Styles';
import DeliveryData from './src/screens/DeliveryData';
import Destenation from './src/screens/Destenation';
import DetailsTrip from './src/screens/DetailsTrip';
import DriverRegistration from './src/screens/DriverRegistration';
import DriverRegistration1 from './src/screens/DriverRegistration1';
import DriverRequests from './src/screens/DriverRequests';
import Earnings from './src/screens/Earnings';
import MapDriver from './src/screens/MapDriver';
import More from './src/screens/More';
import Rateing from './src/screens/Rateing';
import SubscriptionPackages from './src/screens/SubscriptionPackages';
import TripLog from './src/screens/TripLog';

import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import { useEffect } from 'react';
import Geolocation from 'react-native-geolocation-service';
import { io } from "socket.io-client";
import Home from './src/screens/Home';
import MyPackageDetails from './src/screens/MyPackageDetails';
import MyProfile from './src/screens/MyProfile';
import Orders from './src/screens/Orders';
import OrderVip from './src/screens/OrderVip';
import OrderVipDetails from './src/screens/OrderVipDetails';
import Packages from './src/screens/Packages';
import ReportaVacation from './src/screens/ReportaVacation';
import driverProfile from './Store/Reducers/driverProfile';
import payoutsReducer from './Store/Reducers/payouts';

import PushNotificationIOS from '@react-native-community/push-notification-ios';
import BackgroundTimer from 'react-native-background-timer';
import PushNotification from 'react-native-push-notification';
import Chat from './src/screens/Chat';
import ContactUs from './src/screens/ContactUs';
import DriverOffers from './src/screens/DriverOffers';
import Notifications from './src/screens/Notifications';
import OrderCities from './src/screens/OrderCities';
import PaymentMethods2 from './src/screens/PaymentMethods2';
import PaymentMethodsWallet from './src/screens/PaymentMethodsWallet';
import PaymentWebView from './src/screens/PaymentWebView';
import Portfolio from './src/screens/Portfolio';
import Prizes from './src/screens/Prizes';
import ReceivablesDetails from './src/screens/ReceivablesDetails';
import RequiredUpadte from './src/screens/RequiredUpadte';
import Safety from './src/screens/Safety';
import { updateLocation } from './Store/Actions/socket';
import socketReducer from './Store/Reducers/socket';

import { NativeModules } from 'react-native';
import Test from './src/screens/Test';
import appsFlyer from 'react-native-appsflyer';



export const NEWOFFER = 'NEWOFFER';
export const UPDATEOFFER = 'UPDATEOFFER';
export const NEWDAILYTRIP = 'NEWDAILYTRIP';
export const UPDATEDDAILYTRIP = 'UPDATEDDAILYTRIP';
export const UPDATEDVIPTRIP = 'UPDATEDVIPTRIP';
export const UPDATEDCITYTRIP = 'UPDATEDCITYTRIP';
export const UPDATEDPACKAGETRIP = 'UPDATEDPACKAGETRIP';
export const UPDATEDPACKAGETRIPDRIVER = 'UPDATEDPACKAGETRIPDRIVER';
export const DILYTRIPCANCELLED = 'DILYTRIPCANCELLED';
export const NEWMESSAGE = 'NEWMESSAGE';
// 

const socket = io('https://step-db.com', { transports: ['websocket', 'polling'], forceNew: true, reconnection: true });
// const socket = io('http://*************', { transports: ['websocket', 'polling'], forceNew: true, reconnection: true });


const Stack = createStackNavigator();
const Drawer = createDrawerNavigator();
const { ForegroundService } = NativeModules;
const { WakeUpModule } = NativeModules;
const { NotificationHelper } = NativeModules;
const { BringToForeground } = NativeModules;

// const { AppForegroundModule, AppForegroundService, NotificationHelper } = NativeModules;

// function bringAppToForeground() {
//   if (AppForegroundModule && AppForegroundModule.bringToForeground) {
//     AppForegroundModule.bringToForeground();
//   } else {
//     console.error('AppForegroundModule is not available');
//   }
// }

// const { AppForegroundModule } = NativeModules;
// function bringAppToForeground() {
//   if (AppForegroundModule && AppForegroundModule.bringToForeground) {
//     AppForegroundModule.bringToForeground();
//   } else {
//     console.error('AppForegroundModule is not available');
//   }
// }
// console.log('AppForegroundModule', AppForegroundModule);
// console.log('AppForegroundModule', AppForegroundModule.bringToForeground());

function MyStack() {

  // const appState = React.useRef(AppState.currentState);

  const navigation = useNavigation();
  const trips = useSelector((state) => state.trips);
  const isConnected = useSelector((state) => state.socket.isConnected);
  const isDisconnected = useSelector((state) => state.socket.isDisconnected);

  useEffect(() => {

    appsFlyer.initSdk(
      {
        devKey: 'LJxrXqHE82GrmWngVM7nSQ',
        isDebug: true,
        appId: '6477336440', // Only required for iOS
      },
      (result) => {
        console.log('AppsFlyer initialized:', result);
      },
      (error) => {
        console.error('AppsFlyer init failed:', error);
      }
    );


    // const requestOverlayPermission = () => {
    //   if (Platform.OS === 'android') {
    //     NativeModules.OverlayPermission.requestOverlayPermission();
    //   }
    // };
    // requestOverlayPermission()
    // if (Platform.OS === 'android' && !PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.SYSTEM_ALERT_WINDOW)) {
    //   Linking.openSettings(); // أو افتح نافذة الإذن يدويًا
    //   console.log('androidOverlayPermissionGranted');
    // }

    const Trips = async () => {
      let token = await AsyncStorage.getItem('token')
      let userType = await AsyncStorage.getItem('userType')
      // console.log('tripstrips', trips);
      // if (token && userType == '2') {
      //   if (trips.dailyTrip.dailyTrip) {
      //     if (trips.dailyTrip.dailyTrip.status == "pending") {
      //       console.log('new');
      //       navigation.navigate('MapDriver', { request: 1, item: trips.dailyTrip.dailyTrip });
      //     }
      //   }
      // }
      if (token && userType == '1') {
        if (trips.updatedVipTrip.vip_trip) {
          if (trips.updatedVipTrip.vip_trip.status == "accepted") {
            console.log('new');
            navigation.navigate('DetailsTrip', { item: trips.updatedVipTrip.vip_trip, type: 'orderVip' });
          }
        }
        if (trips.updatedCityTrip.city_trip) {
          if (trips.updatedCityTrip.city_trip.status == "accepted") {
            console.log('new');
            navigation.navigate('DetailsTrip', { item: trips.updatedCityTrip.city_trip, type: 'betweenCities' });
          }
        }
        if (trips.updatedPackageTrip.user_package) {
          if (trips.updatedPackageTrip.user_package.status == "accepted") {
            console.log('new');
            navigation.navigate('DetailsTrip', { item: trips.updatedPackageTrip.user_package, type: 'packages' });
          }
        }
      }
    }
    Trips()

  }, [trips]);

  useEffect(() => {

    // const getFCMToken = async () => {
    //   try {
    //     const authorized = await firebase.messaging().hasPermission();
    //     const fcmToken = await getToken();

    //     if (authorized) return fcmToken;
    //     console.log('fcmToken', fcmToken);
    //     await firebase.messaging().requestPermission();
    //     return fcmToken;
    //   } catch (error) {
    //     console.log(error);
    //   }
    // };

    // getFCMToken()

    const notifications = async () => {
      FirebaseMessagingTypes().onNotificationOpenedApp(remoteMessage => {
        console.log(
          'Notification caused app to open from background state:',
          remoteMessage.notification,
        );
        // navigation.navigate(remoteMessage.data.type);
      });

      // Check whether an initial notification is available
      messaging()
        .getInitialNotification()
        .then(remoteMessage => {
          if (remoteMessage) {
            console.log(
              'Notification caused app to open from quit state:',
              remoteMessage.notification,
            );
            // setInitialRoute(remoteMessage.data.type); // e.g. "Settings"
          }
        });
    }
    // const getToken = async () => {
    //   await messaging().registerDeviceForRemoteMessages();
    //   const FcmToken = await messaging().getToken();
    //   console.log('getFcmToken', FcmToken);
    //   await AsyncStorage.setItem("FcmToken", FcmToken ? FcmToken : '');
    // }

    const requestUserPermission = async () => {
      console.log('requestUserPermission');

      if (Platform.OS === "android") {
        console.log('permission android');
        try {
          PermissionsAndroid.check('android.permission.POST_NOTIFICATIONS').then(
            response => {
              console.log('notification response:', response);
              if (!response) {
                PermissionsAndroid.request('android.permission.POST_NOTIFICATIONS', {
                  title: 'Notification',
                  message:
                    'Step needs access to your notification',
                  buttonNeutral: 'Ask Me Later',
                  buttonNegative: 'Cancel',
                  buttonPositive: 'OK',
                })
              }
            }
          ).catch(
            err => {
              console.log("Notification Error=====>", err);
            }
          )
        } catch (err) {
          console.log(err);
        }
      }
      else {
        console.log('permission ios');

        PushNotificationIOS.requestPermissions();
        const authStatus = await messaging().requestPermission();
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (enabled) {
          console.log('Authorization status:', authStatus);
        }
      }
    }

    // notifications();
    // getToken();
    requestUserPermission();

    PushNotification.createChannel(
      {
        channelId: "channel-id", // (required)
        channelName: "My channel", // (required)
        playSound: true, // (optional) default: true
        vibrate: true, // (optional) default: true. Creates the default vibration pattern if true.

      },
      (created) => console.log(`createChannel returned '${created}'`) // (optional) callback returns whether the channel was created, false means it already existed.
    );

    if (Platform.OS == 'ios') {
      console.log('ios');
      PushNotification.configure({
        onRegister: function (token) {
          console.log("TOKEN:", token);
        },
        onNotification: function (notification) {
          console.log("NOTIFICATION:", notification);
          // Process the notification
          notification.finish(PushNotificationIOS.FetchResult.NoData);
        },
        // senderID: "YOUR_GCM_SENDER_ID",
        permissions: {
          alert: true,
          badge: true,
          sound: true,
        },
        popInitialNotification: true,
        requestPermissions: true,
      });
      // PushNotification.localNotification({
      //   alertTitle: remoteMessage.notification.title,
      //   alertBody: remoteMessage.notification.body,
      // });

    }

    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log('A new FCM message arrived!', JSON.stringify(remoteMessage));
      if (Platform.OS == 'ios') {
        console.log('ios');
        // PushNotification.localNotification({
        //   alertTitle: remoteMessage.notification.title,
        //   alertBody: remoteMessage.notification.body,
        // });
        Alert.alert(
          'New Notification',
          `${remoteMessage.notification.title}\n${remoteMessage.notification.body}`,
          [
            { text: 'OK', onPress: () => console.log('OK Pressed') }
          ],
          { cancelable: true }
        );
      }
      else {
        console.log('A new FCM message arrived!', JSON.stringify(remoteMessage));
        PushNotification.localNotification({
          channelId: 'channel-id',
          title: remoteMessage.notification.title, // (optional)
          message: remoteMessage.notification.body, // (required)
          playSound: true, // (optional) default: true
        });
      }

    });

    // return unsubscribe;
    return () => {
      // Remove event listener and release the sound when the component unmounts
      unsubscribe();
    };
  }, []);

  useEffect(() => {
    const socketDisconnect = async () => {
      socket.disconnect();
    }

    if (isDisconnected) {
      socketDisconnect()
    }

  }, [isDisconnected]);

  useEffect(() => {
    // console.log('bringToForeground',);


    const socketConnect = async () => {
      let userId = await AsyncStorage.getItem('id');
      let userType = await AsyncStorage.getItem('userType');

      if (userId && userType) {
        socket.connect()
        let s = socket.emit('join', {
          userId: userId,
          userType: userType == '2' ? 'driver' : 'user' // user | driver
        });
        console.log('socket connect', s);

        socket.on('joined', (data) => {
          console.log('joined', data);
        });

        socket.on("disconnect", (reason) => {
          console.log("disconnect due to " + reason);
          let s = socket.emit('join', {
            userId: userId,
            userType: userType == '2' ? 'driver' : 'user' // user | driver
          });
        });

        socket.on('new-driver-offer', async (offer) => {
          console.log('new offer:', offer);
          store.dispatch({ type: NEWOFFER, offer: offer });
        });

        socket.on('driver-offer-changed', async (offer) => {
          console.log('updated offer:', offer);
          store.dispatch({ type: UPDATEOFFER, offerUpdated: offer });
        });

        socket.on('new-daily-trip', async (trip) => {


          // WakeUpModuleAppToForeground();


          // if (ForegroundService) {
          //   ForegroundService.startForegroundService();
          //   console.log('NotificationHelper.startForegroundService done');
          // }
          // startForegroundService();

          // if (NotificationHelper) {
          //   NotificationHelper.clearAllNotifications();
          //   console.log('NotificationHelper.clearAllNotifications done');
          // }
          // clearAllNotifications();

          // bringAppToForeground();
          // if (NotificationHelper && NotificationHelper.clearAllNotifications) {
          //   NotificationHelper.clearAllNotifications();
          //   console.log('NotificationHelper', NotificationHelper.clearAllNotifications());
          // }
          // if (ForegroundService && ForegroundService.startForegroundService) {
          //   ForegroundService.startForegroundService();
          //   console.log('ForegroundService', ForegroundService.startForegroundService());

          // }

          // if (WakeUpModule && WakeUpModule.bringAppToForeground) {
          //   WakeUpModule.bringAppToForeground();
          //   console.log('WakeUpModule', WakeUpModule.bringAppToForeground());

          // }
          // if (BringToForeground && BringToForeground.bringToForeground) {
          //   BringToForeground.bringToForeground();
          //   console.log('BringToForeground', BringToForeground.bringToForeground());
          // }

          // showFloatingBubble(10, 10)
          //   .then(() => console.log("Floating Bubble Added"));

          //   // What to do when user press the bubble
          //   BringToForeground.bringToForeground();
          //   hideFloatingBubble()
          //     .then(() => console.log("Floating Bubble Removed"));

          //   console.log("Press Bubble")
          // });

          if (Platform.OS === 'android') {
            console.log('1');

            BringToForeground.hasOverlayPermission(function (hasPermission) {
              console.log('2');
              if (!hasPermission) {
                console.log('3');
                Alert.alert(
                  'Permission Required',
                  'This app needs permission to draw over other apps.',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Grant',
                      onPress: function () {
                        console.log('4');
                        BringToForeground.requestOverlayPermission();
                      },
                    },
                  ]
                );
              }
              else {
                console.log('6');
              }
            });
          }

          if (BringToForeground && BringToForeground.bringToForeground) {
            BringToForeground.bringToForeground();
            console.log('BringToForeground', BringToForeground.bringToForeground());
          }
          console.log('5');

          store.dispatch({ type: NEWDAILYTRIP, trip: trip });
        });

        socket.on('daily-trip-changed', (trip) => {
          console.log('updated daily trip:', trip);
          store.dispatch({ type: UPDATEDDAILYTRIP, updatedDailyTrip: trip });
        });

        socket.on('daily-trip-cancelled', (trip) => {
          console.log('canceled daily trip:', trip);
          store.dispatch({ type: DILYTRIPCANCELLED, dailyTripCancelled: trip });
        });

        socket.on('vip-trip-changed', (trip) => {
          console.log('updated vip trip:', trip);
          store.dispatch({ type: UPDATEDVIPTRIP, updatedVipTrip: trip });
        });

        socket.on('city-trip-changed', (trip) => {
          console.log('updated city trip:', trip);
          store.dispatch({ type: UPDATEDCITYTRIP, updatedCityTrip: trip });
        });

        socket.on('driver-package-trip-changed', (trip) => {
          console.log('updated package trip driver:', trip);
          store.dispatch({ type: UPDATEDPACKAGETRIPDRIVER, updatedPackageTripDriver: trip });
        });

        socket.on('user-package-changed', (trip) => {
          console.log('updated package trip:', trip);
          store.dispatch({ type: UPDATEDPACKAGETRIP, updatedPackageTrip: trip });
        });

        socket.on('new-chat-message', (message) => {
          console.log('new-chat-message:', message);
          store.dispatch({ type: NEWMESSAGE, message: message });
        });

      }

    }

    socketConnect()


  }, [isConnected]);

  return (

    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
      initialRouteName={'Splash'}
    >

      <Stack.Screen name="Splash" component={Splash} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Intro1" component={Intro1} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Intro2" component={Intro2} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Intro3" component={Intro3} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Login" component={Login} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Login1" component={Login1} options={{ gestureEnabled: false }} />
      <Stack.Screen name="ConfirmationCode" component={ConfirmationCode} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Registration" component={Registration} options={{ gestureEnabled: false }} />
      <Stack.Screen name="MapAddress" component={MapAddress} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Search" component={Search} />
      <Stack.Screen name="DeliveryCompanies" component={DeliveryCompanies} />
      <Stack.Screen name="DeliveryData" component={DeliveryData} />
      <Stack.Screen name="SubscriptionPackages" component={SubscriptionPackages} />
      <Stack.Screen name="TripLog" component={TripLog} />
      <Stack.Screen name="DetailsTrip" component={DetailsTrip} options={{ gestureEnabled: false }} />
      <Stack.Screen name="DriverRegistration" component={DriverRegistration} />
      <Stack.Screen name="DriverRegistration1" component={DriverRegistration1} />
      <Stack.Screen name="DriverRequests" component={DriverRequests} options={{ gestureEnabled: false }} />
      <Stack.Screen name="DriverOffers" component={DriverOffers} options={{ gestureEnabled: false }} />
      <Stack.Screen name="MapDriver" component={MapDriver} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Rateing" component={Rateing} />
      <Stack.Screen name="Earnings" component={Earnings} />
      <Stack.Screen name="Portfolio" component={Portfolio} />
      <Stack.Screen name="ReceivablesDetails" component={ReceivablesDetails} />
      <Stack.Screen name="MyProfileDriver" component={MyProfileDriver} />
      <Stack.Screen name="MyProfile" component={MyProfile} />
      <Stack.Screen name="OrderVip" component={OrderVip} />
      <Stack.Screen name="OrderCities" component={OrderCities} />
      <Stack.Screen name="Orders" component={Orders} />
      <Stack.Screen name="OrderVipDetails" component={OrderVipDetails} />
      <Stack.Screen name="Packages" component={Packages} />
      <Stack.Screen name="MyPackageDetails" component={MyPackageDetails} />
      <Stack.Screen name="Home" component={Home} options={{ gestureEnabled: false }} />
      <Stack.Screen name="ReportaVacation" component={ReportaVacation} />
      <Stack.Screen name="More" component={More} />
      <Stack.Screen name="Destenation" component={Destenation} />

      {/* <Stack.Screen name="Home" component={Home} options={{ gestureEnabled: false }} /> */}
      {/* <Stack.Screen name="ContactUs" component={ContactUs} /> */}
      <Stack.Screen name="TermsAndConditions" component={TermsAndConditions} />
      <Stack.Screen name="Safety" component={Safety} />
      <Stack.Screen name="ContactUs" component={ContactUs} />
      <Stack.Screen name="Notifications" component={Notifications} />
      <Stack.Screen name="PaymentMethods" component={PaymentMethods} />
      <Stack.Screen name="PaymentMethods2" component={PaymentMethods2} />
      <Stack.Screen name="PaymentWebView" component={PaymentWebView} />
      <Stack.Screen name="Chat" component={Chat} />
      <Stack.Screen name="RequiredUpadte" component={RequiredUpadte} />
      <Stack.Screen name="PaymentMethodsWallet" component={PaymentMethodsWallet} />
      <Stack.Screen name="Prizes" component={Prizes} />
      <Stack.Screen name="Test" component={Test} />

    </Stack.Navigator>
  );
}


const rootReducer = combineReducers({
  socket: socketReducer,
  auth: authReducer,
  trips: tripsReducer,
  driverProfile: driverProfile,
  general: generalReducer,
  payouts: payoutsReducer,
  profile: profileReducer,
  message: messageReducer,
});

const store = createStore(rootReducer, applyMiddleware(ReduxThunk));

const ASPECT_RATIO = screenWidth / screenHeight



const App = () => {
  useEffect(() => {
    const intervalId = BackgroundTimer.setInterval(() => {
      if (NotificationHelper && NotificationHelper.clearAllNotifications) {
        NotificationHelper.clearAllNotifications();
        console.log('NotificationHelper', NotificationHelper.clearAllNotifications());
      }
      // console.log('Background Timer is running...');
      // const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      //   console.log('Foreground message:', remoteMessage);
      //   bringAppToForeground(); // Bring the app to the foreground
      // });

      // const backgroundHandler = messaging().setBackgroundMessageHandler(async (remoteMessage) => {
      //   console.log('Background message:', remoteMessage);
      //   bringAppToForeground(); // Bring the app to the foreground
      // });

      // return () => {
      //   unsubscribe();
      // };
    }, 10000); // Runs every 5 seconds

    // Clean up the interval when the component is unmounted
    return () => {
      BackgroundTimer.clearInterval(intervalId);
    };

  }, []);

  // useEffect(() => {
  //   // Start the background timer
  //   const intervalId = BackgroundTimer.setInterval(() => {
  //     console.log('Background Timer is running...');
  //     // if (AppForegroundModule && AppForegroundModule.bringToForeground) {
  //     //   AppForegroundModule.bringToForeground;
  //     // }
  //     // Here you can handle your socket connection logic
  //     // checkSocketConnection();
  //   }, 5000); // Runs every 5 seconds

  //   // Clean up the interval when the component is unmounted
  //   return () => {
  //     BackgroundTimer.clearInterval(intervalId);
  //   };
  // }, []);

  // useEffect(() => {


  // }, []);


  console.disableYellowBox = true;
  LogBox.ignoreLogs(['Warning: ...']); // Ignore log notification by message
  LogBox.ignoreAllLogs();//Ignore all log notifications
  const isDarkMode = useColorScheme() === 'dark';
  // const [userId, setUserId] = useState(null);
  // const [userType, setUserType] = useState(null);
  const [region, SetRegion] = useState({
    latitude: 0,
    longitude: 0,
    longitudeDelta: 0.01 * ASPECT_RATIO,
    latitudeDelta: 0.01
  })
  const [lat, SetLat] = useState(0)
  const [lng, SetLng] = useState(0)

  // useEffect(() => {
  //   const socketConnect = async () => {
  //     const userId = await AsyncStorage.getItem('id');
  //     if (userId) {
  //       const userType = await AsyncStorage.getItem('userType');
  //       // let s = socket.emit('join', {
  //       //   userId: userId,
  //       //   userType: userType == '2' ? 'driver' : 'user' // user | driver
  //       // });
  //       // console.log('socket connect', s);
  //       store.dispatch(connectSocket(userId, userType));
  //     }
  //   }
  //   socketConnect()
  // }, [])



  useEffect(() => {

    const showCustomAlert = () => {
      Alert.alert(
        'Permission Alert',
        'The app will not work properly without this permission ',
        [
          {
            text: 'Access permission from settings',
            onPress: () => {
              console.log('start permission');
              Linking.openURL('app-settings:');
              // get_Lat_User()
            },
          },
          // {
          //   text: 'Exit app',
          //   onPress: () => {
          //     console.log('exit');
          //     BackHandler.exitApp()
          //   },
          // },
        ],
        { cancelable: false }
      );
    };

    const requestLocationPermission = async () => {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            'title': 'Location Permission',
            'message': 'MyMapApp needs access to your location'
          }
        )

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          get_Lat_User()
        } else {
          // console.log("Location permission denied")
        }
      } catch (err) {
        console.warn(err)
      }
    }

    const RequestIosPermissio = () => {
      Geolocation.requestAuthorization('always').then((res) => {
        console.log('res', res);
        if (res == 'denied') {
          // BackHandler.exitApp()
          // showCustomAlert();
          // Linking.openURL('app-settings:');
        }
      });
    }

    let currentLat = null;
    let currentLng = null;

    const get_Lat_User = async () => {

      if (Platform.OS == 'ios') {
        RequestIosPermissio()

      }

      Geolocation.getCurrentPosition(
        (position) => {
          console.log('position', position);
          var lat = parseFloat(position.coords.latitude)
          var longi = parseFloat(position.coords.longitude)
          var initialRegion = {
            latitude: lat,
            longitude: longi,
            longitudeDelta: 0.01 * ASPECT_RATIO,
            latitudeDelta: 0.01
          }
          SetRegion(initialRegion)
          SetLat(lat)
          SetLng(longi)
          currentLat = lat;
          currentLng = longi;
        },
        (error) => {
          console.log('error', error);

        },
        { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 1000 },
      );
    }

    if (Platform.OS == 'ios') {
      get_Lat_User()
    }
    else {
      requestLocationPermission();
    }

    const timerId = setInterval(async () => {


      let user_id = await AsyncStorage.getItem('id');
      let user_type = await AsyncStorage.getItem('userType');
      console.log('user_id', user_id);
      console.log('user_type', user_type);
      console.log('lat', currentLat);
      console.log('lng', currentLng);
      if (user_id && user_type && currentLat && currentLng) {
        let s = socket.emit('update-current-location', {
          user_id: user_id,
          user_type: user_type == '2' ? 'driver' : 'user',
          current_lat: currentLat,
          current_lng: currentLng
        });
        store.dispatch(updateLocation(currentLat, currentLng));
        console.log('sssss', s);
      }

    }, 10000)
    return () => clearInterval(timerId);
  }, [])



  const backgroundStyle = {
    backgroundColor: isDarkMode ? Colors.darker : Colors.lighter,
  };

  return (
    <Root>
      {/* <NotificationController /> */}
      <Provider store={store}>
        <NavigationContainer>
          <MyStack />
        </NavigationContainer>
      </Provider>
    </Root>
  );
};

const styles = StyleSheet.create({
  imageContainer: {
    flexDirection: 'row',
    width: screenHeight / 30,
    height: screenHeight / 30,
    borderRadius: screenHeight / 60,
    backgroundColor: White,
    alignItems: 'center',
    justifyContent: 'center'
  },
  image: {
    height: '80%',
    width: '80%',
    resizeMode: 'contain'
  },
  textContainer: {
    alignItems: 'flex-start',
    justifyContent: 'center',
    marginStart: '-10%',
    // height: '100%'
  },
  text: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 25,
    color: Black,
    // lineHeight: 20,
    textAlignVertical: 'center',
    // marginBottom: '-1%'
  },
});

export default App;
