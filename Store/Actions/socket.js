import { io } from "socket.io-client";

// const socket = io('http://65.108.33.237', { transports: ['websocket', 'polling'], forceNew: true });
const socket = io('https://step-db.com', { transports: ['websocket', 'polling'], forceNew: true, reconnection: true });

export const connectSocket = (userId, userType) => {
    // socket.on('connect', async () => {
    //     console.log('userId', userId);
    //     console.log('userType', userId, userType);

    //     let s = socket.emit('join', {
    //         userId: userId,
    //         userType: userType == '2' ? 'driver' : 'user' // user | driver
    //     });
    //     console.log('socket connect', s);
    // });
    return (dispatch) => {
        console.log('Socket connected and user joined', userId, userType);
        dispatch({ type: 'SOCKET_CONNECTED' });
    };
};


export const disconnectSocket = () => {
    return (dispatch) => {
        socket.disconnect();
        console.log('Socket disconnected ');
        dispatch({ type: 'SOCKET_DISCONNECTED' });
    };
};

export const updateLocation = (lat, lng) => {
    return (dispatch) => {
        dispatch({ type: 'LAT', lat: lat });
        dispatch({ type: 'LNG', lng: lng });
    };
};