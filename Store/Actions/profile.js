// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { <PERSON>uffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, URLDriver } from '../../src/components/Constant';

export const TRIPSCOUNT = 'TRIPSCOUNT';


export const getPendingTrip = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'profile/pending-trip?locale=' + lan;
    console.log("getPendingTrip url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getPendingTrip', response);
        // dispatch({ type: PROFILE, profile: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};


export const getNotPaidTripsCount = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'profile/not-paid-trips-count?locale=' + lan;
    console.log("getNotPaidTripsCount url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getNotPaidTripsCount', response);
        dispatch({ type: TRIPSCOUNT, tripsCount: response.data.trips_count });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};






