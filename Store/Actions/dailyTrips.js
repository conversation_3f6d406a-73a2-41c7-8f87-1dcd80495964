// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { <PERSON>uffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, VERSION, Domain } from '../../src/components/Constant';

export const CITIES = 'CITIES';
export const AREAS = 'AREAS';
export const CARTCOUNT = 'CARTCOUNT';
export const NOTIFICATIONCOUNT = 'NOTIFICATIONCOUNT';
export const MODELS = 'MODELS';
export const PAYMENT = 'PAYMENT';
export const BANKIBAN = 'BANKIBAN';

export const getDailyTripsTypes = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'daily-trips/daily-trip-types?locale=' + lan;
    console.log("getDailyTripsTypes url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getDailyTripsTypes', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const createNewTrip = (
  daily_trip_type_id,
  from_lat,
  from_lng,
  from_address,
  to_lat,
  to_lng,
  to_address,
  notes,
  client_price,
  from_address_id,
  to_address_id,
  expected_time,
  expected_distance,
  genderId,
  max_price,
  min_price,
  original_price
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();
    urlencoded.append('daily_trip_type_id', daily_trip_type_id);
    urlencoded.append('from_lat', from_lat);
    urlencoded.append('from_lng', from_lng);
    urlencoded.append('from_address', from_address);
    urlencoded.append('to_lat', to_lat);
    urlencoded.append('to_lng', to_lng);
    urlencoded.append('to_address', to_address);
    urlencoded.append('notes', notes);
    urlencoded.append('client_price', client_price);
    urlencoded.append('from_address_id', from_address_id);
    urlencoded.append('to_address_id', to_address_id);
    urlencoded.append('payment_method_id', '1');
    urlencoded.append('expected_time', expected_time);
    urlencoded.append('expected_distance', expected_distance);
    urlencoded.append('gender', genderId);
    urlencoded.append('max_price', max_price);
    urlencoded.append('min_price', min_price);
    urlencoded.append('original_price', original_price);

    console.log('createNewTrip urlencoded data', urlencoded);

    let url = URL + 'daily-trips?locale=' + lan;

    console.log("createNewTrip url", url);
    console.log("createNewTrip url", JSON.stringify({
      daily_trip_type_id: daily_trip_type_id,
      from_lat: from_lat,
      from_lng: from_lng,
      from_address: from_address,
      to_lat: to_lat,
      to_lng: to_lng,
      to_address: to_address,
      notes: notes,
      client_price: client_price,
      from_address_id: from_address_id,
      to_address_id: to_address_id,
      payment_method_id: '1',
      expected_time: expected_time,
      expected_distance: expected_distance,
      gender: genderId,
      max_price: max_price,
      min_price: min_price,
      original_price: original_price,
    }),);
    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        daily_trip_type_id: daily_trip_type_id,
        from_lat: from_lat,
        from_lng: from_lng,
        from_address: from_address,
        to_lat: to_lat,
        to_lng: to_lng,
        to_address: to_address,
        notes: notes,
        client_price: client_price,
        from_address_id: from_address_id,
        to_address_id: to_address_id,
        payment_method_id: null,
        expected_time: expected_time,
        expected_distance: expected_distance,
        gender: genderId,
        max_price: max_price,
        min_price: min_price,
        original_price: original_price,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())

      .then(response => {
        console.log('createNewTrip', response);


        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getTrips = (page) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'daily-trips?locale=' + lan + '&page=' + page;
    console.log("getTrips url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getTrips', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getPendingTrip = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'daily-trips/pending?locale=' + lan;
    console.log("getPendingTrip url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getPendingTrip', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};


export const showTrip = (tripID) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'daily-trips/' + tripID;
    console.log("showTrip url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('showTrip', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getDistancee = (start_lat, start_lng, end_lat, end_lng) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = 'https://step-db.com/api/get-distance?start_lat=' + start_lat + '&start_lng=' + start_lng + '&end_lat=' + end_lat + '&end_lng=' + end_lng;
    console.log("getDistancee url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getDistancee', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getDriverOffers = (tripID) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'daily-trips/' + tripID + '/driver-offers?locale=' + lan;
    console.log("getDriverOffers url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getDriverOffers', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const changeOfferStatus = (
  tripId,
  status,
  offer_id,
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('changeOfferStatus urlencoded data', urlencoded);

    let url = URL + 'daily-trips/' + tripId + '/update-offer-status?locale=' + lan;

    console.log("changeOfferStatus url", url);
    console.log("changeOfferStatus url", JSON.stringify({
      status: status,
      offer_id: offer_id,
    }),);
    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        status: status,
        offer_id: offer_id,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('changeOfferStatus', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const updatePrice = (
  id,
  client_price
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    urlencoded.append('client_price', client_price);
    console.log('updatePrice urlencoded data', urlencoded);

    let url = URL + 'daily-trips/' + id + '/update-price?locale=' + lan;

    console.log("updatePrice url", url);
    console.log("updatePrice url", JSON.stringify({
      client_price: client_price
    }),);
    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        client_price: client_price,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('updatePrice', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const applyCoupon = (
  code,
  daily_trip_id
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    urlencoded.append('code', code);
    urlencoded.append('daily_trip_id', daily_trip_id);
    console.log('applyCoupon urlencoded data', urlencoded);

    let url = URL + 'daily-trips/apply-coupon?locale=' + lan;

    console.log("applyCoupon url", url);
    console.log("applyCoupon url", JSON.stringify({
      code: code,
      daily_trip_id: daily_trip_id,
    }),);
    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        code: code,
        daily_trip_id: daily_trip_id,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('applyCoupon', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};


export const cancelTrip = (
  id,
  cancel_reason_id,
  cancel_reason_text,
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');

    let url = URL + 'daily-trips/' + id + '/cancel?locale=' + lan;

    console.log("cancelTrip url", url);
    console.log("cancelTrip data", JSON.stringify({
      "cancel_reason_text": cancel_reason_text,
      "cancel_reason_id": cancel_reason_id,
    }));

    return fetch(url, {
      method: 'POST',
      body: JSON.stringify({
        "cancel_reason_text": cancel_reason_text,
        "cancel_reason_id": cancel_reason_id,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('cancelTrip', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const calculatePrice = (
  distance,
  daily_trip_type_id
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    // urlencoded.append('distance', distance);
    // urlencoded.append('daily_trip_type_id', daily_trip_type_id);
    console.log('calculatePrice urlencoded data', urlencoded);

    let url = URL + 'daily-trips/calculate-price?locale=' + lan;

    console.log("calculatePrice url", url);
    console.log("calculatePrice url", JSON.stringify({
      distance: distance,
      daily_trip_type_id: daily_trip_type_id,
    }),);
    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        distance: distance,
        daily_trip_type_id: daily_trip_type_id,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('calculatePrice', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const rateDriver = (
  tripId,
  rating,
  comment
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    urlencoded.append('rating', rating);
    urlencoded.append('comment', comment);
    console.log('rateDriver urlencoded data', urlencoded);

    let url = URL + 'daily-trips/' + tripId + '/review?locale=' + lan;

    console.log("rateDriver url", url);
    console.log("rateDriver url", JSON.stringify({
      rating: rating,
      comment: comment,
    }),);
    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        rating: rating,
        comment: comment,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('rateDriver', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const confirmPayment = (
  id,
  payment_method_id
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');

    let url = URL + 'daily-trips/' + id + '/confirm-payment?locale=' + lan;

    console.log("confirmPayment url", url);

    return fetch(url, {
      method: 'POST',
      body: JSON.stringify({
        payment_method_id: payment_method_id
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('confirmPayment', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };

};

export const getNearbyDrivers = (page) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'daily-trips/nearby-drivers?locale=' + lan;
    console.log("getNearbyDrivers url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getNearbyDrivers', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};
