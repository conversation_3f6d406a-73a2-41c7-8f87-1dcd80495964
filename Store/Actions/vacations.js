// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { <PERSON>uffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, VERSION, Domain } from '../../src/components/Constant';



export const getMyVacations = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'vacations?locale=' + lan;
    console.log("getMyVacations url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getMyVacations', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const createVacation = (user_package_id, start_date, end_date,) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    urlencoded.append('start_date', start_date);
    urlencoded.append('end_date', end_date);
    urlencoded.append('user_package_id', user_package_id);

    console.log('createVacation urlencoded data', urlencoded);

    let url = URL + 'vacations?locale=' + lan;

    console.log("createVacation url", url);
    console.log("createVacation url", JSON.stringify({
      start_date: start_date,
      end_date: end_date,
      user_package_id: user_package_id,
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        start_date: start_date,
        end_date: end_date,
        user_package_id: user_package_id,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('createVacation', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const deleteVacation = (vacationId) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('deleteVacation urlencoded data', urlencoded);

    let url = URL + 'vacations/' + vacationId + '/delete?locale=' + lan;

    console.log("deleteVacation url", url);
    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      // body: JSON.stringify({
      //   start_date: start_date,
      //   end_date: end_date,
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/x-www-form-urlencoded',
        // 'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('deleteVacation', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};
