// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { <PERSON>uffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, VERSION, Domain } from '../../src/components/Constant';

export const CITIES = 'CITIES';
export const AREAS = 'AREAS';
export const CARTCOUNT = 'CARTCOUNT';
export const NOTIFICATIONCOUNT = 'NOTIFICATIONCOUNT';
export const MODELS = 'MODELS';
export const PAYMENT = 'PAYMENT';
export const BANKIBAN = 'BANKIBAN';

export const getAddresses = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'addresses';
    console.log("getAddresses url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getAddresses', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const saveAddress = (addressId, label) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();
    urlencoded.append('lat', label);
    console.log('saveAddress urlencoded data', urlencoded);
    console.log('saveAddress ', JSON.stringify({
      label: label,
    }),);

    let url = URL + 'addresses/' + addressId + '/save?locale=' + lan;

    console.log("saveAddress url", url);
    return fetch(url, {
      method: 'POST',
      // body:urlencoded,
      body: JSON.stringify({
        label: label,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())

      .then(response => {
        console.log('saveAddress', response);


        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const favoriteAddress = (addressId) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();
    // urlencoded.append('lat', DATA.lat);
    // urlencoded.append('lat', DATA.lng);
    // urlencoded.append('lat', DATA.address);
    // console.log('favoriteAddress urlencoded data', urlencoded);

    let url = URL + 'addresses/' + addressId + '/favorite?locale=' + lan;

    console.log("favoriteAddress url", url);
    return fetch(url, {
      method: 'POST',
      // body: JSON.stringify({
      //   appRate: appRate,
      //   serviceRate: serviceRate,
      //   providerRate: providerRate,
      //   comment: comment,
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())

      .then(response => {
        console.log('favoriteAddress', response);


        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const deleteAddress = (addressId) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();
    urlencoded.append('lat', DATA.lat);
    urlencoded.append('lat', DATA.lng);
    urlencoded.append('lat', DATA.address);
    console.log('deleteAddress urlencoded data', urlencoded);

    let url = URL + 'addresses/' + addressId + '/delete?locale=' + lan;

    console.log("deleteAddress url", url);
    return fetch(url, {
      method: 'POST',
      body: JSON.stringify({
        appRate: appRate,
        serviceRate: serviceRate,
        providerRate: providerRate,
        comment: comment,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())

      .then(response => {
        console.log('deleteAddress', response);


        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};