// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { <PERSON>uffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URLDriver } from '../../src/components/Constant';

export const CITIES = 'CITIES';
export const AREAS = 'AREAS';
export const CARTCOUNT = 'CARTCOUNT';
export const NOTIFICATIONCOUNT = 'NOTIFICATIONCOUNT';
export const DRIVERPROFILE = 'DRIVERPROFILE';
export const PAYMENT = 'PAYMENT';
export const BANKIBAN = 'BANKIBAN';





export const getProfile = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'profile?locale=' + lan;
    console.log("getProfile url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getProfile', response);
        dispatch({ type: DRIVERPROFILE, driverProfile: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getCities = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'profile/cities?locale=' + lan;
    console.log("getCities url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getCities', response);
        // dispatch({ type: PROFILE, profile: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getVehicleOptions = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'profile/vehicle-select-options?locale=' + lan;
    console.log("getVehicleOptions url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getVehicleOptions', response);
        // dispatch({ type: PROFILE, profile: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const updateProfile = (
  name,
  mobile_number,
  email,
  gender,
  date_of_birth_gregorian,
  identity_number,
  city_id,
  neighborhood_id,
  national_id_image,
  image,
  driving_license_image,
  car_license_image,
  stc_number,
  iban_number
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const data = new FormData();

    data.append('name', name)
    data.append('mobile_number', mobile_number)
    data.append('email', email)
    data.append('gender', gender)
    data.append('date_of_birth_gregorian', date_of_birth_gregorian)
    data.append('identity_number', identity_number)
    data.append('city_id', city_id)
    data.append('neighborhood_id', neighborhood_id)
    data.append('national_id_image', national_id_image)
    data.append('image', image)
    data.append('driving_license_image', driving_license_image)
    data.append('car_license_image', car_license_image)
    data.append('stc_number', stc_number)
    data.append('iban_number', iban_number)

    console.log('updateProfile urlencoded data', data);
    let url = URLDriver + 'profile/update-profile?locale=' + lan;
    console.log("updateProfile url", url);

    return fetch(url, {
      method: 'POST',
      body: data,
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'multipart/form-data',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('updateProfile', response);
        if (response.data) {
          dispatch({ type: DRIVERPROFILE, driverProfile: response.data });
        }
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const updateVehicle = (
  sequence_number,
  plate_letter_right,
  plate_letter_left,
  plate_letter_middle,
  plate_number,
  front_image,
  back_image,
  right_image,
  left_image,
  inside_image,
  daily_trip_type_id,
  car_brand_id,
  car_model_id,
  year,
  color,
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const data = new FormData();

    data.append('sequence_number', sequence_number)
    data.append('plate_letter_right', plate_letter_right)
    data.append('plate_letter_left', plate_letter_left)
    data.append('plate_letter_middle', plate_letter_middle)
    data.append('plate_number', plate_number)
    data.append('front_image', front_image)
    data.append('back_image', back_image)
    data.append('right_image', right_image)
    data.append('left_image', left_image)
    data.append('inside_image', inside_image)
    data.append('daily_trip_type_id', daily_trip_type_id)
    data.append('car_brand_id', car_brand_id)
    data.append('car_model_id', car_model_id)
    data.append('year', year)
    data.append('color', color)

    console.log('updateVehicle urlencoded data', data);
    let url = URLDriver + 'profile/update-vehicle?locale=' + lan;
    console.log("updateVehicle url", data);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: data,
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'multipart/form-data',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('updateVehicle', response);
        if (response.data) {
          dispatch({ type: DRIVERPROFILE, driverProfile: response.data });
        }
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getReviews = (page) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'profile/reviews?locale=' + lan + '&page=' + page;
    console.log("getReviews url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getReviews', response);
        // dispatch({ type: PROFILE, profile: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getPendingTrip = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'profile/pending-trip?locale=' + lan;
    console.log("getPendingTrip url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getPendingTrip', response);
        // dispatch({ type: PROFILE, profile: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getPendingOffer = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'profile/pending-offer?locale=' + lan;
    console.log("getPendingOffer url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getPendingOffer', response);
        // dispatch({ type: PROFILE, profile: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const updateConnectedStatus = (is_connected) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');

    const urlencoded = new URLSearchParams();

    let url = URLDriver + 'profile/update-connected-status?locale=' + lan;
    console.log("updateConnectedStatus url", url);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        'is_connected': is_connected,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('updateConnectedStatus', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};



