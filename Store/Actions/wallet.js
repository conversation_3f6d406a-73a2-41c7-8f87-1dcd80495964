// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { Buffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URLDriver, URLGeneral } from '../../src/components/Constant';

export const SETTINGS = 'SETTINGS';





export const getPayouts = (page) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'payouts?status=pending' + '&page=' + page;
    console.log("getPayouts url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getPayouts', response);
        // dispatch({ type: SETTINGS, settings: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getWallet = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'wallet';
    console.log("getWallet url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getWallet', response);
        // dispatch({ type: SETTINGS, settings: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getWalletTransactions = (page) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'wallet/transactions' + '?page=' + page;
    console.log("getWalletTransactions url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getWalletTransactions', response);
        // dispatch({ type: SETTINGS, settings: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};




