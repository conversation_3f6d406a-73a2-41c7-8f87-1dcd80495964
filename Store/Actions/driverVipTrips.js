// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { <PERSON>uffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URLDriver } from '../../src/components/Constant';

export const CITIES = 'CITIES';
export const AREAS = 'AREAS';
export const CARTCOUNT = 'CARTCOUNT';
export const NOTIFICATIONCOUNT = 'NOTIFICATIONCOUNT';
export const MODELS = 'MODELS';
export const PAYMENT = 'PAYMENT';
export const BANKIBAN = 'BANKIBAN';


export const getVipRequests = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'vip-trips/trip-requests?locale=' + lan;
    console.log("getVipRequests url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getVipRequests', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const updateRequestStatus = (vip_trip_id, status) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    // console.log('updateStatus urlencoded data', urlencoded);
    let url = URLDriver + 'vip-trips/update-request-status?locale=' + lan;
    console.log("updateRequestStatus url", JSON.stringify({
      vip_trip_id: vip_trip_id,
      status: status
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        vip_trip_id: vip_trip_id,
        status: status
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('updateRequestStatus', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getVipTrips = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'vip-trips?locale=' + lan;
    console.log("getVipTrips url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getVipTrips', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const showTrip = (tripId) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'vip-trips/' + tripId + '/show?locale=' + lan;
    console.log("showTrip url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('showTrip', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const updateStatus = (tripId, status) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    // console.log('updateStatus urlencoded data', urlencoded);
    let url = URLDriver + 'vip-trips/' + tripId + '/update-status?locale=' + lan;
    console.log("updateStatus url", JSON.stringify({
      status: status
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        status: status
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('updateStatus', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const confirmArrival = (tripId, expected_distance) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('confirmArrival urlencoded data', urlencoded);
    let url = URLDriver + 'vip-trips/' + tripId + '/confirm-arrival?locale=' + lan;
    console.log("confirmArrival url", url);
    console.log("confirmArrival body", JSON.stringify({
      expected_distance: expected_distance
    }));

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        expected_distance: expected_distance
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('confirmArrival', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const confirmClientArrival = (tripId) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('confirmClientArrival urlencoded data', urlencoded);
    let url = URLDriver + 'vip-trips/' + tripId + '/confirm-client-arrival?locale=' + lan;
    console.log("confirmClientArrival url", url);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      // body: JSON.stringify({
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('confirmClientArrival', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const finishTrip = (tripId) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('finishTrip urlencoded data', urlencoded);
    let url = URLDriver + 'vip-trips/' + tripId + '/finish-trip?locale=' + lan;
    console.log("finishTrip url", url);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      // body: JSON.stringify({
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('finishTrip', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const report = (tripId, report) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('report urlencoded data', urlencoded);
    let url = URLDriver + 'vip-trips/' + tripId + '/report?locale=' + lan;
    console.log("report url", JSON.stringify({
      report: report
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        report: report
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('report', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const cancel = (tripId, cancel_reason_id, cancel_reason_text) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('cancel urlencoded data', urlencoded);
    let url = URLDriver + 'vip-trips/' + tripId + '/cancel?locale=' + lan;
    console.log("cancel url", JSON.stringify({
      cancel_reason_id: cancel_reason_id,
      cancel_reason_text: cancel_reason_text,
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        cancel_reason_id: cancel_reason_id,
        cancel_reason_text: cancel_reason_text,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('cancel', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const leaveReview = (tripId, rating, comment) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    // console.log('finishTrip urlencoded data', urlencoded);
    let url = URLDriver + 'vip-trips/' + tripId + '/review?locale=' + lan;
    console.log("leaveReview url", JSON.stringify({
      rating: rating,
      comment: comment,
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        rating: rating,
        comment: comment,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('leaveReview', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

