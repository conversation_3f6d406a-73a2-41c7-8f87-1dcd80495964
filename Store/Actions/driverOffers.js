// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { Buffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URLDriver } from '../../src/components/Constant';

export const CITIES = 'CITIES';
export const AREAS = 'AREAS';
export const CARTCOUNT = 'CARTCOUNT';
export const NOTIFICATIONCOUNT = 'NOTIFICATIONCOUNT';
export const MODELS = 'MODELS';
export const PAYMENT = 'PAYMENT';
export const BANKIBAN = 'BANKIBAN';



export const sendOffer = (daily_trip_id, price) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('sendOffer urlencoded data', urlencoded);
    let url = URLDriver + 'driver-offers?locale=' + lan;
    console.log("sendOffer url", url);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        'daily_trip_id': daily_trip_id,
        'price': price,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('sendOffer', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const updatePrice = (daily_trip_id, price) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('updatePrice urlencoded data', urlencoded);
    let url = URLDriver + 'driver-offers/' + daily_trip_id + '/update-price?locale=' + lan;
    console.log("updatePrice url", url);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        'price': price,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('updatePrice', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const cancelOffer = (daily_trip_id, cancel_reason_id, cancel_reason_text) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    // console.log('cancelOffer urlencoded data', urlencoded);
    let url = URLDriver + 'driver-offers/' + daily_trip_id + '/cancel?locale=' + lan;
    console.log("cancelOffer url", JSON.stringify({
      cancel_reason_id: cancel_reason_id,
      cancel_reason_text: cancel_reason_text,
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        cancel_reason_id: cancel_reason_id,
        cancel_reason_text: cancel_reason_text,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('cancelOffer', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const reportOffer = (daily_trip_id) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('reportOffer urlencoded data', urlencoded);
    let url = URLDriver + 'driver-offers/' + daily_trip_id + '/report?locale=' + lan;
    console.log("reportOffer url", url);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      //       body: JSON.stringify({
      // 'price':price,
      //       }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('reportOffer', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

