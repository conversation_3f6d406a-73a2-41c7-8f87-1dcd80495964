// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { <PERSON><PERSON>er } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, VERSION, Domain } from '../../src/components/Constant';
import moment from 'moment';



export const getPackages = (
  destinations,
  start_time,
  return_time,
  repeat_days,
  start_date
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    destinations.map((item, index) => {
      urlencoded.append(`destinations[${index}][from_lat]`, item.from_lat);
      urlencoded.append(`destinations[${index}][from_lng]`, item.from_lng);
      urlencoded.append(`destinations[${index}][from_address]`, item.from_address);
      urlencoded.append(`destinations[${index}][to_lat]`, item.to_lat);
      urlencoded.append(`destinations[${index}][to_lng]`, item.to_lng);
      urlencoded.append(`destinations[${index}][to_address]`, item.to_address);
      urlencoded.append(`destinations[${index}][type]`, item.type);
      if (item.start_time != '') {
        urlencoded.append(`destinations[${index}][start_time]`, moment(item.start_time, ["h:mm A"]).format("HH:mm"));
      }
      if (item.end_time != '') {
        urlencoded.append(`destinations[${index}][end_time]`, moment(item.end_time, ["h:mm A"]).format("HH:mm"));
      }
      urlencoded.append(`destinations[${index}][distance]`, item.distance);
    })
    urlencoded.append('start_time', start_time);
    urlencoded.append('return_time', return_time);
    urlencoded.append('start_date', start_date);
    urlencoded.append('repeat_days', repeat_days);

    let i = 0;
    for (let day of repeat_days) {
      urlencoded.append(`repeat_days[${i}]`, day.id);
      i++;
    }

    let total_distance = 0;
    for (let destination of destinations) {
      total_distance = total_distance + destination.distance
    }
    urlencoded.append('total_distance', total_distance);


    console.log('getPackages urlencoded data', urlencoded);

    let url = URL + 'packages/packages?locale=' + lan;

    console.log("getPackages url", url);
    console.log("getPackages url", JSON.stringify({
      destinations: destinations,
      start_time: start_time,
      return_time: return_time,
      start_date: start_date,
      repeat_days: repeat_days,
      total_distance: total_distance,
    }),);

    return fetch(url, {
      method: 'POST',
      body: urlencoded.toString(),
      // body: JSON.stringify({
      //   destinations: destinations,
      //   start_time: start_time,
      //   return_time: return_time,
      //   repeat_days: repeat_days,
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/x-www-form-urlencoded',
        // 'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getPackages', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const createPackages = (
  destinations,
  start_time,
  return_time,
  start_date,
  repeat_days,
  packageId,
  // paymentMethodId,
  // total_distance,
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    destinations.map((item, index) => {
      urlencoded.append(`destinations[${index}][from_lat]`, item.from_lat);
      urlencoded.append(`destinations[${index}][from_lng]`, item.from_lng);
      urlencoded.append(`destinations[${index}][from_address]`, item.from_address);
      urlencoded.append(`destinations[${index}][to_lat]`, item.to_lat);
      urlencoded.append(`destinations[${index}][to_lng]`, item.to_lng);
      urlencoded.append(`destinations[${index}][to_address]`, item.to_address);
      urlencoded.append(`destinations[${index}][type]`, item.type);
      if (item.start_time != '') {
        urlencoded.append(`destinations[${index}][start_time]`, moment(item.start_time, ["h:mm A"]).format("HH:mm"));
      }
      if (item.end_time != '') {
        urlencoded.append(`destinations[${index}][end_time]`, moment(item.end_time, ["h:mm A"]).format("HH:mm"));
      }
      urlencoded.append(`destinations[${index}][distance]`, item.distance);
    })
    urlencoded.append('start_time', start_time);
    urlencoded.append('return_time', return_time);
    urlencoded.append('start_date', start_date);

    let i = 0;
    for (let day of repeat_days) {
      urlencoded.append(`repeat_days[${i}]`, day.id);
      i++;
    }

    urlencoded.append('package_id', packageId);

    let total_distance = 0;
    for (let destination of destinations) {
      total_distance = total_distance + destination.distance
    }
    urlencoded.append('total_distance', total_distance);

    console.log('createPackages urlencoded data', urlencoded);

    let url = URL + 'packages/create?locale=' + lan;

    console.log("createPackages url", url);
    console.log("createPackages url", JSON.stringify({
      destinations: destinations,
      start_time: start_time,
      return_time: return_time,
      start_date: start_date,
      repeat_days: repeat_days,
      packageId: packageId,
      // paymentMethodId: paymentMethodId,
      total_distance: total_distance,
    }),);

    return fetch(url, {
      method: 'POST',
      body: urlencoded.toString(),
      // body: JSON.stringify({
      //   destinations: destinations,
      //   start_time: start_time,
      //   return_time: return_time,
      //   repeat_days: repeat_days,
      //   packageId: packageId,
      //   paymentMethodId: paymentMethodId,
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/x-www-form-urlencoded',
        // 'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('createPackages', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const createPackagesRequest = (
  destinations,
  start_time,
  return_time,
  start_date,
  repeat_days,
  // paymentMethodId,
  // total_distance,
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    destinations.map((item, index) => {
      urlencoded.append(`destinations[${index}][from_lat]`, item.from_lat);
      urlencoded.append(`destinations[${index}][from_lng]`, item.from_lng);
      urlencoded.append(`destinations[${index}][from_address]`, item.from_address);
      urlencoded.append(`destinations[${index}][to_lat]`, item.to_lat);
      urlencoded.append(`destinations[${index}][to_lng]`, item.to_lng);
      urlencoded.append(`destinations[${index}][to_address]`, item.to_address);
      urlencoded.append(`destinations[${index}][type]`, item.type);
      if (item.start_time != '') {
        urlencoded.append(`destinations[${index}][start_time]`, moment(item.start_time, ["h:mm A"]).format("HH:mm"));
      }
      if (item.end_time != '') {
        urlencoded.append(`destinations[${index}][end_time]`, moment(item.end_time, ["h:mm A"]).format("HH:mm"));
      }
      urlencoded.append(`destinations[${index}][distance]`, item.distance);
    })
    urlencoded.append('start_time', start_time);
    urlencoded.append('return_time', return_time);
    urlencoded.append('start_date', start_date);

    let i = 0;
    for (let day of repeat_days) {
      urlencoded.append(`repeat_days[${i}]`, day.id);
      i++;
    }


    let total_distance = 0;
    for (let destination of destinations) {
      total_distance = total_distance + destination.distance
    }
    urlencoded.append('total_distance', total_distance);

    console.log('createPackages urlencoded data', urlencoded);

    let url = URL + 'packages/create-request?locale=' + lan;

    console.log("createPackages url", url);
    console.log("createPackages url", JSON.stringify({
      destinations: destinations,
      start_time: start_time,
      return_time: return_time,
      start_date: start_date,
      repeat_days: repeat_days,
      total_distance: total_distance,
    }),);

    return fetch(url, {
      method: 'POST',
      body: urlencoded.toString(),
      // body: JSON.stringify({
      //   destinations: destinations,
      //   start_time: start_time,
      //   return_time: return_time,
      //   repeat_days: repeat_days,
      //   packageId: packageId,
      //   paymentMethodId: paymentMethodId,
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/x-www-form-urlencoded',
        // 'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('createPackagesRequest', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getMyPackages = (page) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'packages/my-packages?locale=' + lan + '&page=' + page;
    console.log("getMyPackages url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getMyPackages', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getMyPackage = (pacageId) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'packages/my-package/' + pacageId;
    console.log("getMyPackage url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getMyPackage', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};



export const acceptOffer = (
  paymentMethodId,
  offerId
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    urlencoded.append('payment_method_id', paymentMethodId);
    urlencoded.append('user_package_offer_id', offerId);
    console.log('acceptOffer urlencoded data', urlencoded);

    let url = URL + 'packages/confirm-offer?locale=' + lan;

    console.log("acceptOffer url", url);
    console.log("acceptOffer url", JSON.stringify({
      payment_method_id: paymentMethodId,
      user_package_offer_id: offerId,
    }),);

    return fetch(url, {
      method: 'POST',
      body: urlencoded.toString(),
      // body: JSON.stringify({
      //   client_report: problem,
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/x-www-form-urlencoded',
        // 'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('acceptOffer', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const ReportProblem = (
  id,
  problem
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    urlencoded.append('client_report', problem);
    console.log('getPackages urlencoded data', urlencoded);

    let url = URL + 'packages/' + id + '/report?locale=' + lan;

    console.log("getPackages url", url);
    console.log("getPackages url", JSON.stringify({
      client_report: problem,
    }),);

    return fetch(url, {
      method: 'POST',
      body: urlencoded.toString(),
      // body: JSON.stringify({
      //   client_report: problem,
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/x-www-form-urlencoded',
        // 'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getPackages', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const rateDriver = (
  tripId,
  rating,
  comment
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    urlencoded.append('rating', rating);
    urlencoded.append('comment', comment);
    console.log('rateDriver urlencoded data', urlencoded);

    let url = URL + 'packages/' + tripId + '/review?locale=' + lan;

    console.log("rateDriver url", url);
    console.log("rateDriver url", JSON.stringify({
      rating: rating,
      comment: comment,
    }),);
    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        rating: rating,
        comment: comment,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('rateDriver', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};


