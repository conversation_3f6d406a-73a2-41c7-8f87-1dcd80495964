// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { Buffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URLDriver, URLGeneral } from '../../src/components/Constant';

export const SETTINGS = 'SETTINGS';





export const getSplash = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLGeneral + 'splash?locale=' + lan;
    console.log("getSplash url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        // 'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getSplash', response);
        dispatch({ type: SETTINGS, settings: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getUser = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLGeneral + 'user?locale=' + lan;
    console.log("getUser url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getUser', response);
        // dispatch({ type: SETTINGS, settings: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const supportMessages = (message) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    // console.log('supportMessages urlencoded data', urlencoded);
    let url = URLGeneral + 'support-messages?locale=' + lan;
    console.log("supportMessages url", JSON.stringify({
      message: message
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        message: message
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('supportMessages', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const addFcmToken = (fcm_token) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    // console.log('supportMessages urlencoded data', urlencoded);
    let url = URLGeneral + 'fcm-tokens?locale=' + lan;
    console.log("addFcmToken url", JSON.stringify({
      fcm_token: fcm_token
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        fcm_token: fcm_token
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('addFcmToken', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};


