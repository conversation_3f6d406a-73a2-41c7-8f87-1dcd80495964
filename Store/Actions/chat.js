import { ImagePropTypes } from 'react-native';
import { NavigationActions } from 'react-navigation';
import { <PERSON><PERSON><PERSON> } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, URLDriver, URLGeneral } from '../../src/components/Constant';



export const getChat = (receiver_id, receiver_type, page, subject_id, subject_type) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let userType = await AsyncStorage.getItem('userType')

    let url = URLGeneral + 'chat?receiver_id=' + receiver_id + '&receiver_type=' + receiver_type + '&page=' + page + '&subject_id=' + subject_id + '&subject_type=' + subject_type;
    console.log("getChat url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getChat', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const sendMessage = (receiver_id, receiver_type, message, subject_id, subject_type) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let userType = await AsyncStorage.getItem('userType')

    let url = URLGeneral + 'chat/send-message';
    console.log(url);
    const data = new URLSearchParams();
    data.append('receiver_id', receiver_id);
    data.append('receiver_type', receiver_type);
    data.append('message', message);
    data.append('subject_id', subject_id);
    data.append('subject_type', subject_type);
    console.log('sendMessage data', data);

    return fetch(url, {
      method: 'POST',
      body: JSON.stringify({
        receiver_id: receiver_id,
        receiver_type: receiver_type,
        message: message,
        subject_id: subject_id,
        subject_type: subject_type,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('sendMessage', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};








