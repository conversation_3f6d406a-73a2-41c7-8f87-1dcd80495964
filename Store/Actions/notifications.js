// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { <PERSON>uffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, URLGeneral } from '../../src/components/Constant';


export const getNotifications = (page) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLGeneral + 'notifications?locale=' + lan + '&page=' + page;
    // console.log("deleteNotifications url", JSON.stringify({
    //   fcm_token: fcm_token
    // }),);
    console.log("getNotifications url", url);
    return fetch(url, {
      method: 'GET',
      // body: JSON.stringify({
      //   fcm_token: fcm_token
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getNotifications', response);
        // dispatch({ type: PROFILE, profile: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const deleteNotifications = (id) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    let url = URLGeneral + 'notifications/' + id + '/delete?locale=' + lan;

    console.log('deleteNotifications', url);

    console.log("deleteNotifications url", JSON.stringify({
      id: id
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        id: id
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('deleteNotifications', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const deleteAllNotifications = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    // console.log('supportMessages urlencoded data', urlencoded);
    let url = URLGeneral + 'notifications/delete-all?locale=' + lan;
    console.log("deleteAllNotifications url", JSON.stringify({
      message: message
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        message: message
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('deleteAllNotifications', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};






