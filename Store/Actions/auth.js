// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { <PERSON><PERSON>er } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, Domain, URLDriver } from '../../src/components/Constant';

export const AUTHENTICATE = 'AUTHENTICATE';
export const LOGOUT = 'LOGOUT';
export const UPDATE_PROFILE = 'UPDATE_PROFILE';
export const EXPIREDATE = 'EXPIREDATE';
export const PROFILE = 'PROFILE';

export const authenticate = token => {
  return { type: AUTHENTICATE, token: token };
};

export const login = (dial_code, phone) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');

    let url = URL + 'auth/login?locale=' + lan;
    console.log(url);
    const data = new URLSearchParams();
    data.append('dial_code', dial_code);
    data.append('phone', phone);
    console.log('data', data);

    return fetch(url, {
      method: 'POST',
      // body: data,
      body: JSON.stringify({
        dial_code: dial_code,
        phone: phone,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('response', response);
        // dispatch({ type: AUTHENTICATE, token: response.token });
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const resendVerificationCode = (user_id) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');

    let url = URL + 'auth/resend-verification-code?locale=' + lan;
    console.log(url);
    const data = new URLSearchParams();
    data.append('user_id', user_id);
    console.log('data', data);

    return fetch(url, {
      method: 'POST',
      // body: data,
      body: JSON.stringify({
        user_id: user_id,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('response', response);
        // dispatch({ type: AUTHENTICATE, token: response.token });
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const verify = (user_id, verification_code) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');

    let url = URL + 'auth/verify?locale=' + lan;
    console.log(url);
    const data = new URLSearchParams();
    data.append('user_id', user_id);
    data.append('verification_code', verification_code);
    console.log('data', data);

    return fetch(url, {
      method: 'POST',
      // body: data,
      body: JSON.stringify({
        user_id: user_id,
        verification_code: verification_code,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('response', response);
        dispatch({ type: AUTHENTICATE, token: response.token });
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const register = (user_id, name, email, gender, image) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');

    let url = URL + 'auth/register?locale=' + lan;
    console.log(url);
    const data = new FormData();
    data.append('user_id', user_id);
    data.append('name', name);
    data.append('email', email);
    data.append('gender', gender);
    // if (image) {
    //   data.append('image', image);
    // }
    data.append('image', image);
    console.log('data', data);

    return fetch(url, {
      method: 'POST',
      body: data,
      // body: JSON.stringify({
      //   user_id: user_id,
      //   name: name,
      //   email: email,
      //   gender: gender,
      //   image: image,
      // }),
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('response', response);
        dispatch({ type: AUTHENTICATE, token: response.token });
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const logout = () => {
  return { type: LOGOUT };
};

export const loginDriver = (dial_code, phone) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');

    let url = URLDriver + 'auth/login?locale=' + lan;
    console.log(url);
    const data = new URLSearchParams();
    data.append('dial_code', dial_code);
    data.append('phone', phone);
    console.log('data', data);

    return fetch(url, {
      method: 'POST',
      // body: data,
      body: JSON.stringify({
        dial_code: dial_code,
        phone: phone,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('response', response);
        // dispatch({ type: AUTHENTICATE, token: response.token });
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const resendVerificationCodeDriver = (driver_id) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');

    let url = URLDriver + 'auth/resend-verification-code?locale=' + lan;
    console.log(url);
    const data = new URLSearchParams();
    data.append('driver_id', driver_id);
    console.log('data', data);

    return fetch(url, {
      method: 'POST',
      // body: data,
      body: JSON.stringify({
        driver_id: driver_id,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('response', response);
        // dispatch({ type: AUTHENTICATE, token: response.token });
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const verifyDriver = (driver_id, verification_code) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');

    let url = URLDriver + 'auth/verify?locale=' + lan;
    console.log(url);
    const data = new URLSearchParams();
    data.append('driver_id', driver_id);
    data.append('verification_code', verification_code);
    console.log('data', data);

    return fetch(url, {
      method: 'POST',
      // body: data,
      body: JSON.stringify({
        driver_id: driver_id,
        verification_code: verification_code,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('response', response);
        dispatch({ type: AUTHENTICATE, token: response.token });
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getProfile = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'profile/get?locale=' + lan;
    console.log("getProfile url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getProfile', response);
        dispatch({ type: PROFILE, profile: response.data });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const updateProfile = (
  name,
  dial_code,
  mobile_number,
  email,
  // gender,
  image,
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const data = new FormData();

    data.append('name', name)
    data.append('dial_code', dial_code)
    data.append('phone', mobile_number)
    data.append('email', email)
    // data.append('gender', gender)
    data.append('image', image)

    console.log('updateProfile urlencoded data', data);
    let url = URL + 'profile/update?locale=' + lan;
    console.log("updateProfile url", url);

    return fetch(url, {
      method: 'POST',
      body: data,
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'multipart/form-data',
        // 'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('updateProfile', response);
        if (response.data) {
          dispatch({ type: PROFILE, profile: response.data });
        }
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const updateDriverLocation = (lat, lng) => {
  return async dispatch => {
    let token = await AsyncStorage.getItem('token');
    let lan = await AsyncStorage.getItem('lan');

    let url = URLDriver + 'update-current-location?locale=' + lan;
    console.log('update current location url', url);
    const data = new URLSearchParams();
    data.append('lat', lat);
    data.append('lng', lng);
    console.log('data', data);

    return fetch(url, {
      method: 'POST',
      // body: data,
      body: JSON.stringify({
        lat: lat,
        lng: lng,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('updateDriverLocation', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};



