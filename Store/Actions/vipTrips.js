// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { <PERSON>uffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, VERSION, Domain } from '../../src/components/Constant';


export const createNewTrip = (
  from_lat,
  from_lng,
  from_address,
  to_lat,
  to_lng,
  to_address,
  notes,
  date,
  time,
  passengers_count,
  seat_count,
  from_address_id,
  to_address_id,
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();
    urlencoded.append('from_lat', from_lat);
    urlencoded.append('from_lng', from_lng);
    urlencoded.append('from_address', from_address);
    urlencoded.append('to_lat', to_lat);
    urlencoded.append('to_lng', to_lng);
    urlencoded.append('to_address', to_address);
    urlencoded.append('notes', notes);
    urlencoded.append('date', date);
    urlencoded.append('time', time);
    urlencoded.append('passengers_count', passengers_count);
    urlencoded.append('seat_count', seat_count);
    urlencoded.append('from_address_id', from_address_id);
    urlencoded.append('to_address_id', to_address_id);

    console.log('createNewTrip urlencoded data', urlencoded);

    let url = URL + 'vip-trips?locale=' + lan;

    console.log("createNewTrip url", url);
    console.log("createNewTrip url", JSON.stringify({
      from_lat: from_lat,
      from_lng: from_lng,
      from_address: from_address,
      to_lat: to_lat,
      to_lng: to_lng,
      to_address: to_address,
      notes: notes,
      date: date,
      time: time,
      passengers_count: passengers_count,
      seat_count: seat_count,
      from_address_id: from_address_id,
      to_address_id: to_address_id,
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        from_lat: from_lat,
        from_lng: from_lng,
        from_address: from_address,
        to_lat: to_lat,
        to_lng: to_lng,
        to_address: to_address,
        notes: notes,
        date: date,
        time: time,
        passengers_count: passengers_count,
        seat_count: seat_count,
        from_address_id: from_address_id,
        to_address_id: to_address_id,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('createNewTrip', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const approveTrip = (
  id,
  payment_method_id
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();
    urlencoded.append('payment_method_id', payment_method_id);

    console.log('approveTrip urlencoded data', urlencoded);

    let url = URL + 'vip-trips/' + id + '/approve?locale=' + lan;

    console.log("approveTrip url", url);
    console.log("approveTrip url", JSON.stringify({
      payment_method_id: payment_method_id,
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        payment_method_id: payment_method_id,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('approveTrip', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const cancelTrip = (
  id,
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');

    let url = URL + 'vip-trips/' + id + '/cancel?locale=' + lan;

    console.log("cancelTrip url", url);

    return fetch(url, {
      method: 'POST',
      // body: JSON.stringify({
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('cancelTrip', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const confirmPayment = (
  id,
  payment_method_id
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');

    let url = URL + 'vip-trips/' + id + '/confirm-payment?locale=' + lan;

    console.log("confirmPayment url", url);

    return fetch(url, {
      method: 'POST',
      // body: JSON.stringify({
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('confirmPayment', response);
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const getTrips = (page) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'vip-trips?locale=' + lan + '&page=' + page;
    console.log("getTrips url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getTrips', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const showTrip = (tripID) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'vip-trips/' + tripID;
    console.log("showTrip url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('showTrip', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const rateDriver = (
  tripId,
  rating,
  comment
) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    urlencoded.append('rating', rating);
    urlencoded.append('comment', comment);
    console.log('rateDriver urlencoded data', urlencoded);

    let url = URL + 'vip-trips/' + tripId + '/review?locale=' + lan;

    console.log("rateDriver url", url);
    console.log("rateDriver url", JSON.stringify({
      rating: rating,
      comment: comment,
    }),);
    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        rating: rating,
        comment: comment,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('rateDriver', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};



