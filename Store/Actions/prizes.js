
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URL, VERSION, Domain } from '../../src/components/Constant';


export const getPrizes = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URL + 'prizes';
    console.log("getPrizes url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getPrizes', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const wonPrize = (prize_id) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();
    urlencoded.append('prize_id', prize_id);
    console.log('wonPrize urlencoded data', urlencoded);
    console.log('wonPrize ', JSON.stringify({
      prize_id: prize_id,
    }),);

    let url = URL + 'prizes/won-prize/?locale=' + lan;

    console.log("wonPrize url", url);
    return fetch(url, {
      method: 'POST',
      // body:urlencoded,
      body: JSON.stringify({
        prize_id: prize_id,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('wonPrize', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const applyUserPrize = (daily_trip_id) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();
    urlencoded.append('daily_trip_id', daily_trip_id);
    console.log('applyUserPrize urlencoded data', urlencoded);
    console.log('applyUserPrize ', JSON.stringify({
      daily_trip_id: daily_trip_id,
    }),);

    let url = URL + 'daily-trips/apply-user-prize/?locale=' + lan;

    console.log("applyUserPrize url", url);
    return fetch(url, {
      method: 'POST',
      // body:urlencoded,
      body: JSON.stringify({
        daily_trip_id: daily_trip_id,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('applyUserPrize', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};
