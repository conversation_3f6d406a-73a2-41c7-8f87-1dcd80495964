// import {AsyncStorage} from '@react-native-community/async-storage';
import { ImagePropTypes } from 'react-native';
// export const SIGNUP = 'SIGNUP';
// export const LOGIN = 'LOGIN';
import { NavigationActions } from 'react-navigation';
import { Buffer } from 'redux-saga';
import { Form } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { URLDriver } from '../../src/components/Constant';

export const CITIES = 'CITIES';
export const AREAS = 'AREAS';
export const CARTCOUNT = 'CARTCOUNT';
export const NOTIFICATIONCOUNT = 'NOTIFICATIONCOUNT';
export const MODELS = 'MODELS';
export const PAYMENT = 'PAYMENT';
export const BANKIBAN = 'BANKIBAN';



export const getDailyTrips = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'daily-trips?locale=' + lan;
    console.log("getDailyTrips url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('getDailyTrips', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const myTrips = () => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'daily-trips/my-trips?locale=' + lan;
    console.log("myTrips url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('myTrips', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const showTrip = (tripId) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    let url = URLDriver + 'daily-trips/' + tripId + '/show?locale=' + lan;
    console.log("showTrip url", url);
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('showTrip', response);
        // dispatch({ type: MODELS, firstModel: response.data.firstModel, lastModel: response.data.lastModel });
        return response;
      })

      .catch(error => {
        console.log('error', error);
      });
  };
};

export const confirmArrival = (tripId) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('confirmArrival urlencoded data', urlencoded);
    let url = URLDriver + 'daily-trips/' + tripId + '/confirm-arrival?locale=' + lan;
    console.log("confirmArrival url", url);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      // body: JSON.stringify({
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('confirmArrival', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const confirmClientArrival = (tripId) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('confirmClientArrival urlencoded data', urlencoded);
    let url = URLDriver + 'daily-trips/' + tripId + '/confirm-client-arrival?locale=' + lan;
    console.log("confirmClientArrival url", url);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      // body: JSON.stringify({
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('confirmClientArrival', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const finishTrip = (tripId) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    console.log('finishTrip urlencoded data', urlencoded);
    let url = URLDriver + 'daily-trips/' + tripId + '/finish-trip?locale=' + lan;
    console.log("finishTrip url", url);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      // body: JSON.stringify({
      // }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('finishTrip', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};

export const leaveReview = (tripId, rating, comment) => {
  return async dispatch => {
    let lan = await AsyncStorage.getItem('lan');
    let token = await AsyncStorage.getItem('token');
    const urlencoded = new URLSearchParams();

    // console.log('finishTrip urlencoded data', urlencoded);
    let url = URLDriver + 'daily-trips/' + tripId + '/review?locale=' + lan;
    console.log("leaveReview url", JSON.stringify({
      rating: rating,
      comment: comment,
    }),);

    return fetch(url, {
      method: 'POST',
      // body: urlencoded.toString(),
      body: JSON.stringify({
        rating: rating,
        comment: comment,
      }),
      headers: {
        'Authorization': 'Bearer ' + JSON.parse(token),
        // 'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    })
      .then(res => res.json())
      .then(response => {
        console.log('leaveReview', response);
        return response;
      })
      .catch(error => {
        console.log('error', error);
      });
  };
};