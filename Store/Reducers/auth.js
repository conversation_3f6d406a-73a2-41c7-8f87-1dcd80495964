import { <PERSON><PERSON>GI<PERSON>, UPDATE_PROFILE, AUTHENTICATE, LOGOUT, EXPIREDATE, PROFILE } from "../Actions/auth";

const initialState = {
  token: '',
  profile: {},
  isAllowToAddAd: false
};

export default (state = initialState, action) => {
  switch (action.type) {
    case AUTHENTICATE:
      return {
        ...state,
        token: action.token,
      };
    case LOGOUT:
      return initialState;
    case UPDATE_PROFILE:
      return {
        ...state,
        profile: action.profile,
      };
    case EXPIREDATE:
      return {
        ...state,
        isAllowToAddAd: action.isAllowToAddAd,
      };
    case PROFILE:
      return {
        ...state,
        profile: action.profile,
      };
    default:
      return state;
  }
};
