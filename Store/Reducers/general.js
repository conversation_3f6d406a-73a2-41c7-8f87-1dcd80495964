import { SETTINGS } from "../Actions/general";

const initialState = {
  settings: {},
  clientReasons: [],
  driverReasons: [],
  driverDaily_trip_types: [],
  intro_screens: [],
  slider_images: [],
  force_update: false,
  is_live: false,
  working_cities: [],
  payment_methods_wallet: [],
  five_offer: false,
  ios_version: null,
  android_version: null,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case SETTINGS:
      return {
        ...state,
        settings: action.settings.settings,
        payment_methods_wallet: action.settings.payment_methods_wallet,
        clientReasons: action.settings.cancel_reasons_client,
        driverReasons: action.settings.cancel_reasons_driver,
        driverDaily_trip_types: action.settings.daily_trip_types,
        intro_screens: action.settings.intro_screens,
        slider_images: action.settings.slider_images,
        force_update: action.settings.force_update,
        is_live: action.settings.is_live,
        working_cities: action.settings.settings.working_cities,
        five_offer: action.settings.settings.enable_20km_offer,
        ios_version: action.settings.settings.ios_version,
        android_version: action.settings.settings.android_version,
      };
    default:
      return state;
  }
};
