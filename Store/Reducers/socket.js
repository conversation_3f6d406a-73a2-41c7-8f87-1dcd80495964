const initialState = {
    isConnected: false,
    isDisconnected: false,
    currentLat: null,
    currentLng: null,
};

const socketReducer = (state = initialState, action) => {
    switch (action.type) {
        case 'SOCKET_CONNECTED':
            return {
                ...state,
                isConnected: true,
                isDisconnected: false,
            };
        case 'SOCKET_DISCONNECTED':
            return {
                ...state,
                isConnected: false,
                isDisconnected: true,
            };
        case 'LAT':
            return {
                ...state,
                currentLat: action.lat,
            };
        case 'LNG':
            return {
                ...state,
                currentLng: action.lng,
            };
        default:
            return state;
    }
};

export default socketReducer;