import { <PERSON>WD<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>WO<PERSON>ER, UP<PERSON>TE<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>LED, UPDA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UPDA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UPDA<PERSON>OFFER, UPDATEDPAC<PERSON><PERSON>TRIP, UPDATEDPAC<PERSON>GETRIPDRIVER } from "../../App";
import { CLEARUPDATEDCITYTRIP, CLEARUPDATEDVIPTRIP, CLEARUPDATEDPACKAGETRIP } from "../../src/screens/DetailsTrip";
import { C<PERSON><PERSON><PERSON><PERSON>AILYTRIP, CLEARUPDATEDDAILYTRIP, C<PERSON><PERSON><PERSON><PERSON>YTRIPCANCELLED, CLEARUPDATEDVIPTRIPDRIVER1, <PERSON><PERSON><PERSON>UPDATEDCITYTRIPDRIVER1 } from "../../src/screens/DriverRequests";
import { CLEAROFFERS } from "../../src/screens/MapAddress";
import { CLEAROFFERUPDATED, CLEARDAILYTRIPCANCELLED1, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>UPDATEDDAILYTRIP1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>DCITYTRIP1, CLEARUP<PERSON><PERSON>DVIPTRIPDRIVER } from "../../src/screens/MapDriver";

const initialState = {
  offer: {},
  offerUpdated: {},
  dailyTrip: {},
  updatedDailyTrip: {},
  updatedVipTrip: {},
  updatedCityTrip: {},
  updatedPackageTrip: {},
  updatedPackageTripDriver: {},
  dailyTripCancelled: {},
  tripId: 0,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case NEWOFFER:
      return {
        ...state,
        offer: action.offer,
        offerUpdated: {},
      };
    case UPDATEOFFER:
      return {
        ...state,
        offer: {},
        offerUpdated: action.offerUpdated,
      };
    case CLEAROFFERS:
      return {
        ...state,
        offer: {},
        offerUpdated: {},
      };
    case NEWDAILYTRIP:
      return {
        ...state,
        dailyTrip: action.trip,
        updatedDailyTrip: {},
      };
    case UPDATEDDAILYTRIP:
      return {
        ...state,
        dailyTrip: {},
        updatedDailyTrip: action.updatedDailyTrip,
        tripId: action.updatedDailyTrip.daily_trip.id,
      };
    case DILYTRIPCANCELLED:
      return {
        ...state,
        dailyTripCancelled: action.dailyTripCancelled,
      };
    case CLEARNEWDAILYTRIP:
      return {
        ...state,
        dailyTrip: {},
      };
    case CLEAROFFERUPDATED:
      return {
        ...state,
        offerUpdated: {},
        updatedDailyTrip: {},
      };
    case CLEARUPDATEDDAILYTRIP:
      return {
        ...state,
        updatedDailyTrip: {},
        dailyTrip: {},
        offerUpdated: {},
      };
    case CLEARDID:
      return {
        ...state,
        tripId: 0,
      };
    case CLEARUPDATEDDAILYTRIP1:
      return {
        ...state,
        updatedDailyTrip: {},
        offerUpdated: {},
      };
    case CLEARDAILYTRIPCANCELLED:
      return {
        ...state,
        dailyTripCancelled: {},
      };
    case CLEARDAILYTRIPCANCELLED1:
      return {
        ...state,
        dailyTripCancelled: {},
        updatedDailyTrip: {},
      };
    case UPDATEDVIPTRIP:
      return {
        ...state,
        updatedVipTrip: action.updatedVipTrip
      };
    case CLEARUPDATEDVIPTRIP:
      return {
        ...state,
        updatedVipTrip: {}
      };
    case CLEARUPDATEDVIPTRIPDRIVER:
      return {
        ...state,
        updatedVipTrip: {}
      };
    case CLEARUPDATEDVIPTRIPDRIVER1:
      return {
        ...state,
        updatedVipTrip: {}
      };
    case UPDATEDCITYTRIP:
      return {
        ...state,
        updatedCityTrip: action.updatedCityTrip
      };
    case CLEARUPDATEDCITYTRIP:
      return {
        ...state,
        updatedCityTrip: {}
      };
    case CLEARUPDATEDCITYTRIP1:
      return {
        ...state,
        updatedCityTrip: {}
      };
    case CLEARUPDATEDCITYTRIPDRIVER1:
      return {
        ...state,
        updatedCityTrip: {}
      };
    case UPDATEDPACKAGETRIP:
      return {
        ...state,
        updatedPackageTrip: action.updatedPackageTrip
      };
    case UPDATEDPACKAGETRIPDRIVER:
      return {
        ...state,
        updatedPackageTripDriver: action.updatedPackageTripDriver
      };
    case CLEARUPDATEDPACKAGETRIP:
      return {
        ...state,
        updatedPackageTrip: {}
      };
    default:
      return state;
  }
};
