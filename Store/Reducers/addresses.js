import { CITIES, AREAS, NOTIFICATIONCOUNT, CARTCOUNT, MODELS, PAYMENT, BANKIBAN } from "../Actions/addresses";
const initialState = {
  cities: [],
  areas: [],
  paymentMethods: [],
  cartCount: 0,
  notificationCount: 0,
  firstModel: {},
  lastModel: {},
  bankIban: ''
};

export default (state = initialState, action) => {
  switch (action.type) {
    case CITIES:
      return {
        ...state,
        cities: action.cities,
      };
    case AREAS:
      return {
        ...state,
        areas: action.areas,
      };
    case CARTCOUNT:
      return {
        ...state,
        cartCount: action.cartCount,
      };
    case NOTIFICATIONCOUNT:
      return {
        ...state,
        notificationCount: action.notificationCount,
      };
    case BANKIBAN:
      return {
        ...state,
        bankIban: action.bankIban,
      };
    case MODELS:
      return {
        ...state,
        firstModel: action.firstModel,
        lastModel: action.lastModel,
      };
    case PAYMENT:
      return {
        ...state,
        paymentMethods: action.paymentMethods,
      };
    default:
      return state;
  }
};
