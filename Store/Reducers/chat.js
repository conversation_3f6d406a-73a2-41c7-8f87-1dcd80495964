import { NEWMESSAGE } from "../../App";
import { CLE<PERSON>CHAT } from "../../src/screens/Chat";

const initialState = {
  message: {},
};

export default (state = initialState, action) => {
  switch (action.type) {
    case NEWMESSAGE:
      return {
        ...state,
        message: action.message,
      };
    case CLEARCHAT:
      return {
        ...state,
        message: {},
      };
    default:
      return state;
  }
};
