import org.apache.tools.ant.taskdefs.condition.Os

// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "31.0.0"
        minSdkVersion = 24
        compileSdkVersion = 31
        targetSdkVersion = 34
        googlePlayServicesVersion = '17.0.0'// default: “+”
        // googlePlayServicesVersion = '+'// default: “+”
        firebaseMessagingVersion = '21.1.0'
        firebaseVersion = '17.3.4' 
        kotlinVersion = '1.5.0'
        // ndkVersion = "20.1.5948944"
        androidXCore = "1.7.0"
        if (System.properties['os.arch'] == "aarch64") {
            // For M1 Users we need to use the NDK 24 which added support for aarch64
        ndkVersion = "24.0.8215888"
        } else {
            // Otherwise we default to the side-by-side NDK version from AGP.
            ndkVersion = "21.4.7075529"
        }
    }
    repositories {
        google()
        mavenCentral()
        jcenter()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.0.4")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("de.undercouch:gradle-download-task:4.1.2")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion") 
        classpath 'com.google.gms:google-services:4.3.13'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        exclusiveContent {
           filter {
               includeGroup "com.facebook.react"
           }
           forRepository {
               maven {
                   url "$rootDir/../node_modules/react-native/android"
               }
           }
       }
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        mavenCentral {
            // We don't want to fetch react-native from Maven Central as there are
            // older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }
        google()
        // mavenCentral()  // Maven Central repository
        maven { url 'https://www.jitpack.io' }
    }
    // configurations.all {
    //     resolutionStrategy {
    //     force 'com.appsflyer:af-android-sdk:6.10.2'
    //     }
    // } 
}
