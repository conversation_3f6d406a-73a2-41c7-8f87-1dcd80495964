package com.StepAll;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

public class WakeUpReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        Intent activityIntent = new Intent(context, MainActivity.class);
        activityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);

        // Start the activity to bring the app to the foreground
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            context.startActivity(activityIntent);
        } else {
            context.startActivity(activityIntent);
        }
    }
}
