package com.StepAll;


// import android.content.Context;
// import android.content.Intent;
// import com.facebook.react.bridge.ReactApplicationContext;
// import com.facebook.react.bridge.ReactContextBaseJavaModule;
// import com.facebook.react.bridge.ReactMethod;

// public class AppForegroundModule extends ReactContextBaseJavaModule {

//     public AppForegroundModule(ReactApplicationContext reactContext) {
//         super(reactContext);
//     }

//     @Override
//     public String getName() {
//         return "AppForegroundModule";
//     }

//     @ReactMethod
//     public void bringAppToForeground() {
//         Context context = getReactApplicationContext();
//         Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());

//         if (intent != null) {
//             // These flags will open the app if it is in the background
//             intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
//             context.startActivity(intent);
//         }
//     }
// }

// import android.content.Context;
// import android.content.Intent;
// import com.facebook.react.bridge.ReactApplicationContext;
// import com.facebook.react.bridge.ReactContextBaseJavaModule;
// import com.facebook.react.bridge.ReactMethod;

// public class AppForegroundModule extends ReactContextBaseJavaModule {

//     public AppForegroundModule(ReactApplicationContext reactContext) {
//         super(reactContext);
//     }

//     @Override
//     public String getName() {
//         return "AppForegroundModule";
//     }

//     @ReactMethod
//     public void startForegroundService() {
//         Context context = getReactApplicationContext();
//         Intent serviceIntent = new Intent(context, AppForegroundService.class);
//         serviceIntent.setAction("OPEN_APP");
//         context.startForegroundService(serviceIntent);
//     }
// }

import android.content.Context;
import android.content.Intent;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;

public class AppForegroundModule extends ReactContextBaseJavaModule {

    public AppForegroundModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }


    @Override
    public String getName() {
        return "AppForegroundModule";
    }

    @ReactMethod
    public void bringToForeground() {
        Context context = getReactApplicationContext();
        String packageName = context.getPackageName();
        Intent intent = context.getPackageManager().getLaunchIntentForPackage(packageName);

        if (intent != null) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            context.startActivity(intent);
        }
    }
}