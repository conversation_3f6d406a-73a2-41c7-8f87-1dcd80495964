package com.StepAll;

import android.app.Application;
import android.content.Context;
import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
// import com.reactlibrary.RNFloatingBubblePackage;
import com.jamesisaac.rnbackgroundtask.BackgroundTaskPackage;
import com.github.wumke.RNExitApp.RNExitAppPackage;
import com.zmxv.RNSound.RNSoundPackage;
import com.RNFetchBlob.RNFetchBlobPackage;
import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.config.ReactFeatureFlags;
import com.facebook.soloader.SoLoader;
import com.StepAll.newarchitecture.MainApplicationReactNativeHost;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage;
// import com.oblador.vectoricons.VectorIconsPackage;
// import com.StepAll.AppForegroundPackage;
// import com.StepAll.AppForegroundModule;
import com.StepAll.WakeUpPackage;
import com.StepAll.ForegroundServicePackage;
import com.StepAll.NotificationHelperPackage;
import com.StepAll.BringToForegroundPackage;
import com.appsflyer.AppsFlyerLib;


public class MainApplication extends Application implements ReactApplication {

  private final ReactNativeHost mReactNativeHost =
      new ReactNativeHost(this) {
        @Override
        public boolean getUseDeveloperSupport() {
          return BuildConfig.DEBUG;
        }

        @Override
        protected List<ReactPackage> getPackages() {
          @SuppressWarnings("UnnecessaryLocalVariable")
          List<ReactPackage> packages = new PackageList(this).getPackages();
          // Packages that cannot be autolinked yet can be added manually here, for example:
          // packages.add(new MyReactNativePackage());
          // packages.add(new ReactPackage());
          // packages.add(new AppForegroundPackage());
          packages.add(new WakeUpPackage());
          packages.add(new ForegroundServicePackage());
          packages.add(new NotificationHelperPackage());
          packages.add(new BringToForegroundPackage());
          // packages.add(new RNFloatingBubblePackage());
          // packages.add(new AppForegroundModule());
          // packages.add(new VectorIconsPackage());
          // packages.add(new RNAudioRecorderPlayerPackage());
            // new MainReactPackage(),
            // new RNFloatingBubblePackage();
        // new AppForegroundPackage(); 
          return packages;
        }

        @Override
        protected String getJSMainModuleName() {
          return "index";
        }
      };

  private final ReactNativeHost mNewArchitectureNativeHost =
      new MainApplicationReactNativeHost(this);

  @Override
  public ReactNativeHost getReactNativeHost() {
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      return mNewArchitectureNativeHost;
    } else {
      return mReactNativeHost;
    }
  }

  @Override
  public void onCreate() {
    super.onCreate();
    // If you opted-in for the New Architecture, we enable the TurboModule system
    ReactFeatureFlags.useTurboModules = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
    SoLoader.init(this, /* native exopackage */ false);
    // Initialize AppsFlyer with your dev key
    String afDevKey = "LJxrXqHE82GrmWngVM7nSQ"; // replace with your actual dev key
    AppsFlyerLib.getInstance().init(afDevKey, null, this);
    AppsFlyerLib.getInstance().setDebugLog(true); // optional for debugging
    AppsFlyerLib.getInstance().start(this);
    // initializeFlipper(this, getReactNativeHost().getReactInstanceManager());
    BackgroundTaskPackage.useContext(this);
  }

  /**
   * Loads Flipper in React Native templates. Call this in the onCreate method with something like
   * initializeFlipper(this, getReactNativeHost().getReactInstanceManager());
   *
   * @param context
   * @param reactInstanceManager
   */
  private static void initializeFlipper(
      Context context, ReactInstanceManager reactInstanceManager) {
    if (BuildConfig.DEBUG) {
      try {
        /*
         We use reflection here to pick up the class that initializes Flipper,
        since Flipper library is not available in release mode
        */
        Class<?> aClass = Class.forName("com.StepAll.ReactNativeFlipper");
        aClass
            .getMethod("initializeFlipper", Context.class, ReactInstanceManager.class)
            .invoke(null, context, reactInstanceManager);
      } catch (ClassNotFoundException e) {
        e.printStackTrace();
      } catch (NoSuchMethodException e) {
        e.printStackTrace();
      } catch (IllegalAccessException e) {
        e.printStackTrace();
      } catch (InvocationTargetException e) {
        e.printStackTrace();
      }
    }
  }
}
