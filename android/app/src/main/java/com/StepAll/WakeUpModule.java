package com.StepAll;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;

public class WakeUpModule extends ReactContextBaseJavaModule {
    private static ReactApplicationContext reactContext;

    WakeUpModule(ReactApplicationContext context) {
        super(context);
        reactContext = context;
    }

    @Override
    public String getName() {
        return "WakeUpModule";
    }

    @ReactMethod
    public void bringAppToForeground() {
        Context context = getReactApplicationContext();
        Intent intent = new Intent(context, WakeUpReceiver.class);

        PendingIntent pendingIntent = PendingIntent.getBroadcast(
                context, 
                0, 
                intent, 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        try {
            pendingIntent.send();
        } catch (PendingIntent.CanceledException e) {
            e.printStackTrace();
        }
    }
}

