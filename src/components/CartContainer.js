import {
  View,
  StyleSheet,
  Image,
  Text,
  I18nManager,
  Modal,
  Platform,
} from 'react-native';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  Green,
  mSize,
  Blue,
  Red,
  Black,
  appColor1,
  MediumGrey,
  WhiteGery,
  DarkGrey,
} from './Styles';
import React, { useEffect, useState } from 'react';
import { Button } from 'native-base';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { strings } from '../screens/i18n';
import Toaster from './Toaster';
import { useDispatch, useSelector } from 'react-redux';
import AdContainerLoading from './AdContainerLoading';

const CartContainer = (props) => {
  const [fav, setFav] = useState(true);
  const [loading, setLoading] = useState(false);
  const token = useSelector(state => state.auth.token);
  const [quantity, setQuantity] = useState(0);

  const dispatch = useDispatch();



  useEffect(() => {
    const getInitialState = async () => {
      setLoading(true);
      setQuantity(props.item.quantity)
      setLoading(false);
    };

    getInitialState();
  }, [props]);

  const toggleFav = async () => {
    setLoading(true);
    try {
      let response = await dispatch(equpmentAction.toggleFav(props.item.id));

      if (response.status == 200) {
        setFav(!fav)
      }
      else {
        if (response.message) {
          Toaster(
            'top',
            'danger',
            Red,
            response.message,
            White,
            1500,
            screenHeight / 50,
          );
        }
      }
      setLoading(false);
    }
    catch (err) {
      setLoading(false);
    }
  };


  return (
    <View style={styles.container}>


      <View style={styles.imagecontainer} >
        <Image source={props.item.image} style={styles.image} />
      </View>

      <View style={{ flex: 1, paddingVertical: '2%', paddingStart: '2%' }}>
        <Text style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-start' }}>{props.item.name}</Text>
        <Text style={{ fontFamily: appFontBold, color: appColor1, fontSize: screenWidth / 28, alignSelf: 'flex-start' }}>200<Text style={{ fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 28, }}>  AED</Text> <Text style={{ fontFamily: appFontBold, color: MediumGrey, fontSize: screenWidth / 35, }}>  include VAT</Text></Text>
        <View style={{ flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignItems: 'center', alignSelf: 'flex-start' }}>
          <View style={{ width: 15, height: 15, borderRadius: 30, backgroundColor: props.item.color }} ></View>
          <Text style={{ fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 28, alignSelf: 'flex-start' }}> - {props.item.size}</Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
          <View onPress={() => { setQuantity(quantity + 1) }} transparent style={{ flexDirection: 'row', width: '50%', height: 30, alignSelf: 'center', justifyContent: 'space-between', paddingHorizontal: '2%', alignItems: 'center', backgroundColor: WhiteGery, borderRadius: 25, marginVertical: screenHeight / 50 }}>
            <Button onPress={() => { if (quantity == 1) { props.deletePress } else { setQuantity(quantity - 1) } }} transparent style={{ width: 20, height: 20, alignSelf: 'center', justifyContent: 'center', alignItems: 'center', backgroundColor: appColor1, borderRadius: 40, }}>
              <Image source={require('../images/minus.png')} style={{ width: '100%', height: '100%', marginEnd: '2%', resizeMode: 'contain', tintColor: White, }} />
            </Button>
            <Text style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 25, }}>{quantity}</Text>
            <Button onPress={() => { setQuantity(quantity + 1) }} transparent style={{ width: 20, height: 20, alignSelf: 'center', justifyContent: 'center', alignItems: 'center', backgroundColor: appColor1, borderRadius: 40, }}>
              <Image source={require('../images/plus.png')} style={{ width: '150%', height: '150%', marginEnd: '2%', resizeMode: 'contain', tintColor: White, }} />
            </Button>
          </View>
          <TouchableOpacity onPress={props.deletePress} style={{ width: screenWidth / 9, height: 30, backgroundColor: appColor1, alignItems: 'center', justifyContent: 'center', borderTopStartRadius: 15, borderBottomStartRadius: 15 }}>
            <Image source={require('../images/trash.png')} style={{ width: '70%', height: '70%', marginEnd: '2%', resizeMode: 'contain', tintColor: White, }} />
          </TouchableOpacity>
        </View>
      </View>



    </View>
  );
};

export default CartContainer;
const styles = StyleSheet.create({
  container: {
    backgroundColor: White,
    width: screenWidth / 1.1,
    // height: 220,
    flexDirection: 'row',
    overflow: 'hidden',
    // marginEnd: screenWidth / 30,
    alignSelf: 'center',
    marginBottom: screenHeight / 100,
    borderWidth: .8, borderColor: MediumGrey, borderRadius: 2,
  },
  imagecontainer: {
    height: 150,
    width: '30%',
  },
  image: {
    resizeMode: 'contain',
    height: '100%',
    width: '100%',
  },
  title: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 32,
    alignSelf: 'flex-start',
    color: Black,
  },
  title1: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 32,
    alignSelf: 'flex-start',
    color: appColor1,
  },
  title2: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 32,
    alignSelf: 'flex-start',
    color: DarkGrey,
  },

});
