import React, { Component, useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager } from 'react-native';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  DarkGrey,
  Red,
  Green,
  DarkRed,
  DarkGreen,
  WhiteYellow,
  Grey,
  Black,
  appColor,
  Blue,
  MediumGrey,
  WhiteGery,
  appColor1,
} from './Styles';
import { strings } from '../screens/i18n';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
const OrdersContainer = props => {
  const [active, setActive] = useState(true);
  const [lan, setLan] = useState('');

  return (
    <Pressable onPress={props.onPress} style={styles.container}>

      <View style={{ flexDirection: 'row', justifyContent: 'space-between', overflow: 'hidden', paddingHorizontal: '3%' }}>
        <View style={{ width: '70%', alignItems: 'center', alignSelf: 'center', justifyContent: 'center', height: screenHeight / 30 }}>
          <Text
            style={{
              fontFamily: appFontBold,
              color: Black,
              fontSize: screenWidth / 30,
              alignSelf: 'flex-start',
              textAlign: I18nManager.isRTL ? 'right' : 'left',
            }}
            numberOfLines={3}
          >
            {strings('lang.OrderNumber')} {'  '}
            <Text
              style={{
                fontFamily: appFontBold,
                color: DarkGrey,
                fontSize: screenWidth / 37,
                textAlign: I18nManager.isRTL ? 'right' : 'left',
              }}
              numberOfLines={3}
            >
              {props.item.id}
            </Text>
          </Text>
        </View>
        <View style={{ width: '30%', alignItems: 'center', }}>
          {/* <Text
            style={{
              fontFamily: appFontBold,
              color: Black,
              fontSize: screenWidth / 30,
              alignSelf: 'flex-end',
              textAlign: I18nManager.isRTL ? 'right' : 'left',
            }}
            numberOfLines={3}
          >
            {'VIP'}

          </Text> */}
        </View>
      </View>

      <View style={{ width: '100%', alignItems: 'center', justifyContent: 'center', marginTop: '1%', paddingHorizontal: '3%', height: screenHeight / 30 }}>
        <Text
          style={{
            fontFamily: appFontBold,
            color: Black,
            fontSize: screenWidth / 30,
            alignSelf: 'flex-start',
            textAlign: I18nManager.isRTL ? 'right' : 'left',
          }}
          numberOfLines={3}
        >
          {strings('lang.Date')} {'  '}
          <Text
            style={{
              fontFamily: appFontBold,
              color: DarkGrey,
              fontSize: screenWidth / 37,
              textAlign: I18nManager.isRTL ? 'right' : 'left',
            }}
            numberOfLines={3}
          >
            {props.item.date ? props.item.date : props.item.created_at_formatted}
          </Text>
        </Text>
      </View>

      <View style={styles.bottomContainer}>
        <Text
          style={{
            fontFamily: appFontBold,
            color: Black,
            fontSize: screenWidth / 30,
            textAlign: I18nManager.isRTL ? 'right' : 'left',
          }}
          numberOfLines={3}
        >
          {strings('lang.StatusOfRequest')}
        </Text>

        <Text
          style={{
            fontFamily: appFontBold,
            color: 'green',
            fontSize: screenWidth / 30,
            textAlign: I18nManager.isRTL ? 'right' : 'left',
          }}
          numberOfLines={3}
        >
          {props.item.status_text}
        </Text>
      </View>

    </Pressable>
  );
};

export default OrdersContainer;
const styles = StyleSheet.create({
  container: {
    backgroundColor: White,
    width: '95%',
    height: screenHeight / 8,
    alignSelf: 'center',
    borderWidth: .8,
    borderRadius: 10,
    borderColor: MediumGrey,
    alignItems: 'center',
    marginTop: screenHeight / 80,
    overflow: 'hidden',
    paddingTop: screenHeight / 120
  },
  bottomContainer: {
    backgroundColor: WhiteGery,
    width: '100%',
    height: screenHeight / 25,
    alignSelf: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: '3%',
    alignItems: 'center',
    position: 'absolute',
    bottom: '0%',
    flexDirection: 'row'
  },

});
