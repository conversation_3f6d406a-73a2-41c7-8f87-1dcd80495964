
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Button } from 'native-base';
import React, { useEffect } from 'react';
import { Text, View, Image, StyleSheet, I18nManager, ImageBackground, Platform, ActivityIndicator } from 'react-native'
import { TouchableOpacity } from 'react-native-gesture-handler';

import { appColor1, appFont, appFontBold, MediumGrey, Red, screenHeight, screenWidth, White } from '../components/Styles'
import { strings } from '../screens/i18n';


const AdContainerLoading = props => {

    return (
        <View style={{ backgroundColor: MediumGrey, opacity: .8, position: "absolute", zIndex: 10000, alignSelf: 'center', alignItems: 'center', width: screenWidth / 2.3, height: 220, }}>
            <ActivityIndicator size={100} color={appColor1} style={{ marginTop: screenHeight / 15 }} />
        </View>
    );
};

const styles = StyleSheet.create({
});

export default AdContainerLoading;
