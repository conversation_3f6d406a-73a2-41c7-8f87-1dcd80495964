import {
  StyleSheet,
  Dimensions,
  Platform,
  StatusBar,
  I18nManager,
} from 'react-native';

export const { height: screenHeight, width: screenWidth } = Dimensions.get(
  'window',
);
export const appFontBold = 'Cairo-Bold';
export const appFontMedium = 'Cairo-Medium';
export const appFont = 'Cairo-Bold';

export const WhiteGery = '#F5F5F5';
export const MediumGrey = '#cecdcd';
export const DarkGrey = '#707070';
export const Grey = '#dae8dc';

export const WhiteYellow = '#FFE88F';
export const MediumYellow = '#ffc107';
export const DarkYellow = '#f0c91f';
export const Orange = '#ff8502';

export const White = '#ffffff';
export const Black = '#000000';

export const Green = '#bed65c';
export const MediumGreen = '#18943c';
export const Green2 = '#56AA3B';
export const LightGreen = '#DAECD4';
export const WhiteGreen = '#DAECD4';
export const DarkGreen = '#006442';

export const Red = '#c20404';
export const WhiteRed = '#F9E5E5';
export const MediumRed = '#E0443A';

export const Gold = '#F3C84A';
export const DarkRed = '#C41D24';

export const WhiteBlue = '#CBF0E4';
export const Blue = '#1db5d0';
export const DarkBlue = '#0c2c78';

export const appColor1 = '#D92806';
export const appColor2 = '#00aeef';

const styles = StyleSheet.create({});

export default styles;
