import { Button } from 'native-base';
import React, { Component, useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager } from 'react-native';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import { strings } from '../screens/i18n';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  DarkGrey,
  Red,
  Green,
  DarkRed,
  DarkGreen,
  WhiteYellow,
  Grey,
  Black,
  appColor,
  Blue,
  MediumGrey,
  WhiteGery,
  appColor1,
} from './Styles';
const OrderContainer = props => {
  const [active, setActive] = useState(true);
  const [lan, setLan] = useState('');

  return (
    <View style={styles.container}>
      <View style={{ flexDirection: 'row', paddingHorizontal: '2%' }}>
        <View style={{ width: '70%', flexDirection: 'column' }}>
          <View style={styles.rowContainer}>
            <Image source={require('../images/3333.png')} style={styles.icon1} />
            <Text numberOfLines={1} style={styles.rowText}>2252</Text>
          </View>
          <View style={styles.rowContainer}>
            <Image source={require('../images/svgexport-6.png')} style={styles.icon1} />
            <Text numberOfLines={1} style={styles.rowText}>22 OCTOBER 2023</Text>
          </View>
          <View style={styles.rowContainer}>
            <Image source={require('../images/surface-6.png')} style={styles.icon} />
            <Text numberOfLines={1} style={styles.rowText}>Confirmed order</Text>
          </View>
        </View>
        <Button onPress={props.detailsPress} transparent style={{ width: '30%', height: 40, borderRadius: 20, backgroundColor: appColor1, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
          <Text
            style={{
              fontFamily: appFontBold,
              color: White,
              fontSize: screenWidth / 30,
            }}
            numberOfLines={3}
          >
            {strings('lang.Details').toUpperCase()}
          </Text>
        </Button>
      </View>
    </View>
  );
};

export default OrderContainer;
const styles = StyleSheet.create({
  container: {
    backgroundColor: White,
    width: '95%',
    height: 100,
    alignSelf: 'center',
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: MediumGrey,
    alignItems: 'center',
    borderRadius: 5,
    marginVertical: '2.5%'
  },
  containerActive: {
    backgroundColor: WhiteYellow,
    width: '100%',
    minHeight: screenWidth / 5,
    maxHeight: screenWidth / 3.5,
    alignSelf: 'center',
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderColor: MediumGrey,
    alignItems: 'center'
  },
  modal: {
    flex: 1,
    alignItems: 'center',
    width: screenWidth / 1.3,
    height: screenHeight / 3,
    position: 'absolute',
    top: screenHeight / 3.2,
    alignSelf: 'center',
    backgroundColor: White,
    borderRadius: 10,
  },
  text: {
    fontFamily: appFontBold,
    color: DarkBlue,
    fontSize: screenWidth / 22,
    marginTop: '4%',
  },
  buttonContainer: {
    width: screenWidth / 4,
    height: '65%',
    backgroundColor: DarkBlue,
    alignSelf: 'center',
    marginHorizontal: 5,
    justifyContent: 'center',
    borderRadius: 10,
    alignItems: 'center',
    padding: 12,
    marginTop: 7,
  },
  buttonContainer2: {
    width: screenWidth / 4,
    height: '65%',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: DarkBlue,
    alignSelf: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
    borderRadius: 10,
    alignItems: 'center',
    padding: 12,
    // marginTop: 10,
  },
  buttonText: {
    color: White,
    fontFamily: appFontBold,
    fontSize: screenWidth / 28,
  },
  buttonText2: {
    color: DarkBlue,
    marginVertical: 5,
    alignSelf: 'center',
    fontSize: screenWidth / 25,
    fontFamily: 'Cairo-Bold',
  },
  icon: { resizeMode: 'contain', width: '10%', height: '80%', marginHorizontal: '2%' },
  icon1: { resizeMode: 'contain', width: '10%', height: '60%', marginHorizontal: '2%' },
  rowContainer: { flexDirection: 'row', alignItems: 'center' },
  rowText: { color: DarkGrey, fontFamily: appFontBold, fontSize: screenWidth / 30, marginStart: '2%' },
  modelIconContainer: { width: '15%', height: '100%', justifyContent: 'center' },
  modelTextContainer: { width: '85%', height: '100%', justifyContent: 'center' },
  modelText: {
    fontFamily: appFontBold,
    color: DarkBlue,
    fontSize: screenWidth / 25,
  },
});
