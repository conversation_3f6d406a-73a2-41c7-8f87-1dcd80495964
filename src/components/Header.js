import {
  View,
  StyleSheet,
  Image,
  Text,
  I18nManager,
  Modal,
  Platform,
} from 'react-native';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  Green,
  mSize,
  Blue,
  Red,
  Black,
  appColor1,
  MediumGreen,
  DarkYellow,
  WhiteGery,
} from './Styles';
import React from 'react';
import { Button } from 'native-base';
import { useSelector } from 'react-redux';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { strings } from '../screens/i18n';

export default function Header(props) {
  const token = useSelector(state => state.auth.token);

  return (
    <View style={[styles.headercontainer, props.background ? { backgroundColor: WhiteGery } : props.lohin ? { backgroundColor: null } : {}]}>
      <View
        style={{ width: '10%', alignItems: 'center', justifyContent: 'center', }}
      >
        <Button transparent onPress={props.backPress} style={styles.backButton}>
          <Image
            source={require('../images/arrow-left.png')}
            style={styles.backImage}
          />
        </Button>
      </View>
      <View
        style={{ width: props.HideMore ? '60%' : '80%', justifyContent: 'center', paddingStart: '1.5%', }}
      >
        <Text
          style={{
            fontFamily: appFontBold,
            color: Black,
            fontSize: screenWidth / 20,
            textAlignVertical: 'center',
            alignSelf: 'flex-start',
          }}
        >
          {props.title.toUpperCase()}
        </Text>
      </View>
      {props.HideMore
        ?
        <Button onPress={props.cancelPress}
          transparent
          style={{ width: '30%', justifyContent: 'flex-end', paddingStart: '1.5%', flexDirection: 'row', alignItems: 'center', alignSelf: 'center', height: screenHeight / 18 }}
        >
          <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: appColor1 }}>{strings('lang.cancel')}</Text>
        </Button>
        :
        <View
          style={{ width: '30%', justifyContent: 'flex-end', paddingStart: '1.5%', flexDirection: 'row' }}
        >

        </View>
      }





    </View>
  );
}
const styles = StyleSheet.create({
  headercontainer: {
    backgroundColor: White,
    height: Platform.OS == 'ios' ? screenHeight / 10 : screenHeight / 13,
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingHorizontal: '2.5%',
    marginTop: Platform.OS == 'ios' ? 0 : 0,
    width: screenWidth,
    alignItems: 'center',
    paddingTop: Platform.OS == 'ios' ? screenHeight / 25 : 0
  },
  backImage: {
    width: '75%',
    height: '75%',
    tintColor: DarkYellow,
    resizeMode: 'contain',
    alignSelf: 'center',
    transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }]
  },
  backButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: screenHeight / 18
  },
  redContainer: {
    width: screenWidth / 20,
    height: screenWidth / 20,
    overflow: 'hidden',
    backgroundColor: Red,
    borderRadius: screenWidth / 40,
    position: 'absolute',
    top: '20%',
    borderWidth: 2,
    borderColor: White,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textWhite: {
    fontFamily: appFontBold,
    color: White,
    fontSize: screenWidth / 45,
  },
});
