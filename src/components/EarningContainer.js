import { Button } from 'native-base';
import React, { Component, useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager } from 'react-native';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import { strings } from '../screens/i18n';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  DarkGrey,
  Red,
  Green,
  DarkRed,
  DarkGreen,
  WhiteYellow,
  Grey,
  Black,
  appColor,
  Blue,
  MediumGrey,
  WhiteGery,
  appColor1,
  MediumGreen,
} from './Styles';
import { useEffect } from 'react';
const EarningContainer = props => {

  useEffect(() => {

  }, [])

  return (
    <View style={styles.users}>
      <View style={{ alignItems: 'center', justifyContent: 'flex-start', width: '100%', }}>
        <View style={{ marginBottom: '3%', alignItems: 'center', justifyContent: 'space-between', flexDirection: 'row', width: '100%' }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start', }}>
            <Image source={require('../images/Group-39.png')} style={{ resizeMode: 'contain', width: '20%', marginHorizontal: '5%', height: screenHeight / 30 }} />
            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black, }}>{props.item.user.name}</Text>
          </View>
          <Text style={{ fontFamily: appFont, fontSize: screenWidth / 36, color: Black, marginEnd: '3%' }}>{props.item.created_at.slice(0, 10)}</Text>
        </View>

        {/* <View style={{ flexDirection: 'row', width: '95%', alignItems: 'center', justifyContent: 'space-between' }}>
          <View style={{ flexDirection: 'row', width: '50%', alignItems: 'center', }}>
            <Image source={require('../images/bluelocation.png')} style={{ resizeMode: 'contain', width: '15%', marginHorizontal: '5%', height: screenHeight / 40 }} />
            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 35, color: Black, }}>{'موقعك الحالى'}</Text>
          </View>
          <View style={{ flexDirection: 'row', width: '50%', alignItems: 'center', }}>
            <Image source={require('../images/yallowlocation.png')} style={{ resizeMode: 'contain', width: '15%', marginHorizontal: '5%', height: screenHeight / 40 }} />
            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 35, color: Black, }}>{'مسجد قباء'}</Text>
          </View>
        </View> */}

        {/* <View style={{ width: '47%', height: 1, borderWidth: 1, borderColor: MediumGrey, borderStyle: 'dashed', alignSelf: 'flex-start', marginStart: '9%' }}></View> */}

        <View style={{ alignItems: 'center', justifyContent: 'space-between', flexDirection: 'row', width: '95%' }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start', marginStart: '4%' }}>
            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 35, color: Black, }}>{strings('lang.thedemandprice')} {'  '}
              <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 26, color: MediumGreen, }}>{props.item.amount} {strings('lang.SR')}</Text>
            </Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start', }}>
            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 35, color: Black, }}>{strings('lang.Companycommission')} {'  '}
              <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 26, color: Red, }}>{props.item.commission} {strings('lang.SR')}</Text>
            </Text>
          </View>
        </View>
        <View style={{ alignItems: 'center', justifyContent: 'flex-end', flexDirection: 'row', width: '95%' }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start', marginStart: '4%' }}>
            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 35, color: Black, }}>{strings('lang.totalProfit')} {'  '}
              <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 26, color: MediumGreen, }}>{JSON.parse(props.item.amount) - JSON.parse(props.item.commission)} {strings('lang.SR')}</Text>
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default EarningContainer;
const styles = StyleSheet.create({
  users: {
    alignItems: 'center',
    alignSelf: 'center',
    justifyContent: 'space-between',
    width: '90%',
    maxHeight: screenHeight / 7,
    marginVertical: '2%',
    borderWidth: 1,
    borderColor: MediumGrey,
    borderRadius: 5,
    padding: '3%'
  },
});
