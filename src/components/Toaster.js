import { Toast } from 'native-base';
import { appFont } from './Styles';

const Toaster = (position, type, backgroundColor, text, textColor, duration,marginTop) => {
    Toast.show({
        position: position, type: type, style: { width: '90%', alignSelf: 'center', borderRadius: 10, opacity: 0.85, backgroundColor: backgroundColor,marginTop:marginTop },
        text: text, textStyle: { color: textColor, textAlign: 'center', fontFamily: appFont },
        duration: duration
    })
};

export default Toaster;
