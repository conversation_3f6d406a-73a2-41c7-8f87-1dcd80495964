import {
    Image,
    View,
    Text,
    StyleSheet,
    I18nManager,
    BackHandler,
    TouchableOpacity,
    Keyboard,
    Platform,
    ImageBackground,
} from 'react-native';
import React, { Component, useEffect, useState } from 'react';
import {
    screenHeight,
    DarkBlue,
    screenWidth,
    DarkGrey,
    White,
    appFont,
    WhiteGery,
    appFontMedium,
    MediumGrey,
    appFontBold,
    DarkGreen,
    Red,
    Green,
    appColor2,
    appColor1,
    Black,
    WhiteGreen,
    DarkYellow,
    MediumGreen,
} from '../components/Styles';
import { Input, Container, Button, Toast, Drawer } from 'native-base';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { TextInput } from 'react-native-gesture-handler';
import { strings } from './i18n';

import * as authActions from '../../Store/Actions/auth';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Loading from '../components/Loading';
import Header from '../components/Header';
import RangeSlider from 'rn-range-slider';
import { Slider } from '@miblanchard/react-native-slider';
import {
    createDrawerNavigator,
    DrawerContentScrollView,
    DrawerItemList,
    DrawerItem,
} from '@react-navigation/drawer';
import { NavigationContainer } from '@react-navigation/native';


const AppStack = props => {


    // const Feed = ({ navigation }) => {
    //     return (
    //         <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    //             <Text>Feed Screen</Text>
    //             <Button title="Open drawer" onPress={() => navigation.openDrawer()} />
    //             <Button title="Toggle drawer" onPress={() => navigation.toggleDrawer()} />
    //         </View>
    //     );
    // }

    // const Notifications = () => {
    //     return (
    //         <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    //             <Text>Notifications Screen</Text>
    //         </View>
    //     );
    // }

    // const CustomDrawerContent = (props) => {
    //     return (
    //         <DrawerContentScrollView {...props}>
    //             <DrawerItemList {...props} />
    //             <DrawerItem
    //                 label="Close drawer"
    //                 onPress={() => props.navigation.closeDrawer()}
    //             />
    //             <DrawerItem
    //                 label="Toggle drawer"
    //                 onPress={() => props.navigation.toggleDrawer()}
    //             />
    //         </DrawerContentScrollView>
    //     );
    // }

    // const Drawer = createDrawerNavigator();

    // const MyDrawer = () => {
    //     return (
    //         <Drawer.Navigator
    //             useLegacyImplementation
    //             drawerContent={(props) => <CustomDrawerContent {...props} />}
    //         >
    //             <Drawer.Screen name="Feed" component={Feed} />
    //             <Drawer.Screen name="Notifications" component={Notifications} />
    //         </Drawer.Navigator>
    //     );
    // }


    return (
        <View></View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 36,
        justifyContent: 'flex-end'
    },
    input: {
        padding: 10,
        borderWidth: 0.5,
        borderRadius: 4
    },
    status: {
        padding: 10,
        textAlign: "center"
    }
});

export default AppStack;
