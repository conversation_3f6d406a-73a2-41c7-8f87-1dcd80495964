import React, { Component, useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager, Button } from 'react-native';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  DarkGrey,
  Red,
  Green,
  DarkRed,
  DarkGreen,
  WhiteYellow,
  Grey,
  Black,
  appColor,
  Blue,
  MediumGrey,
  WhiteGery,
  appColor1,
} from './Styles';
import { strings } from '../screens/i18n';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';


const VipTrip = props => {
  const [active, setActive] = useState(true);
  const [lan, setLan] = useState('');

  return (
    <TouchableWithoutFeedback onPress={props.tripPress} style={styles.container}>

      <View style={{ flexDirection: 'row', justifyContent: 'space-between', overflow: 'hidden', paddingHorizontal: '3%' }}>
        <View style={{ width: '100%', alignItems: 'center', flexDirection: 'row', alignSelf: 'center', justifyContent: 'flex-start' }}>
          <Image source={require('../images/222.png')} style={{ width: '10%', height: screenHeight / 50, resizeMode: 'contain', }} />
          <Text
            style={{
              fontFamily: appFontBold,
              color: Black,
              fontSize: screenWidth / 33,
              alignSelf: 'flex-start',
              textAlign: I18nManager.isRTL ? 'right' : 'left',
            }}
            numberOfLines={3}
          >
            {props.item.time}   {' '}
            <Text
              style={{
                fontFamily: appFontBold,
                color: Black,
                fontSize: screenWidth / 33,
                textAlign: I18nManager.isRTL ? 'right' : 'left',
              }}
              numberOfLines={3}
            >
              {props.item.date}
            </Text>
          </Text>
        </View>

      </View>

      <View style={{
        width: '100%', alignItems: 'flex-start',
        flexDirection: 'row', alignSelf: 'flex-start', justifyContent: 'flex-start',
        paddingHorizontal: '3%', marginVertical: '.5%', minHeight: screenHeight / 20
      }}>
        <Image source={require('../images/bluelocation.png')} style={{ width: '8%', marginTop: 5, height: screenHeight / 40, resizeMode: 'contain', }} />
        <Text
          style={{
            fontFamily: appFontBold,
            color: Black,
            fontSize: screenWidth / 33,
            alignSelf: 'flex-start',
            textAlign: I18nManager.isRTL ? 'left' : 'right',
          }}
        >
          {props.item.from_address_id ? props.item.address_from.address : props.item.from_address}
        </Text>
      </View>
      <View style={{ justifyContent: 'space-between', width: '100%' }}>
        <View style={{
          width: '95%',
          alignItems: 'flex-start', flexDirection: 'row', alignSelf: 'flex-start',
          justifyContent: 'flex-start', paddingHorizontal: '3%', minHeight: screenHeight / 15
        }}>
          <Image source={require('../images/yallowlocation.png')} style={{ width: '8%', marginTop: 5, height: screenHeight / 40, resizeMode: 'contain', }} />
          <Text
            style={{
              fontFamily: appFontBold,
              color: Black,
              fontSize: screenWidth / 33,
              alignSelf: 'flex-start',
              textAlign: I18nManager.isRTL ? 'left' : 'right',
            }}
          >
            {props.item.to_address_id ? props.item.address_to.address : props.item.to_address}
          </Text>
        </View>

        <View style={styles.bottomContainer}>
          <Text
            style={{
              fontFamily: appFontBold,
              color: Black,
              fontSize: screenWidth / 33,
              textAlign: I18nManager.isRTL ? 'right' : 'left',
            }}
            numberOfLines={3}
          >
            {strings('lang.StatusOfRequest')}
          </Text>

          <Text
            style={{
              fontFamily: appFontBold,
              color: 'green',
              fontSize: screenWidth / 33,
              textAlign: I18nManager.isRTL ? 'right' : 'left',
            }}
            numberOfLines={3}
          >
            {props.item.status_text}
          </Text>
        </View>
      </View>


    </TouchableWithoutFeedback>
  );
};

export default VipTrip;
const styles = StyleSheet.create({
  container: {
    backgroundColor: White,
    width: '95%',
    minHeight: screenHeight / 7,
    alignSelf: 'center',
    borderWidth: .8,
    borderRadius: 10,
    borderColor: MediumGrey,
    alignItems: 'center',
    marginTop: screenHeight / 80,
    overflow: 'hidden',
    paddingTop: screenHeight / 100
  },
  bottomContainer: {
    backgroundColor: WhiteGery,
    width: '100%',
    height: screenHeight / 25,
    alignSelf: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: '3%',
    alignItems: 'center',
    // position: 'absolute',
    // bottom: '0%',
    flexDirection: 'row'
  },

});
