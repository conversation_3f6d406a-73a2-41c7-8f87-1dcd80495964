import React, { Component, useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager, Button } from 'react-native';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  DarkGrey,
  Red,
  Green,
  DarkRed,
  DarkGreen,
  WhiteYellow,
  Grey,
  Black,
  appColor,
  Blue,
  MediumGrey,
  WhiteGery,
  appColor1,
} from './Styles';
import { strings } from '../screens/i18n';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';


const TripsContainer = props => {
  const [active, setActive] = useState(true);
  const [lan, setLan] = useState('');

  return (
    <TouchableWithoutFeedback onPress={props.RBSheetPrees} style={styles.container}>
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', overflow: 'hidden', paddingHorizontal: '3%' }}>
        <View style={{ width: '70%', alignItems: 'center', flexDirection: 'row', alignSelf: 'center', justifyContent: 'flex-start' }}>
          <Image source={require('../images/222.png')} style={{ width: '10%', height: 12, resizeMode: 'contain', }} />
          <Text
            style={{
              fontFamily: appFontBold,
              color: Black,
              fontSize: screenWidth / 33,
              alignSelf: 'flex-start',
              textAlign: I18nManager.isRTL ? 'right' : 'left',
            }}
            numberOfLines={3}
          >
            {'12 اكتوبر 2022'}   {'  '}
            <Text
              style={{
                fontFamily: appFontBold,
                color: Black,
                fontSize: screenWidth / 33,
                textAlign: I18nManager.isRTL ? 'right' : 'left',
              }}
              numberOfLines={3}
            >
              {' 11:32 ص'}
            </Text>
          </Text>
        </View>

        <View style={{ width: '30%', alignItems: 'center', }}>
          <Text
            style={{
              fontFamily: appFontBold,
              color: Black,
              fontSize: screenWidth / 33,
              alignSelf: 'flex-end',
              textAlign: I18nManager.isRTL ? 'right' : 'left',
            }}
            numberOfLines={3}
          >
            {'بين المدن'}

          </Text>
        </View>

      </View>

      <View style={{ width: '70%', alignItems: 'center', flexDirection: 'row', alignSelf: 'flex-start', justifyContent: 'flex-start', paddingHorizontal: '3%' }}>
        <Image source={require('../images/bluelocation.png')} style={{ width: '10%', height: 12, resizeMode: 'contain', }} />
        <Text
          style={{
            fontFamily: appFontBold,
            color: Black,
            fontSize: screenWidth / 33,
            alignSelf: 'flex-start',
            textAlign: I18nManager.isRTL ? 'right' : 'left',
          }}
          numberOfLines={3}
        >
          {'موقعك الحالى'}
        </Text>
      </View>

      <View style={{ width: '70%', alignItems: 'center', flexDirection: 'row', alignSelf: 'flex-start', justifyContent: 'flex-start', paddingHorizontal: '3%' }}>
        <Image source={require('../images/yallowlocation.png')} style={{ width: '10%', height: 12, resizeMode: 'contain', }} />
        <Text
          style={{
            fontFamily: appFontBold,
            color: Black,
            fontSize: screenWidth / 33,
            alignSelf: 'flex-start',
            textAlign: I18nManager.isRTL ? 'right' : 'left',
          }}
          numberOfLines={3}
        >
          {'مسجد قباء'}
        </Text>
      </View>

      <View style={styles.bottomContainer}>
        <Text
          style={{
            fontFamily: appFontBold,
            color: Black,
            fontSize: screenWidth / 33,
            textAlign: I18nManager.isRTL ? 'right' : 'left',
          }}
          numberOfLines={3}
        >
          {strings('lang.StatusOfRequest')}
        </Text>

        <Text
          style={{
            fontFamily: appFontBold,
            color: 'green',
            fontSize: screenWidth / 33,
            textAlign: I18nManager.isRTL ? 'right' : 'left',
          }}
          numberOfLines={3}
        >
          {'تم التوصيل'}
        </Text>
      </View>

    </TouchableWithoutFeedback>
  );
};

export default TripsContainer;
const styles = StyleSheet.create({
  container: {
    backgroundColor: White,
    width: '95%',
    height: screenHeight / 7,
    alignSelf: 'center',
    borderWidth: .8,
    borderRadius: 10,
    borderColor: MediumGrey,
    alignItems: 'center',
    marginTop: screenHeight / 80,
    overflow: 'hidden', paddingTop: screenHeight / 80
  },
  bottomContainer: {
    backgroundColor: WhiteGery,
    width: '100%',
    height: screenHeight / 25,
    alignSelf: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: '3%',
    alignItems: 'center',
    position: 'absolute',
    bottom: '0%',
    flexDirection: 'row'
  },

});
