import {
    View,
    StyleSheet,
    Image,
    Text,
    I18nManager,
    Modal,
    Platform,
} from 'react-native';
import {
    appFont,
    White,
    screenWidth,
    screenHeight,
    DarkBlue,
    appFontBold,
    Green,
    mSize,
    Blue,
    Red,
    Black,
    appColor1,
    MediumGrey,
    WhiteGery,
} from './Styles';
import React, { useState } from 'react';
import { Button, Item } from 'native-base';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { strings } from '../screens/i18n';

const AccessoryContainer = props => {
    const [fav, setFav] = useState(true);

    return (
        <TouchableOpacity onPress={props.press} style={styles.container}>
            <View style={{ width: '100%', height: '60%' }}>
                <Image source={props.item.image ? { uri: props.item.image } : require('../images/The_second_dishes_Shrimp_Tomatoes_White_background_-1.png')} style={styles.imagecontainer} />
            </View>
            <View style={styles.textcontainer}>
                <Text numberOfLines={1} style={styles.title}>{props.item.name}</Text>
            </View>
            <View style={styles.bottomcontainer}>
                <Text numberOfLines={1} style={styles.title2}>{strings('lang.DailyRent')} {props.item.priceByDay} {strings('lang.SAR')} / {strings('lang.Monthly')} {props.item.buyPrice} {strings('lang.SAR')}</Text>
            </View>
        </TouchableOpacity>
    );
};

export default AccessoryContainer;
const styles = StyleSheet.create({
    container: {
        backgroundColor: WhiteGery,
        width: screenWidth / 2.3,
        height: 180,
        flexDirection: 'column',
        borderColor: MediumGrey,
        borderWidth: 1,
        borderRadius: 8,
        marginBottom: 10,
        overflow: 'hidden',
        marginHorizontal: screenWidth / 30
    },
    imagecontainer: {
        resizeMode: 'cover',
        height: '100%',
        width: '100%',
    },
    title: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        marginStart: '3%',
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'flex-start',
        color: Black
    },
    textcontainer: {
        width: '100%',
        // marginVertical: '1%',
        alignSelf: 'center',
        height: '20%',
        justifyContent: 'center',
        alignItems: 'center',
    },
    text: {
        flexDirection: 'row',
        width: '33%',
        alignItems: 'center',
        alignSelf: 'flex-start'
    },
    image: {
        resizeMode: 'contain',
        height: 15,
        width: '30%',
    },
    title2: {
        fontFamily: appFont,
        fontSize: screenWidth / 40,
        marginStart: '5%',
        color: 'black',
        alignSelf: 'flex-start'
    },
    title3: {
        fontFamily: appFont,
        fontSize: screenWidth / 42,
        // marginStart: '5%',
        color: White,
    },
    bottomcontainer: {
        backgroundColor: White,
        width: '100%',
        alignSelf: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        height: '20%',
        overflow: 'hidden',
    },
});
