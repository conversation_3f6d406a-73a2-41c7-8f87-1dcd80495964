import { Button } from 'native-base';
import React, { Component, useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager } from 'react-native';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import { strings } from '../screens/i18n';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  DarkGrey,
  Red,
  Green,
  DarkRed,
  DarkGreen,
  WhiteYellow,
  Grey,
  Black,
  appColor,
  Blue,
  MediumGrey,
  WhiteGery,
  appColor1,
} from './Styles';
import { useEffect } from 'react';

const DriverPackageTrip = props => {

  useEffect(() => {


  }, [])

  return (
    <TouchableOpacity onPress={props.tripPress} style={{ overflow: 'hidden', width: '95%', alignSelf: 'center', minHeight: screenHeight / 9, borderRadius: 20, borderWidth: 1, borderColor: MediumGrey, marginBottom: 10 }}>
      <View style={{ flexDirection: 'row', width: '100%', justifyContent: 'space-between', alignItems: 'center', alignSelf: 'center', height: screenHeight / 30, backgroundColor: WhiteGery, paddingHorizontal: '5%', }}>
        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 35, color: Black }}>{props.item.user_package.user.name}</Text>
        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 35, color: Black }}>{props.item.start_time}</Text>
      </View>
      {props.item.isHoliday
        ?
        <View
          style={{ width: '100%', paddingHorizontal: '5%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', marginTop: screenHeight / 80 }}>
          <View style={{ width: '100%', flexDirection: 'row', alignItems: 'center' }}>
            <Image source={require('../images/Group10600.png')} style={{ width: '10%', resizeMode: 'contain', marginEnd: '5%' }} />
            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, }}>{strings('lang.message11')}</Text>
          </View>
        </View>
        :
        <>
          <View
            style={{ width: '100%', paddingHorizontal: '5%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', marginTop: screenHeight / 80 }}>
            <View style={{ width: '100%', flexDirection: 'row', marginBottom: screenHeight / 80 }}>
              <Image source={require('../images/yallowlocation.png')} style={{ width: '5%', resizeMode: 'contain', marginEnd: '5%' }} />
              <View style={{ width: '90%' }}>
                {props.item.user_package.destinations.map((item) => {
                  return <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{item.from_address}</Text>

                })}
                {/* <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start' }}>هذا النص هو مثال لنص يمكن أن يستبدل في نفس</Text> */}
              </View>
            </View>
          </View>
        </>
      }
    </TouchableOpacity>
  );
};

export default DriverPackageTrip;
const styles = StyleSheet.create({

});
