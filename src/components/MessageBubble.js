import { View, Text, StyleSheet, I18nManager } from "react-native";
import React, { Component } from 'react';
import { useSelector } from "react-redux";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>G<PERSON>, White, appFontBold, screenWidth } from "./Styles";
import { Image } from "react-native";
import moment from "moment";

export default function MessageBubble(props) {
    //   const currentUser = useSelector((state) => state.users.currentUser);

    const { message, userId } = props;

    const isMyMessage = message ? message.sender.id === JSON.parse(userId) : false;
    // const isMyMessage = false;
    const isMessageRead = false;
    return (
        <View>
            <View style={{ flexDirection: isMyMessage ? 'row-reverse' : 'row', alignItems: 'flex-end', marginTop: 10, marginBottom: 3 }}>
                {isMyMessage
                    ?
                    <View style={{ marginStart: 10 }}>
                    </View>
                    :
                    <View style={{ width: screenWidth / 10, height: screenWidth / 10, borderRadius: screenWidth / 20, borderWidth: 1, borderColor: MediumGrey, marginStart: 10, overflow: 'hidden' }}>
                        <Image source={{ uri: message && message.sender.image }} style={{ width: '100%', height: '100%', resizeMode: 'cover', }} />
                    </View>
                }

                {isMyMessage
                    ?
                    <Image source={require('../images/chat_bubble1.png')} style={{ width: 15, height: 15, resizeMode: 'cover', alignSelf: 'flex-end', marginEnd: 10, zIndex: 100, marginStart: -1, transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }] }} />
                    :
                    <Image source={require('../images/chat_bubble.png')} style={{ width: 15, height: 15, resizeMode: 'cover', tintColor: MediumGrey, alignSelf: 'flex-end', marginStart: 10, transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }] }} />
                }
                <View
                    style={{
                        ...styles.messageContainer,
                        alignSelf: isMyMessage ? "flex-end" : "flex-start",
                        backgroundColor: isMyMessage ? White : MediumGrey,
                        borderBottomLeftRadius: isMyMessage ? 10 : 0,
                        borderBottomRightRadius: isMyMessage ? 0 : 10,
                    }}
                >
                    <Text
                        style={{
                            ...styles.messageText,
                        }}
                    >
                        {message && message.message}
                    </Text>
                    {/* <View
                style={{
                    ...styles.timeAndReadContainer,
                    left: isMyMessage ? 10 : 0,
                }}
            >
                <Text style={styles.timeText}>
                    {message.time}
                </Text>
                <View>
                    {isMessageRead ? (
                        <MaterialCommunityIcons name="read" size={16} color="#5bb6c9" />
                    ) : (
                        <MaterialCommunityIcons name="check" size={16} color="grey" />
                    )}
                </View>
            </View> */}
                </View>
            </View>
            <View style={{ width: screenWidth / 2, paddingHorizontal: isMyMessage ? screenWidth / 20 + screenWidth / 40 : 0, alignItems: 'center', justifyContent: 'flex-start', flexDirection: 'row', alignSelf: isMyMessage ? 'flex-end' : 'flex-start', marginEnd: isMyMessage ? 20 : 0, marginStart: isMyMessage ? 0 : 20 + screenWidth / 10, }}>
                <Text
                    style={{
                        ...styles.messageText,
                        textAlign: isMyMessage ? 'right' : 'left'
                    }}
                >
                    {message && moment(message.created_at).format('LT')}
                </Text>
                {isMyMessage
                    ?
                    <>
                        <View style={{ width: screenWidth / 40, }}>
                        </View>
                        <View style={{ width: screenWidth / 20, height: screenWidth / 20, borderRadius: screenWidth / 20, borderWidth: 1, borderColor: MediumGrey, overflow: 'hidden' }}>
                            <Image source={{ uri: message && message.receiver.image }} style={{ width: '100%', height: '100%', resizeMode: 'cover', }} />
                        </View>
                    </>
                    :
                    <></>
                }
            </View>
        </View >
    );
}

const styles = StyleSheet.create({
    messageContainer: {
        width: "75%",
        marginTop: 10,
        // marginHorizontal: 16,
        padding: 10,
        flexDirection: "row",
        borderRadius: 10,
        borderWidth: 1,
        borderColor: MediumGrey,
        // marginStart: -1
    },
    leftMessageArrow: {
        height: 0,
        width: 0,
        borderLeftWidth: 10,
        borderLeftColor: "transparent",
        borderTopColor: White,
        borderTopWidth: 10,
        alignSelf: "flex-start",
        borderRightColor: "black",
        right: 10,
        bottom: 10,
    },
    messageText: {
        fontSize: screenWidth / 26,
        fontFamily: appFontBold,
        width: "100%",
        color: Black
    },
    timeAndReadContainer: {
        flexDirection: "row",
    },
    timeText: {
        fontSize: 12,
        color: Black,
    },
    rightMsgArrow: {
        height: 0,
        width: 0,
        borderRightWidth: 10,
        borderRightColor: "transparent",
        borderTopColor: Green,
        borderTopWidth: 10,
        alignSelf: "flex-start",
        left: 6,
        bottom: 10,
    },

});
