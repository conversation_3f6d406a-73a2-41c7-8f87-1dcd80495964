import React, { Component, useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager } from 'react-native';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  DarkGrey,
  Red,
  Green,
  DarkRed,
  DarkGreen,
  WhiteYellow,
  Grey,
  Black,
  appColor,
  Blue,
  MediumGrey,
  WhiteGery,
  appColor1,
  DarkYellow,
  Gold,
} from './Styles';
import { Button } from 'native-base';
const AddressContainer = props => {
  const [active, setActive] = useState(true);
  const [lan, setLan] = useState('');

  return (
    <View style={{ flexDirection: 'row', height: screenHeight / 12, width: '100%', borderBottomWidth: 1, alignItems: 'center', justifyContent: 'space-between', borderBottomColor: WhiteGery }}>
      <TouchableOpacity
        onPress={props.screen == 'Home' ? () => { } : props.addressPress}
        style={{ width: '80%', alignItems: 'flex-start', }}>
        <Text numberOfLines={2} style={styles.textblack1}>{props.item.address}</Text>
        {/* <Text numberOfLines={1} style={styles.textgrey}>{item.address}</Text> */}
      </TouchableOpacity>
      <View
        style={{ width: '20%', height: screenHeight / 20, alignItems: 'center', justifyContent: 'space-between', flexDirection: 'row' }}>
        <Button
          transparent
          onPress={props.modalPress}
          style={{ width: '48%', alignItems: 'center', height: '100%', justifyContent: 'center' }}>
          <View style={{ alignItems: 'center', justifyContent: 'center', width: '100%', height: screenWidth / 20, borderRadius: screenWidth / 24, }}>
            <Image source={require('../images/edit.png')} style={[styles.image, { tintColor: Gold, alignSelf: 'center' }]} />
          </View>
        </Button>
        <Button
          transparent
          onPress={props.favPress}
          style={{ width: '48%', alignItems: 'center', height: '100%', justifyContent: 'center' }}>
          <View style={{ alignItems: 'center', justifyContent: 'center', width: '100%', height: screenWidth / 20, }}>
            <Image source={props.item.is_favorite ? require('../images/fav(2).png') : require('../images/fav.png')} style={[styles.image, { tintColor: props.item.is_favorite ? DarkYellow : null }]} />
          </View>
        </Button>
      </View>
    </View>
  );
};

export default AddressContainer;
const styles = StyleSheet.create({
  textblack1: { fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' },
  image: {
    resizeMode: 'contain',
    width: '100%',
    height: '100%',
    // tintColor: Green,
    alignSelf: 'center',

  },
});
