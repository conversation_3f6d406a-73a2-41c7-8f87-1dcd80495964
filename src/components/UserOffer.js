import { Button } from 'native-base';
import React, { Component, useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager } from 'react-native';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import { strings } from '../screens/i18n';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  DarkGrey,
  Red,
  Green,
  DarkRed,
  DarkGreen,
  WhiteYellow,
  Grey,
  Black,
  appColor,
  Blue,
  MediumGrey,
  WhiteGery,
  appColor1,
  DarkYellow,
  Green2,
  MediumGreen,
} from './Styles';
import { useEffect } from 'react';
const UserOffer = props => {
  const [active, setActive] = useState(true);
  const [lan, setLan] = useState('');
  const [distance, setDistance] = useState('');
  const [duration, setDuration] = useState('');

  useEffect(() => {
    const getDistance = async () => {
      console.log('1', props.trip.from_address_id ? props.trip.from_address.lat : props.trip.from_lat);
      console.log('2', props.trip.from_address_id ? props.trip.from_address.lng : props.trip.from_lng);
      console.log('3', props.item.driver.current_lat);
      console.log('4', props.item.driver.current_lng);
      if (props.item.driver.current_lat && props.item.driver.current_lng && (props.trip.from_address.lat || props.trip.from_lat) && (props.trip.from_address.lng || props.trip.from_lng)) {
        let GOOGLE_DISTANCES_API_KEY = 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'
        let ApiURL = "https://maps.googleapis.com/maps/api/distancematrix/json?";
        let mode = 'driving'
        let params = `origins=${props.item.driver.current_lat},${props.item.driver.current_lng}&destinations=${props.trip.from_address_id ? props.trip.from_address.lat : props.trip.from_lat},${props.trip.from_address_id ? props.trip.from_address.lng : props.trip.from_lng}&mode=${mode}&key=${GOOGLE_DISTANCES_API_KEY}`;
        let finalApiURL = `${ApiURL}${encodeURI(params)}`;

        console.log("finalApiURL:\n");
        console.log(finalApiURL);

        // get duration/distance from base to each target
        try {
          let response = await fetch(finalApiURL);
          let responseJson = await response.json();
          console.log("responseJson:\n");
          console.log(responseJson);
          let distance = (responseJson.rows[0].elements[0].distance.value / 1000);
          let duration = (Math.round(responseJson.rows[0].elements[0].duration.value / 60));
          setDistance(distance)
          setDuration(duration)
          return [distance, duration];
        }
        catch (error) {
          console.error(error);
        }
      }
    }
    getDistance();
  }, [])

  return (
    <View style={[styles.modal, { zIndex: 10000000 }]}>
      <View style={{ width: '100%', height: '45%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', }}>
        <View style={{ width: screenWidth / 8, height: screenWidth / 8, borderRadius: screenWidth / 16 }}>
          <Image source={props.item.driver && props.item.driver.image ? { uri: props.item.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '100%' }} />
        </View>


        <View style={{ width: '60%', height: '90%', alignItems: 'flex-start', }}>
          {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', }}>{'Chevorlet Corvette'}</Text> */}
          <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', }}>{props.item.driver && props.item.driver.name}</Text>
          {props.item.driver && props.item.driver.vehicle
            ?
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {props.item.driver.vehicle.car_brand ?
                <Image source={{ uri: props.item.driver.vehicle.car_brand.image }} style={{ height: screenWidth / 15, resizeMode: 'contain', width: screenWidth / 15 }} />
                :
                <></>
              }
              <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', lineHeight: screenHeight / 35 }}> - {props.item.driver.vehicle.car_model && props.item.driver.vehicle.car_model.name} - {props.item.driver.vehicle.year && props.item.driver.vehicle.year}</Text>
              <View style={{ width: screenWidth / 22, height: screenWidth / 22, borderRadius: screenWidth / 24, borderWidth: .8, borderColor: MediumGrey, backgroundColor: props.item.driver.vehicle.color && props.item.driver.vehicle.color, marginStart: 5 }}></View>
            </View>
            :
            <></>
          }
          {props.item.driver && props.item.driver.vehicle
            ?
            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', lineHeight: screenHeight / 35 }}>{props.item.driver.vehicle.plate_number + ' ' + props.item.driver.vehicle.plate_letter_right + ' ' + props.item.driver.vehicle.plate_letter_middle + ' ' + props.item.driver.vehicle.plate_letter_left}</Text>
            :
            <></>
          }

        </View>
        <View style={{ width: '20%', height: '90%', alignItems: 'flex-end', justifyContent: 'center', }}>
          <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 26, color: MediumGreen, marginStart: '1%' }}>{props.item.driver && props.item.price} {strings('lang.SR')}</Text>
        </View>
      </View>

      <View style={{ width: '85%', height: '15%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', alignSelf: 'flex-end', }}>
        <View style={{ width: '25%', height: '90%', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
          <Image source={require('../images/star.png')} style={{ height: '60%', resizeMode: 'contain', width: '25%' }} />
          <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', }}> {props.item.driver && props.item.driver.avg_rating}</Text>
        </View>
        {
          duration
            ?
            <>
              <View style={{ width: 1, height: '50%', backgroundColor: MediumGrey, }}></View>

              <View style={{ height: '90%', width: '25%', alignItems: 'center', justifyContent: 'center' }}>
                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', }}>{duration} {strings('lang.minute')}</Text>
              </View>
            </>
            :
            <></>
        }
        {
          distance
            ?
            <>
              <View style={{ width: 1, height: '50%', backgroundColor: MediumGrey, }}></View>

              <View style={{ height: '90%', width: '25%', alignItems: 'flex-start', justifyContent: 'center' }}>
                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}>{distance} {strings('lang.km')}</Text>
              </View>
            </>
            :
            <></>
        }

      </View>

      <View style={{ height: screenHeight / 20, flexDirection: 'row-reverse', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '100%', }}>
        <Button transparent style={styles.modalbuttonContainer2} onPress={props.onReject}>
          <Text style={styles.modalbuttonText}>{strings('lang.Refusal')}</Text>
        </Button>
        <Button transparent style={styles.modalbuttonContainer} onPress={props.onAccept}>
          <Text style={styles.modalbuttonText}>{strings('lang.Acceptance')}</Text>
        </Button>
      </View>
    </View>
  );
};

export default UserOffer;
const styles = StyleSheet.create({
  modal: {
    alignItems: 'center',
    width: screenWidth / 1.1,
    height: screenHeight / 5,
    // position: 'absolute',
    alignSelf: 'center',
    backgroundColor: White,
    borderRadius: 20,
    paddingVertical: '2%',
    justifyContent: 'space-between',
    paddingHorizontal: '5%',
    marginBottom: 30
  },
  modalbuttonContainer: {
    height: screenHeight / 25,
    width: '60%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Green2,
    alignSelf: 'center',
    borderRadius: 100
  },
  modalbuttonContainer2: {
    height: screenHeight / 25,
    width: '35%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: appColor1,
    alignSelf: 'center',
    borderRadius: 100
  },
  modalbuttonText: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 40, color: White
  },

});
