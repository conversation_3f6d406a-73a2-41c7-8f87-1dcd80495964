import {
  View,
  StyleSheet,
  Image,
  Text,
  I18nManager,
  Modal,
  Platform,
} from 'react-native';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  Green,
  mSize,
  Blue,
  Red,
  Black,
  appColor1,
  MediumGrey,
  WhiteGery,
  DarkGrey,
} from './Styles';
import React, { useEffect, useState } from 'react';
import { Button } from 'native-base';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { strings } from '../screens/i18n';
import Toaster from './Toaster';
import { useDispatch, useSelector } from 'react-redux';
import AdContainerLoading from './AdContainerLoading';

const ProductContainer = (props) => {
  const [fav, setFav] = useState(true);
  const [loading, setLoading] = useState(false);
  const token = useSelector(state => state.auth.token);

  const dispatch = useDispatch();



  useEffect(() => {
    const getInitialState = async () => {
      setLoading(true);
      setFav(props.item.isFav)
      setLoading(false);
    };

    getInitialState();
  }, [props]);

  const toggleFav = async () => {
    setLoading(true);
    try {
      let response = await dispatch(equpmentAction.toggleFav(props.item.id));

      if (response.status == 200) {
        setFav(!fav)
      }
      else {
        if (response.message) {
          Toaster(
            'top',
            'danger',
            Red,
            response.message,
            White,
            1500,
            screenHeight / 50,
          );
        }
      }
      setLoading(false);
    }
    catch (err) {
      setLoading(false);
    }
  };


  return (
    <View>
      <TouchableOpacity onPress={props.detailsPress} style={styles.container}>


        <TouchableOpacity onPress={props.detailsPress}>
          <Image source={props.item.image} style={styles.imagecontainer} />
        </TouchableOpacity>
        <View style={{ paddingStart: 2 }}>
          <Text numberOfLines={1} style={styles.title}>{props.item.name}</Text>
          <Text numberOfLines={1} style={styles.title1}>{props.item.price} <Text style={styles.title2}> AED</Text></Text>
        </View>


      </TouchableOpacity>
      <View
        style={{
          position: 'absolute',
          width: screenWidth / 12,
          height: screenWidth / 12,
          top: '6%',
          end: '6%',
          alignItems: 'center',
          justifyContent: 'center',
          alignSelf: 'center',
        }}
      >
        <TouchableOpacity
          onPress={() => { setFav(!fav) }}
          style={{ width: screenWidth / 12, height: screenWidth / 12 }}
        >
          <Image
            source={require('../images/Group-21.png')}
            style={{
              width: '99%',
              height: '99%',
              resizeMode: 'contain',
              borderRadius: 1,
              borderColor: White,
              tintColor: fav ? null : MediumGrey,
            }}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ProductContainer;
const styles = StyleSheet.create({
  container: {
    backgroundColor: White,
    width: screenWidth / 2.5,
    // height: 220,
    flexDirection: 'column',
    overflow: 'hidden',
    marginEnd: screenWidth / 30,
    alignSelf: 'center',
    marginHorizontal: screenWidth / 30,
    marginTop: screenHeight / 80,
    borderWidth: .5, borderColor: MediumGrey, borderRadius: 2,
  },
  imagecontainer: {
    resizeMode: 'cover',
    height: 175,
    width: '100%',
  },
  title: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 32,
    alignSelf: 'flex-start',
    color: Black,
  },
  title1: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 32,
    alignSelf: 'flex-start',
    color: appColor1,
  },
  title2: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 32,
    alignSelf: 'flex-start',
    color: DarkGrey,
  },

});
