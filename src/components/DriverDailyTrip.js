import { Button } from 'native-base';
import React, { Component, useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager } from 'react-native';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import { strings } from '../screens/i18n';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  DarkGrey,
  Red,
  Green,
  DarkRed,
  DarkGreen,
  WhiteYellow,
  Grey,
  Black,
  appColor,
  Blue,
  MediumGrey,
  WhiteGery,
  appColor1,
  DarkYellow,
} from './Styles';
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
const DriverDailyTrip = props => {
  const [active, setActive] = useState(true);
  const [lan, setLan] = useState('');
  const [distance, setDistance] = useState('');
  const [duration, setDuration] = useState('');
  const [distanceDriver, setDistanceDriver] = useState('');
  const [durationDriver, setDurationDriver] = useState('');
  const five_offer = useSelector(state => state.general.five_offer);

  useEffect(() => {
    const getDistance = async () => {
      if (props.item.from_address_id ? props.item.address_from.address : props.item.from_address &&
        props.item.to_address_id ? props.item.address_to.address : props.item.to_address
      ) {

        let GOOGLE_DISTANCES_API_KEY = 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'
        let ApiURL = "https://maps.googleapis.com/maps/api/distancematrix/json?";
        let mode = 'driving'
        let params = `origins=${props.item.from_address_id ? props.item.address_from.address : props.item.from_address}
        &destinations=${props.item.to_address_id ? props.item.address_to.address : props.item.to_address}&mode=${mode}&key=${GOOGLE_DISTANCES_API_KEY}`;
        let finalApiURL = `${ApiURL}${encodeURI(params)}`;

        // console.log("finalApiURL:\n");
        // console.log(finalApiURL);

        // get duration/distance from base to each target
        try {
          let response = await fetch(finalApiURL);
          let responseJson = await response.json();
          // console.log("responseJson:\n");
          // console.log(responseJson);
          setDistance(responseJson.rows[0].elements[0].distance.text)
          setDuration(responseJson.rows[0].elements[0].duration.text)
        } catch (error) {
          console.error(error);
        }
      }
    }

    const getDistanceDriver = async () => {
      if (props.driverLocation && props.item.from_address_id ? props.item.address_from.address : props.item.from_address) {

        let GOOGLE_DISTANCES_API_KEY = 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'
        let ApiURL = "https://maps.googleapis.com/maps/api/distancematrix/json?";
        let mode = 'driving'
        let params = `origins=${props.driverLocation}&destinations=${props.item.from_address_id ? props.item.address_from.address : props.item.from_address}&mode=${mode}&key=${GOOGLE_DISTANCES_API_KEY}`;
        let finalApiURL = `${ApiURL}${encodeURI(params)}`;

        // console.log("finalApiURL:\n");
        // console.log(finalApiURL);

        // get duration/distance from base to each target
        try {
          let response = await fetch(finalApiURL);
          let responseJson = await response.json();
          // console.log("responseJson:\n");
          // console.log(responseJson);
          setDistanceDriver(responseJson.rows[0].elements[0].distance.text)
          setDurationDriver(responseJson.rows[0].elements[0].duration.text)
        } catch (error) {
          console.error(error);
        }
      }
    }
    // getDistance();
    getDistanceDriver();
  }, [])

  return (
    <TouchableOpacity onPress={props.tripPress} style={{ overflow: 'hidden', width: '95%', alignSelf: 'center', minHeight: screenHeight / 6, borderRadius: 10, borderWidth: 1, borderColor: MediumGrey, marginBottom: 10 }}>
      <View style={{ flexDirection: 'row', width: '100%', justifyContent: 'space-between', alignItems: 'center', alignSelf: 'center', height: screenHeight / 30, backgroundColor: WhiteGery, paddingHorizontal: '5%', }}>
        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 35, color: Black }}>{props.item.user.name}</Text>
        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 35, color: Black }}>{durationDriver} - {distanceDriver}</Text>
      </View>
      <View
        style={{ width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', marginTop: screenHeight / 80 }}>
        <View style={{ width: '100%', paddingHorizontal: '5%', flexDirection: 'row', marginBottom: screenHeight / 80 }}>
          <Image source={require('../images/bluelocation.png')} style={{ width: '5%', resizeMode: 'contain', marginEnd: '5%' }} />
          <View style={{ width: '90%' }}>
            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{props.item.from_address_id ? props.item.address_from.address : props.item.from_address}</Text>
            {/* <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start' }}>هذا النص هو مثال لنص يمكن أن يستبدل في نفس</Text> */}
          </View>
        </View>
        <View style={{ width: '100%', paddingHorizontal: '5%', flexDirection: 'row', marginBottom: screenHeight / 80 }}>
          <Image source={require('../images/yallowlocation.png')} style={{ width: '5%', resizeMode: 'contain', marginEnd: '5%' }} />
          <View style={{ width: '90%' }}>
            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{props.item.to_address_id ? props.item.address_to.address : props.item.to_address}</Text>
            {/* <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start' }}>هذا النص هو مثال لنص يمكن أن يستبدل في نفس</Text> */}
          </View>
        </View>

        <View style={{ width: '100%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', backgroundColor: WhiteYellow }}>

          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', width: '30%', }}>
            <Image source={require('../images/Group9877.png')} style={{ width: screenWidth / 28, resizeMode: 'contain', marginTop: 5, height: screenHeight / 20, marginEnd: '2%' }} />
            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, }}>{five_offer ? props.item.original_price : props.item.client_price} {' '}
              <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 35, color: Black }}>{strings('lang.SR')} </Text>
            </Text>
          </View>

          <View style={{ width: 1, height: 20, backgroundColor: DarkGrey }}></View>

          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', width: '30%', }}>
            <Image source={require('../images/Group9856.png')} style={{ width: screenWidth / 28, resizeMode: 'contain', height: screenHeight / 20 }} />
            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 35, color: Black, marginStart: '1%' }}> {JSON.parse(props.item.expected_distance) / 1000}  {strings('lang.km')} </Text>
          </View>

          <View style={{ width: 1, height: 20, backgroundColor: DarkGrey }}></View>

          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', width: '30%', }}>
            <Image source={require('../images/time.png')} style={{ width: screenWidth / 28, resizeMode: 'contain', height: screenHeight / 20, }} />
            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 35, color: Black, marginStart: '1%' }}>  {props.item.expected_time} {strings('lang.minute')}</Text>
          </View>

        </View>

        <View style={{
          width: '100%', height: screenHeight / 20,
          // marginVertical: 10,
          alignItems: 'center', justifyContent: 'center',
          backgroundColor: DarkBlue,
        }}>
          <Text style={{
            fontFamily: appFontBold,
            fontSize: screenWidth / 25, color: White,
          }}>  {strings('lang.Acceptance')}</Text>
        </View>

      </View>
    </TouchableOpacity>
  );
};

export default DriverDailyTrip;
const styles = StyleSheet.create({

});
