import {
  View,
  StyleSheet,
  Image,
  Text,
  I18nManager,
  Modal,
  Platform,
} from 'react-native';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  Green,
  mSize,
  Blue,
  Red,
  Black,
  appColor1,
} from './Styles';
import React from 'react';
import { Button } from 'native-base';
import { useSelector } from 'react-redux';
import { TouchableOpacity } from 'react-native-gesture-handler';

export default function HeaderMore(props) {
  const token = useSelector(state => state.auth.token);

  return (
    <View style={styles.headercontainer}>
      <View style={{ flexDirection: 'row', height: '30%', alignItems: 'center', width: '100%', }}>
        <View
          style={{ width: '10%', alignItems: 'center', justifyContent: 'center', height: '100%' }}
        >
          <Button transparent onPress={props.backPress} style={styles.backButton}>
            <Image
              source={require('../images/arrow-left.png')}
              style={styles.backImage}
            />
          </Button>
        </View>
        <Text
          style={{
            fontFamily: appFontBold,
            color: White,
            fontSize: screenWidth / 24,
            textAlignVertical: 'center',
          }}
        >
          {props.title.toUpperCase()}
        </Text>
      </View>

    </View>
  );
}
const styles = StyleSheet.create({
  headercontainer: {
    backgroundColor: Red,
    height: Platform.OS == 'ios' ? screenHeight / 5.5 : screenHeight / 6.5,
    flexDirection: 'row',
    paddingHorizontal: '2.5%',
    marginTop: Platform.OS == 'ios' ? 0 : 0,
    width: screenWidth,
    alignItems: 'flex-start',
    paddingTop: Platform.OS == 'ios' ? screenHeight / 20 : screenHeight / 50
  },
  backImage: {
    width: '75%',
    height: '75%',
    tintColor: White,
    resizeMode: 'contain',
    alignSelf: 'center',
    transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }]
  },
  backButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%'
  },
  redContainer: {
    width: screenWidth / 20,
    height: screenWidth / 20,
    overflow: 'hidden',
    backgroundColor: Red,
    borderRadius: screenWidth / 40,
    position: 'absolute',
    top: '20%',
    borderWidth: 2,
    borderColor: White,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textWhite: {
    fontFamily: appFontBold,
    color: White,
    fontSize: screenWidth / 45,
  },
});
