import { DrawerContentScrollView, DrawerI<PERSON>s, DrawerItemList } from "@react-navigation/drawer";
import React, { useState } from "react";
import { I18nManager, Image, ImageBackground, Text, TouchableOpacity, View } from 'react-native'
import { strings } from "../screens/i18n";
import { appColor1, appFontBold, Black, screenHeight, screenWidth, White } from "./Styles";

const DrawerHeader = (props) => {
    const [isDriver, setIsDriver] = useState(false)

    return (
        <View style={{ flex: 1, }}>
            <ImageBackground source={require('../images/drawer.png')} style={{ width: '100%', height: screenHeight, }}>
                <View style={{ height: screenHeight }}>
                    <TouchableOpacity style={{ height: screenHeight / 10, marginTop: '5%', marginBottom: '35%', width: '90%', flexDirection: 'row', alignSelf: 'center', alignItems: 'center' }}>
                        <Image source={require('../images/Group10019.png')} style={{ width: '25%', height: '70%', resizeMode: 'contain' }} />
                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '10%' }}>عبد الرحمن محمد</Text>
                    </TouchableOpacity>
                    <DrawerContentScrollView {...props} showsVerticalScrollIndicator={false} contentContainerStyle={{}}>
                        {/* <DrawerItemList {...props} /> */}
                        <DrawerItemList {...props} />
                    </DrawerContentScrollView>
                    {isDriver ?
                        <TouchableOpacity
                            onPress={() => {
                                props.navigation.navigate('DriverRequests')
                                setIsDriver(false)
                            }}
                            style={{ height: screenHeight / 20, marginBottom: '8%', marginTop: '2%', width: '80%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', backgroundColor: White }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '10%' }}>{strings('lang.Switchtodriver')}</Text>
                        </TouchableOpacity>
                        :
                        <TouchableOpacity
                            onPress={() => {
                                props.navigation.navigate('MapAddress', { isPaid: false })
                                setIsDriver(true)
                            }}
                            style={{ height: screenHeight / 20, marginBottom: '8%', marginTop: '2%', width: '80%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', backgroundColor: White }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '10%' }}>{strings('lang.Switchtoapassenger')}</Text>
                        </TouchableOpacity>
                    }
                </View>
            </ImageBackground>
        </View>
    )
}

export default DrawerHeader;