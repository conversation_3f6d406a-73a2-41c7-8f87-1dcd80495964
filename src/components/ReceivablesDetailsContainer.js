import { Button } from 'native-base';
import React, { Component, useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager } from 'react-native';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import { strings } from '../screens/i18n';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  DarkGrey,
  Red,
  Green,
  DarkRed,
  DarkGreen,
  WhiteYellow,
  Grey,
  Black,
  appColor,
  Blue,
  MediumGrey,
  WhiteGery,
  appColor1,
  MediumGreen,
  Green2,
} from './Styles';
import { useEffect } from 'react';
const ReceivablesDetailsContainer = props => {

  useEffect(() => {

  }, [])

  return (
    <View style={styles.users}>
      <View style={{ alignItems: 'center', justifyContent: 'flex-start', width: '100%', }}>
        <View style={{ marginBottom: '3%', backgroundColor: WhiteGery, alignItems: 'center', paddingHorizontal: 10, justifyContent: 'space-between', flexDirection: 'row', width: '100%' }}>
          <Text style={{ fontFamily: appFont, fontSize: screenWidth / 36, color: DarkGrey, }}>{props.item.created_at_formatted}</Text>
          <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: DarkGrey, }}>{`${props.item.id} #`}</Text>
          {/* <Text style={{ fontFamily: appFont, fontSize: screenWidth / 36, color: DarkGrey, }}>{' دفع كاش'}</Text> */}
        </View>

        <View style={{ alignItems: 'center', justifyContent: 'space-between', flexDirection: 'row', width: '95%' }}>
          <Text style={{ fontFamily: appFont, fontSize: screenWidth / 35, color: Black, }}>{strings('lang.Tripprice')} </Text>
          <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: MediumGreen, }}>{`${props.item.amount} ${strings('lang.SR')}`}</Text>
        </View>

        <View style={{ alignItems: 'center', justifyContent: 'space-between', flexDirection: 'row', width: '95%' }}>
          <Text style={{ fontFamily: appFont, fontSize: screenWidth / 35, color: Black, }}>{strings('lang.Companycommission')} {'  '}
          </Text>
          <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Red, }}>{`${props.item.commission} ${strings('lang.SR')}`}</Text>

        </View>

      </View>
    </View>
  );
};

export default ReceivablesDetailsContainer;
const styles = StyleSheet.create({
  users: {
    alignItems: 'center',
    alignSelf: 'center',
    justifyContent: 'space-between',
    width: '95%',
    maxHeight: screenHeight / 7,
    borderWidth: 1,
    marginBottom: 10,
    borderColor: MediumGrey,
    overflow: 'hidden',
    borderRadius: 5,
    paddingBottom: 10
  },
});
