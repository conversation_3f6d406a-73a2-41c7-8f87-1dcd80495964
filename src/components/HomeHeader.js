import React, { Component } from 'react';
import {
  View,
  StyleSheet,
  Image,
  Text,
  I18nManager,
  Modal,
  Platform,
} from 'react-native';
import { TextInput, TouchableOpacity } from 'react-native-gesture-handler';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  Green,
  mSize,
  Blue,
  Red,
  Black,
  MediumGrey,
  Gold,
  appColor1,
  WhiteGery,
  DarkGrey,
  DarkYellow,
} from './Styles';
import { Button, Left, Right } from 'native-base';
import { strings } from '../screens/i18n';
import { Placement } from 'react-native-popover-view/dist/Types';
import { useSelector } from 'react-redux';

const HomeHeader = props => {
  const profilee = useSelector(state => state.auth.profile);

  return (
    <View style={styles.headercontainer}>
      <View
        style={{
          width: screenWidth / 1.35,
          height: '100%',
          alignSelf: 'flex-start',
          justifyContent: 'flex-start',
          alignItems: 'center',
          flexDirection: 'row',
        }}
      >
        <View style={{ width: screenWidth / 6, height: screenWidth / 6, borderRadius: screenWidth / 3, overflow: 'hidden', borderWidth: .8, borderColor: MediumGrey, marginStart: '5%' }}>
          <Image
            source={profilee ? profilee.image ? { uri: profilee.image } : require('../images/Group-26.png') : require('../images/Group-26.png')}
            style={{
              resizeMode: 'contain',
              width: screenWidth / 6,
              height: screenWidth / 6,
              alignSelf: 'center',
              marginHorizontal: screenWidth / 50,
            }}
          />
        </View>
        <View
          style={{
            flexDirection: 'column',
            width: '60%',
            height: '85%',
            alignSelf: 'center',
            alignItems: 'center',
            marginStart: '2%'
          }} >
          <Text
            style={{
              color: Gold,
              fontFamily: appFontBold,
              fontSize: screenWidth / 30,
              alignSelf: 'flex-start',
            }}
          >
            {strings('lang.hi')}
          </Text>
          <Text
            style={{
              color: Black,
              fontFamily: appFontBold,
              fontSize: screenWidth / 30,
              alignSelf: 'flex-start',
            }}
          >
            {profilee ? profilee.name : ''}
          </Text>
          {/* <Text
            style={{
              color: Black,
              fontFamily: appFont,
              fontSize: screenWidth / 48,
              alignSelf: 'flex-start',
            }}
          >
            {'الباقة الشهرية - تنتهى في 20 نوفمبر'}
          </Text> */}

        </View>
      </View>

      <TouchableOpacity
        onPress={props.notificationPress}
        style={{
          width: screenWidth / 15,
          height: '100%',
          borderRadius: 10,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {/* <View style={{
          position: 'absolute',
          top: screenHeight / 45,
          start: '0%',
          height: screenHeight / 50,
          width: screenHeight / 50,
          borderRadius: screenHeight / 100,
          backgroundColor: DarkBlue,
          zIndex: 100,
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Text style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 50 }}>1</Text>
        </View> */}
        <Image
          style={{
            resizeMode: 'contain',
            width: '80%',
            height: '100%',
            tintColor: DarkYellow,
            marginStart: '20%'
          }}
          source={require('../images/Group267.png')}
        />
      </TouchableOpacity>

      <View
        style={{
          width: screenWidth / 10,
          height: '100%',
          alignSelf: 'center',
          justifyContent: 'center',
          marginHorizontal: 5
        }}
      >
        <Image
          source={require('../images/Group10544.png')}
          style={{
            resizeMode: 'contain',
            width: '100%',
            height: '75%',
            alignSelf: 'center',
          }}
        />
      </View>

    </View>
  );
};
export default HomeHeader;

const styles = StyleSheet.create({
  headercontainer: {
    backgroundColor: White,
    height: screenHeight / 11,
    paddingEnd: '5%',
    alignSelf: 'flex-start',
    justifyContent: 'space-between',
    flexDirection: 'row',
    width: '100%',
    marginTop: Platform.OS == 'ios' ? screenHeight / 25 : screenHeight / 100, paddingTop: screenHeight / 100
  },
  title: {
    fontFamily: appFontBold,
    color: Black,
    fontSize: screenWidth / 25,
    textAlignVertical: 'center',
  },
  title2: {
    fontFamily: appFontBold,
    color: Red,
    fontSize: screenWidth / 25,
    textAlignVertical: 'center',
  },
  backButton: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
  },
  backImage: {
    width: '80%',
    height: '50%',
    resizeMode: 'contain',
    marginBottom: '20%',
  },
  drawerImage: {
    width: '80%',
    height: '80%',
    resizeMode: 'contain',
  },
  redContainer: {
    width: screenWidth / 20,
    height: screenWidth / 20,
    overflow: 'hidden',
    backgroundColor: Red,
    borderRadius: screenWidth / 40,
    position: 'absolute',
    top: '20%',
    borderWidth: 2,
    borderColor: White,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textWhite: {
    fontFamily: appFontBold,
    color: White,
    fontSize: screenWidth / 45,
  },
});
