import React, { Component, useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager, Pressable } from 'react-native';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import {
  appFont,
  White,
  screenWidth,
  screenHeight,
  DarkBlue,
  appFontBold,
  DarkGrey,
  Red,
  Green,
  DarkRed,
  DarkGreen,
  WhiteYellow,
  Grey,
  Black,
  appColor,
  Blue,
  MediumGrey,
  WhiteGery,
  appColor1,
  DarkYellow,
} from './Styles';
const Notificationcontainer = props => {
  const [active, setActive] = useState(true);
  const [lan, setLan] = useState('');

  return (
    <View style={styles.container}>
      <View onPress={props.onPress} style={{ flexDirection: 'row' }}>
        <View style={{ width: '20%', alignItems: 'center', alignSelf: 'center' }}>
          <View
            style={{
              width: screenWidth / 8,
              height: screenWidth / 8,
              borderRadius: 10,
              backgroundColor: WhiteGery,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Image
              style={{
                resizeMode: 'contain',
                width: '40%',
                height: '40%',
                tintColor: DarkYellow,
              }}
              source={require('../images/Group267.png')}
            />
          </View>
        </View>
        <View style={{ width: '79%', flexDirection: 'column', }}>
          <View
            style={{
              width: '100%',
              paddingVertical: '5%',
              alignItems: 'flex-start',
              justifyContent: 'center',
            }}
          >
            <Text
              style={{
                fontFamily: appFontBold,
                color: Black,
                fontSize: screenWidth / 33,
                alignSelf: 'flex-start',
                textAlign: I18nManager.isRTL ? 'left' : 'left',
              }}
            >
              {props.item.body}
            </Text>


          </View>
          {/* <Text
            style={{
              fontFamily: appFontBold,
              color: MediumGrey,
              fontSize: screenWidth / 33,
              alignSelf: 'flex-start',
            }}
            numberOfLines={1}
          >
            22 October 2023
          </Text> */}
        </View>
      </View>
      {/* <Pressable
        onPress={props.deleteNotiPress}
        style={{
          width: screenWidth / 8,
          height: screenWidth / 8,
          borderRadius: 10,
          // backgroundColor: WhiteGery,
          alignItems: 'center',
          justifyContent: 'center',
          position: 'absolute',
          end: '2%'
        }}
      >
        <Image
          style={{
            resizeMode: 'contain',
            width: '40%',
            height: '40%',
          }}
          source={require('../images/x.png')}
        />
      </Pressable> */}
    </View>
  );
};

export default Notificationcontainer;
const styles = StyleSheet.create({
  container: {
    backgroundColor: White,
    width: '95%',
    // height: screenHeight / 7,
    alignSelf: 'center',
    paddingVertical: '3%',
    flexDirection: 'row',
    borderWidth: .8,
    borderRadius: 15,
    borderColor: MediumGrey,
    alignItems: 'center',
    marginTop: screenHeight / 80
  },
  containerActive: {
    backgroundColor: WhiteYellow,
    width: '100%',
    minHeight: screenWidth / 5,
    maxHeight: screenWidth / 3.5,
    alignSelf: 'center',
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderColor: MediumGrey,
    alignItems: 'center'
  },
  modal: {
    flex: 1,
    alignItems: 'center',
    width: screenWidth / 1.3,
    height: screenHeight / 3,
    position: 'absolute',
    top: screenHeight / 3.2,
    alignSelf: 'center',
    backgroundColor: White,
    borderRadius: 10,
  },
  text: {
    fontFamily: appFontBold,
    color: DarkBlue,
    fontSize: screenWidth / 22,
    marginTop: '4%',
  },
  buttonContainer: {
    width: screenWidth / 4,
    height: '65%',
    backgroundColor: DarkBlue,
    alignSelf: 'center',
    marginHorizontal: 5,
    justifyContent: 'center',
    borderRadius: 10,
    alignItems: 'center',
    padding: 12,
    marginTop: 7,
  },
  buttonContainer2: {
    width: screenWidth / 4,
    height: '65%',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: DarkBlue,
    alignSelf: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
    borderRadius: 10,
    alignItems: 'center',
    padding: 12,
    // marginTop: 10,
  },
  buttonText: {
    color: White,
    fontFamily: appFontBold,
    fontSize: screenWidth / 28,
  },
  buttonText2: {
    color: DarkBlue,
    marginVertical: 5,
    alignSelf: 'center',
    fontSize: screenWidth / 25,
    fontFamily: 'Cairo-Bold',
  },
  icon: { resizeMode: 'contain', width: '90%', height: '55%' },
  modelIconContainer: { width: '15%', height: '100%', justifyContent: 'center' },
  modelTextContainer: { width: '85%', height: '100%', justifyContent: 'center' },
  modelText: {
    fontFamily: appFontBold,
    color: DarkBlue,
    fontSize: screenWidth / 25,
  },
});
