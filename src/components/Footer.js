
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Button } from 'native-base';
import React, { useEffect, useState } from 'react';
import { Text, View, Image, StyleSheet, I18nManager, ImageBackground, Platform } from 'react-native'
import { TouchableOpacity } from 'react-native-gesture-handler';

import { appColor1, appFont, appFontBold, Black, DarkGreen, DarkGrey, DarkYellow, MediumGrey, Red, screenHeight, screenWidth, White } from '../components/Styles'
import { strings } from '../screens/i18n';
import { useSelector } from 'react-redux';


const Footer = props => {
    const tripsCount = useSelector((state) => state.profile.tripsCount);

    // console.log('tripsCount', tripsCount);

    return (
        <View style={{
            height: screenHeight / 11, width: screenWidth, borderRadius: screenWidth / 20, paddingBottom: Platform.OS == 'ios' ? screenHeight / 20 : screenHeight / 40, marginTop: 5, zIndex: 10, backgroundColor: White, alignItems: 'center', alignSelf: 'center', shadowColor: '#000000',
            shadowOffset: {
                width: 0,
                height: 4
            },
            shadowRadius: 4,
            shadowOpacity: 1.0
        }}>
            <Image source={require('../images/footer.png')} style={{ width: '105%', height: screenHeight, resizeMode: 'contain' }} />
            <View style={{ position: 'absolute', top: '20%', width: screenWidth, height: screenHeight / 11.5, flexDirection: 'row', justifyContent: 'space-between', }}>
                <View style={styles.halfFooterContainer}>
                    <TouchableOpacity onPress={() => { props.navigation.push('Home', {}) }} style={styles.quarterFooterContainer}>
                        <Image style={props.current == 'Home' ? { ...styles.icon, tintColor: DarkYellow } : styles.icon} source={require('../images/Group-141.png')} />
                        <Text numberOfLines={1} style={props.current == 'Home' ? { ...styles.text, color: DarkYellow } : styles.text}>{strings('lang.Home')}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => { props.navigation.push('Orders') }} style={styles.quarterFooterContainer}>
                        {tripsCount != 0
                            ?
                            <View style={{
                                position: 'absolute', left: '40%', top: 0,
                                height: 15,
                                width: 15, borderRadius: 30,
                                backgroundColor: Red, zIndex: 10000
                            }}></View>
                            :
                            <></>
                        }

                        <Image style={props.current == 'Orders' ? { ...styles.icon, tintColor: DarkYellow } : styles.icon} source={require('../images/Group-140.png')} />
                        <Text numberOfLines={1} style={props.current == 'Orders' ? { ...styles.text, color: DarkYellow } : styles.text}>{strings('lang.Orders')}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => { props.navigation.push('Packages') }} style={styles.quarterFooterContainer}>
                        <Image style={props.current == 'Packages' ? { ...styles.icon, tintColor: DarkYellow } : styles.icon} source={require('../images/mony.png')} />
                        <Text numberOfLines={1} style={props.current == 'Packages' ? { ...styles.text, color: DarkYellow } : styles.text}>{strings('lang.socialist1')}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => { props.navigation.push('More'); }} style={styles.quarterFooterContainer}>
                        <Image style={props.current == 'More' ? { ...styles.icon, tintColor: DarkYellow } : styles.icon} source={require('../images/filter.png')} />
                        <Text numberOfLines={1} style={props.current == 'More' ? { ...styles.text, color: DarkYellow } : styles.text}>{strings('lang.More')}</Text>
                    </TouchableOpacity>
                    {/* <TouchableOpacity onPress={() => { props.navigation.navigate('More') }} style={styles.quarterFooterContainer}>
                        <Image style={props.current == 'More' ? { ...styles.icon, tintColor: appColor1, width: '35%', transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] } : { ...styles.icon, width: '35%', transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} source={require('../images/Group-44.png')} />
                        <Text numberOfLines={1} style={props.current == 'More' ? { ...styles.text, color: appColor1 } : styles.text}>{strings('lang.More')}</Text>
                    </TouchableOpacity> */}
                </View>
            </View>

        </View>
    );
};

const styles = StyleSheet.create({
    halfFooterContainer: { width: '100%', height: '100%', flexDirection: 'row', justifyContent: 'space-between', },
    quarterFooterContainer: { width: screenWidth / 4, height: '100%', alignItems: 'center', padding: 5, },
    icon: { height: '40%', resizeMode: 'contain', tintColor: DarkGrey, zIndex: 100, },
    text: { fontFamily: appFontBold, fontSize: screenWidth / 35, color: DarkGrey, zIndex: 100, marginTop: 2 },

});

export default Footer;
