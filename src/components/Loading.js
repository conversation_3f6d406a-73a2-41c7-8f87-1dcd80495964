
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Button } from 'native-base';
import React, { useEffect } from 'react';
import { Text, View, Image, StyleSheet, I18nManager, ImageBackground, Platform, ActivityIndicator } from 'react-native'
import { TouchableOpacity } from 'react-native-gesture-handler';

import { appColor1, appFont, appFontBold, DarkBlue, MediumGrey, Red, screenHeight, screenWidth, White } from '../components/Styles'
import { strings } from '../screens/i18n';


const Loading = props => {

    return (
        <View style={{ backgroundColor: MediumGrey, opacity: .8, position: "absolute", zIndex: 10000000000000, alignSelf: 'center', alignItems: 'center', width: screenWidth, height: 2 * screenHeight, }}>
            {/* <View style={{ backgroundColor: '#000000', opacity: .8, position: "absolute", zIndex: 10000000000000, justifyContent: 'center', alignSelf: 'center', alignItems: 'center', width: screenWidth, height: screenHeight, }}> */}

            {/* <Image source={require('../images/move.gif')} style={styles.carImage} /> */}
            <ActivityIndicator size={Platform.OS == 'ios' ? 100 : 50} color={DarkBlue} style={{ marginTop: screenHeight / 2.2 }} />
        </View>
    );
};

const styles = StyleSheet.create({
    carImage: {
        width: '30%',
        height: '20%',
        resizeMode: 'contain',
    },
});

export default Loading;
