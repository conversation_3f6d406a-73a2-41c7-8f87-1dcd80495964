import React, { Component, useEffect, useRef, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    TextInput,
    FlatList,
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    appColor1,
    MediumGreen,
    DarkYellow,
    DarkBlue,
    Gold,
    WhiteBlue,
    LightGreen,
} from '../components/Styles';
import { Button, Input, Textarea } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import RBSheet from 'react-native-raw-bottom-sheet';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import SelectDropdown from 'react-native-select-dropdown';
import OrdersContainer from '../components/OrdersContainer';


const OrderVipDetails = props => {
    const dispatch = useDispatch();
    const [termsAndConditionData, setTermsAndConditionData] = useState('');
    const [loading, setLoading] = useState(false);
    const [paymentMethodId, setPaymentMethodId] = useState(0);
    const [code, setCode] = useState('');
    const [activeText, setActiveText] = useState(0);
    const [orders, setOrders] = useState([{}, {}]);
    const [review, setReview] = useState('');


    useEffect(() => {


    }, []);

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.ordervip')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.VIPorderdetails')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />
                <ScrollView
                    showsVerticalScrollIndicator={false} style={{ width: '95%' }}>

                    {/* <View style={{ width: '100%', flexDirection: 'row', justifyContent: 'center', paddingTop: screenHeight / 100 }}>
                        <View style={{ width: '20%', height: screenHeight / 15 }}>
                            <Image source={require('../images/profile.png')} style={{ height: '100%', resizeMode: 'contain', width: '90%' }} />
                        </View>

                        <View style={{ width: '80%', alignItems: 'flex-start', }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: MediumGrey, marginStart: '1%', lineHeight: screenHeight / 40 }}>{'مجدى السيد'}</Text>
                            <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 25, alignItems: 'center' }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{'5520 AA'}</Text>
                                <View style={{ height: '70%', alignSelf: 'center', width: 1, backgroundColor: MediumGrey }}></View>
                                <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '10%', alignSelf: 'center' }} />
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {'4.5'}</Text>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 20, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-around', }}>
                                <Button transparent style={{ backgroundColor: LightGreen, width: '40%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                    <Image source={require('../images/call.png')} style={{ resizeMode: 'contain', width: '10%', marginHorizontal: '2%' }} />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, }}>{strings('lang.Call')}</Text>
                                </Button>
                                <Button transparent style={{ backgroundColor: White, borderWidth: 1, borderColor: MediumGrey, width: '40%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                    <Image source={require('../images/whatssapp.png')} style={{ resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, }}>{strings('lang.whatssapp')}</Text>
                                </Button>
                            </View>
                        </View>
                    </View> */}

                    <View style={styles.PriceContainer}>
                        <View style={styles.labelBlack}>
                            <View style={{ height: '100%', width: '25%', alignItems: 'flex-start', justifyContent: 'center' }}>
                                <Image source={require('../images/Group9877.png')} style={{ width: '70%', height: '75%', resizeMode: 'contain', }} />
                            </View>
                            <Text style={styles.blackText}>{strings('lang.Tripvalue')}</Text>
                        </View>
                        <Text style={styles.blackText}>{'200'}  {strings('lang.SR')}</Text>
                    </View>

                    <View style={styles.dataContainer}>
                        <View style={{ height: '100%', width: '10%', alignItems: 'center', justifyContent: 'center', }}>
                            <Image source={require('../images/222.png')} style={{ width: '90%', height: '35%', resizeMode: 'contain', marginTop: screenHeight / 120 }} />
                        </View>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35 }]}>{'12 اكتوبر 2022'}</Text>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%' }]}>{' 11:32 صباح'}</Text>
                    </View>

                    <View style={styles.dataContainer}>
                        <View style={{ height: '100%', width: '10%', alignItems: 'center', justifyContent: 'center', }}>
                            <Image source={require('../images/66.png')} style={{ width: '90%', height: '35%', resizeMode: 'contain', marginTop: screenHeight / 120 }} />
                        </View>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35 }]}>{'3'}</Text>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '2%' }]}>{'افراد'}</Text>
                    </View>

                    <View style={styles.dataContainer}>
                        <View style={{ height: '100%', width: '10%', alignItems: 'center', justifyContent: 'center', }}>
                            <Image source={require('../images/55.png')} style={{ width: '90%', height: '35%', resizeMode: 'contain', marginTop: screenHeight / 120 }} />
                        </View>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35 }]}>{'4'}</Text>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '2%' }]}>{'مقاعد'}</Text>
                    </View>



                    <View
                        style={[styles.contentContainer, { width: '95%', justifyContent: 'flex-start', alignItems: 'center', marginTop: screenHeight / 100, }]}>
                        <View style={{ height: screenHeight / 25, width: '5%', alignItems: 'center', justifyContent: 'flex-start' }}>
                            <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                        </View>
                        <View style={{ width: '92%' }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', alignSelf: 'flex-start' }}>مسجد قباء</Text>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginStart: '1%', alignSelf: 'flex-start' }}>هذا النص هو مثال لنص يمكن أن يستبدل في نفس</Text>
                        </View>
                    </View>
                    <View
                        style={[styles.contentContainer, { width: '95%', justifyContent: 'flex-start', alignItems: 'center', marginTop: screenHeight / 100 }]}>
                        <View style={{ height: screenHeight / 25, width: '5%', alignItems: 'center', justifyContent: 'flex-start' }}>
                            <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                        </View>
                        <View style={{ width: '92%' }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', alignSelf: 'flex-start' }}>مسجد قباء</Text>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginStart: '1%', alignSelf: 'flex-start' }}>هذا النص هو مثال لنص يمكن أن يستبدل في نفس</Text>
                        </View>
                    </View>


                </ScrollView>

                <View style={styles.buttonsContainer}>
                    <Button onPress={() => { props.navigation.navigate('PaymentMethods', { subscription: false }) }} style={styles.button}>
                        <Text style={styles.buttonText}>{strings('lang.Acceptance')}</Text>
                    </Button>
                    <Button onPress={() => { props.navigation.goBack('') }} style={styles.button1}>
                        <Text style={styles.buttonText}>{strings('lang.Refusal')}</Text>
                    </Button>
                </View>

                <View style={{ height: screenHeight / 15 }}></View>
            </View>
        );
    }
};

const styles = StyleSheet.create({
    PriceContainer: {
        height: screenHeight / 16,
        width: '100%',
        paddingHorizontal: '5%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        alignSelf: 'center',
        borderRadius: 10,
        backgroundColor: WhiteGery,
        marginVertical: '3%'
    },
    dataContainer: {
        height: screenHeight / 18,
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
        alignSelf: 'center',
        borderBottomColor: MediumGrey,
        borderBottomWidth: .8
    },

    labelBlack: {
        width: '30%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    container: {
        height: screenHeight / 9,
        width: '100%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row',
        marginVertical: '5%',
    },
    imageContainer: {
        height: '65%',
        width: '10%',
        alignSelf: 'flex-start',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    image: {
        height: '25%',
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
    },
    addressContainer: {
        height: '100%',
        width: '90%',
        alignSelf: 'flex-start',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
    },
    buttonsContainer: {
        width: '95%',
        height: screenHeight / 18,
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center'
    },
    button: {
        backgroundColor: DarkBlue,
        width: '66%',
        height: screenHeight / 20,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 20,
    },
    button1: {
        backgroundColor: Red,
        width: '30%',
        height: screenHeight / 20,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 20,
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
    },
    blackText: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
    },
    contentContainer: {
        width: '90%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2%',
        height: screenHeight / 18,
        borderRadius: 5,
        paddingHorizontal: 0,
        flexDirection: 'row',
    },
    locationImage: {
        resizeMode: 'contain',
        width: '100%',
        height: '50%'
    },
});

export default OrderVipDetails;
