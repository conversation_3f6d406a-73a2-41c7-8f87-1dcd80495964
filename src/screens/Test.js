import React, { useRef, useState } from 'react';
import { Animated, Dimensions, Image, ImageBackground, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Svg, { G, Path } from 'react-native-svg';
import { useDispatch } from 'react-redux';
import { appFontBold, Black, DarkBlue, Dark<PERSON><PERSON>w, White } from '../components/Styles';
import { strings } from './i18n';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const Test = props => {
    const dispatch = useDispatch();
    const [selectedPrize, setSelectedPrize] = useState(null);
    // const rotation = useRef(new Animated.Value(0)).current;
    const indicatorXValue = useRef(new Animated.Value(0)).current; // For the indicator movement
    const [rotation, setRotation] = useState(new Animated.Value(0));

    // Wheel items with 7 prizes
    const items = [
        { id: 1, name: "IPhone 16 Pro Max", percentage: 0 },
        { id: 2, name: "كاب", percentage: 100 },
        { id: 3, name: "مج", percentage: 100 },
        { id: 4, name: "ميدالية", percentage: 100 },
        { id: 5, name: "مج حراري", percentage: 100 },
        { id: 6, name: "ميدالية", percentage: 100 },
        { id: 7, name: "500 ريال", percentage: 0 },
    ];

    const spinWheel = () => {
        // Calculate winner based on percentages
        const totalPercentage = items.reduce((acc, item) => acc + item.percentage, 0);
        const randomValue = Math.random() * totalPercentage;

        let accumulated = 0;
        let winnerIndex = 0;

        for (let i = 0; i < items.length; i++) {
            accumulated += items[i].percentage;
            if (randomValue < accumulated) {
                winnerIndex = i;
                break;
            }
        }
        startIndicatorAnimation()

        // Calculate final rotation angle
        const anglePerSlice = 360 / items.length;
        const winnerAngle = 255 - winnerIndex * anglePerSlice - anglePerSlice / 2;

        // Spin animation
        Animated.timing(rotation, {
            toValue: (360 * 8 + winnerAngle) + 165, // More rotations for better effect
            duration: 5000, // Longer duration
            useNativeDriver: true,
        }).start(() => {
            setSelectedPrize(items[winnerIndex]);
            // rotation.setValue(winnerAngle + 145);
            setRotation(new Animated.Value(winnerAngle + 165)); // Reset rotation
            // setRotation(new Animated.Value(0)); // Reset rotation
            stopIndicatorAnimation()

        });
    };

    const interpolateRotation = rotation.interpolate({
        inputRange: [0, 360],
        outputRange: ["0deg", "360deg"],
    });

    // Start the indicator animation
    const startIndicatorAnimation = () => {
        Animated.loop(
            Animated.sequence([
                Animated.timing(indicatorXValue, {
                    toValue: 25, // Move 20 pixels to the right
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(indicatorXValue, {
                    toValue: -25, // Move 20 pixels to the left
                    duration: 300,
                    useNativeDriver: true,
                }),
            ])
        ).start();
    };

    // Stop the indicator animation
    const stopIndicatorAnimation = () => {
        indicatorXValue.stopAnimation();
        indicatorXValue.setValue(0); // Reset position
    };



    const indicatorRotation = indicatorXValue.interpolate({
        inputRange: [0, 360],
        outputRange: ['0deg', '80deg'],
    });

    return (
        <ImageBackground source={require('../images/winBackground.jpg')} style={{ width: screenWidth, height: '100%', resizeMode: 'cover', alignSelf: 'center', }} >

            <View style={styles.container}>
                <Image source={require('../images/allWin.png')} style={{
                    width: screenWidth / 1.2,
                    height: screenHeight / 10, resizeMode: 'contain', marginTop: -screenHeight / 10,
                    marginBottom: screenHeight / 15,
                }} />


                <Pressable
                    onPress={() => {
                        props.navigation.push('Home');
                    }}
                    style={{
                        position: 'absolute', top: 50, right: 5,
                        // height: screenHeight / 12, width: screenHeight / 12, borderRadius: screenHeight / 24, 
                        // backgroundColor: Black,
                        zIndex: 10000,
                        alignItems: 'center', justifyContent: 'center'
                    }}>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 20, color: White }}>{strings('lang.Skip')}</Text>

                </Pressable>
                <View style={styles.wheelContainer}>
                    {/* Center dot */}
                    <View style={styles.centerDot} >
                        <Image source={require('../images/Group10.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain' }} />
                    </View>

                    <Animated.View
                        style={[
                            styles.wheel,
                            {
                                transform: [{ rotate: interpolateRotation }]
                            }
                        ]}
                    >
                        <Svg width={300} height={300} viewBox="0 0 300 300">
                            <G rotation={-45} origin="150,150" stroke={'#9c8113'} strokeWidth={1}>
                                {items.map((item, index) => {
                                    const angle = (360 / items.length) * index;
                                    const nextAngle = (360 / items.length) * (index + 1);
                                    const largeArcFlag = nextAngle - angle > 180 ? 1 : 0;
                                    const x1 = 150 + 150 * Math.cos((Math.PI / 180) * angle);
                                    const y1 = 150 + 150 * Math.sin((Math.PI / 180) * angle);
                                    const x2 = 150 + 150 * Math.cos((Math.PI / 180) * nextAngle);
                                    const y2 = 150 + 150 * Math.sin((Math.PI / 180) * nextAngle);

                                    return (
                                        <Path
                                            key={item.id}
                                            d={`M150,150 L${x1},${y1} A150,150 0 ${largeArcFlag},1 ${x2},${y2} Z`}
                                            fill={DarkYellow} // Gold and Orange alternating
                                            stroke="#000"
                                            strokeWidth="1"
                                        />
                                    );
                                })}
                            </G>

                            {items.map((item, index) => {
                                const angle = ((360 / items.length) * (index + 0.5)) + 210;
                                const x = 150 + 85 * Math.cos((Math.PI / 180) * angle);
                                const y = 150 + 85 * Math.sin((Math.PI / 180) * angle);
                                return (
                                    <View
                                        key={item.id}
                                        style={{
                                            position: 'absolute',
                                            top: y - 25,
                                            right: x - 50,
                                            width: 100, // Adjust width to fit text
                                            height: 40, // Adjust height to fit text
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            transform: [
                                                { rotate: `${angle + 90}deg` }], // Rotate text
                                        }}
                                    >

                                        {item.id == 1 ?

                                            <Image source={
                                                require('../images/1.png')
                                            } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                            :
                                            item.id == 2 ?
                                                <Image source={
                                                    require('../images/2.png')
                                                } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                                :
                                                item.id == 3 ?
                                                    <Image source={
                                                        require('../images/3.png')
                                                    } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                                    :
                                                    item.id == 4 ?
                                                        <Image source={
                                                            require('../images/4.png')
                                                        } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                                        :
                                                        item.id == 5 ?
                                                            <Image source={
                                                                require('../images/5.png')
                                                            } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                                            :
                                                            item.id == 6 ?
                                                                <Image source={
                                                                    require('../images/6.png')
                                                                } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                                                :

                                                                <Image source={
                                                                    require('../images/7.png')
                                                                } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                        }
                                        <Text
                                            numberOfLines={2}
                                            style={{
                                                fontSize: item.id == 1 ? screenWidth / 60 : screenWidth / 45,
                                                fontFamily: appFontBold,
                                                color: Black,
                                                textAlign: 'center',
                                            }}>
                                            {
                                                item.name
                                            }
                                        </Text>
                                    </View>
                                );
                            })}
                        </Svg>
                    </Animated.View>

                    {/* Pointer */}
                    <Animated.View
                        style={[
                            styles.indicator,
                            { transform: [{ rotate: indicatorRotation }] },
                        ]}
                        elevation={10}
                    >
                        <View style={styles.triangle}
                            elevation={10}

                        />
                    </Animated.View>
                    {/* <View style={styles.pointerContainer}>
                        <View style={styles.pointer} />
                    </View> */}
                </View>

                <TouchableOpacity
                    style={styles.spinButton}
                    onPress={spinWheel}
                    disabled={selectedPrize !== null}
                >
                    <Text style={styles.spinButtonText}>Spin the Wheel</Text>
                </TouchableOpacity>

                {selectedPrize && (
                    <View style={styles.prizeContainer}>
                        <Text style={styles.prizeText}>
                            Congratulations! You won:
                        </Text>
                        <Text style={styles.prizeName}>
                            {selectedPrize.name}
                        </Text>
                    </View>
                )}
            </View>
        </ImageBackground>
    );
};

const styles = StyleSheet.create({
    indicator: {
        position: 'absolute',
        top: -20,
        zIndex: 2,
        // top: I18nManager.isRTL ? screenHeight / 4 : screenHeight / 3.45,
        zIndex: 10000,
        shadowColor: '#000000',
        shadowOffset: {
            width: 0,
            height: 0
        },
        shadowRadius: 12,
        shadowOpacity: 1.0,
        elevation: 10
    },
    triangle: {
        width: 0,
        height: 0,
        borderLeftWidth: 15,
        borderRightWidth: 15,
        borderBottomWidth: 30,
        borderLeftColor: 'transparent',
        borderRightColor: 'transparent',
        borderBottomColor: DarkBlue,
        elevation: 10,
        transform: [{ rotate: '180deg' }],
        elevation: 10

    },
    container: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    wheelContainer: {
        marginTop: 50,
        alignItems: 'center',
        justifyContent: 'center',
    },
    wheel: {
        width: 300,
        height: 300,
    },
    centerDot: {
        position: 'absolute',
        width: 60,
        height: 60,
        backgroundColor: White,
        borderRadius: 30,
        overflow: 'hidden',
        zIndex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    pointerContainer: {
        position: 'absolute',
        top: -20,
        zIndex: 2,
    },
    pointer: {
        width: 40,
        height: 40,
        backgroundColor: 'red',
        transform: [{ rotate: '45deg' }],
        borderRadius: 8,
    },
    spinButton: {
        marginTop: 40,
        paddingVertical: 15,
        paddingHorizontal: 30,
        backgroundColor: DarkYellow,
        borderRadius: 25,
        elevation: 5,
    },
    spinButtonText: {
        color: White,
        fontSize: 18,
        fontWeight: 'bold',
    },
    prizeContainer: {
        marginTop: 30,
        alignItems: 'center',
    },
    prizeText: {
        fontSize: 16,
        color: Black,
        marginBottom: 5,
    },
    prizeName: {
        fontSize: 24,
        fontWeight: 'bold',
        color: DarkYellow,
        textAlign: 'center',
    },
});

export default Test;