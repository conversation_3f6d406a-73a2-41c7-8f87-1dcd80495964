import React, { Component, useEffect, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    PermissionsAndroid,
    SectionList,
    ActivityIndicator,
    RefreshControl,
    FlatList,
    BackHandler,
    Alert,
    Linking
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    MediumGreen,
    appColor1,
    Grey,
    DarkYellow,
    DarkBlue,
    Gold,
} from '../components/Styles';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import FooterDriver from '../components/FooterDriver';
import Toaster from '../components/Toaster';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import RequestContainer from '../components/RequestContainer';
import * as driverDailyTripsActions from '../../Store/Actions/driverDailyTrips';
import * as driverPackagesActions from '../../Store/Actions/driverPackages';
import * as driverVipTripsActions from '../../Store/Actions/driverVipTrips';
import * as driverCityTripsActions from '../../Store/Actions/driverCityTrips';
import * as driverProfileActions from '../../Store/Actions/driverProfile';
import * as authActions from '../../Store/Actions/auth';
import * as generalActions from '../../Store/Actions/general';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import DriverDailyTrip from '../components/DriverDailyTrip';
import DriverPackageTrip from '../components/DriverPackageTrip';
import Geolocation from 'react-native-geolocation-service';
import Geocoder from 'react-native-geocoding';
import VipTrip from '../components/VipTrip';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Loading from '../components/Loading';
import Sound from 'react-native-sound';
import VipRequest from '../components/VipRequest';
import PackagRequest from '../components/PackagRequest';



const ASPECT_RATIO = screenWidth / screenHeight


const DriverOffers = props => {

    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [loadingDailyTrips, setLoadingDailyTrips] = useState(false);
    const [loadingPackagesTrips, setLoadingPackagesTrips] = useState(false);
    const [loadingVipTrips, setLoadingVipTrips] = useState(false);
    const [loadingBetweenCitiesTrips, setLoadingBetweenCitiesTrips] = useState(false);
    const [name, setName] = useState('');
    const [image, setImage] = useState('');


    const [Reload, setReload] = useState(false);

    const [connectedStatus, setConnectedStatus] = useState(false);
    const [connected, setConnected] = useState([
        {
            status: true,
            name: strings('lang.connected'),
        },
        {
            status: false,
            name: strings('lang.notConnected'),
        },
    ]);

    const [categories, setcategories] = useState([
        {
            id: 2,
            name: strings('lang.packages')
        },
        {
            id: 3,
            name: strings('lang.VIPrequests')
        },
    ]);
    const [categoryId, setCategoryId] = useState(2);

    const [dailyTrips, setDailyTrips] = useState([]);
    const [noResault, setNoResault] = useState('');
    const [page, setPage] = useState(0);
    const [lastPage, setLastPage] = useState(0);

    const [packagesTrips, setPackagesTrips] = useState([]);
    const [packagesRequests, setpackagesRequests] = useState([]);
    const [noResault1, setNoResault1] = useState('');
    const [page1, setPage1] = useState(0);
    const [lastPage1, setLastPage1] = useState(0);

    const [vipRequests, setVipRequests] = useState([]);
    const [vipTrips, setVipTrips] = useState([]);
    const [noResault2, setNoResault2] = useState('');
    const [page2, setPage2] = useState(0);
    const [lastPage2, setLastPage2] = useState(0);

    const [betweenCitiesTrips, setBetweenCitiesTrips] = useState([]);
    const [noResault3, setNoResault3] = useState('');
    const [page3, setPage3] = useState(0);
    const [lastPage3, setLastPage3] = useState(0);


    const [region, SetRegion] = useState({
        latitude: 0,
        longitude: 0,
        longitudeDelta: 0.01 * ASPECT_RATIO,
        latitudeDelta: 0.01
    })
    const [lat, SetLat] = useState(0)
    const [lng, SetLng] = useState(0)
    const [addressDesription, setAddressDesription] = useState('');

    const [profile, setProfile] = useState({});

    const dispatch = useDispatch();
    const isFocused = useIsFocused();

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    useEffect(() => {

        const getVipRequests = async () => {
            setLoading(true)
            try {
                let response = await dispatch(driverVipTripsActions.getVipRequests());
                if (response.success == true) {
                    if (response.data.length == 0) {
                        setVipRequests([])
                        setNoResault2(strings('lang.No_Results'));
                    } else {
                        setVipRequests(response.data)
                        setNoResault2('');
                    }
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getUserPackageRequests = async () => {
            setLoading(true)
            try {
                let response = await dispatch(driverPackagesActions.getUserPackageRequests());
                if (response.success == true) {
                    if (response.data.length == 0) {
                        setpackagesRequests([])
                        setNoResault1(strings('lang.No_Results'));
                    } else {
                        setpackagesRequests(response.data)
                        setNoResault1('');
                        console.log('package', response.data);
                    }
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        // setNoResault1(strings('lang.No_Results'));
        getVipRequests()
        getUserPackageRequests()
    }, [Reload, isFocused]);

    // vipTrips
    const onRefreshVip = async () => {
        setLoading(true)
        try {
            let response = await dispatch(driverVipTripsActions.getVipRequests());
            if (response.success == true) {
                if (response.data.length == 0) {
                    setVipRequests([])
                    setNoResault2(strings('lang.No_Results'));
                } else {
                    setVipRequests(response.data)
                    setNoResault2('');
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const updateRequestStatus = async (id, status) => {
        setLoadingMore(true)
        try {
            let response = await dispatch(driverVipTripsActions.updateRequestStatus(id, status));
            if (response.success == true) {
                if (status == 'accept') {
                    props.navigation.navigate('MapDriver', { request: 3, trip: response.data })
                } else {
                    onRefreshVip()
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false);
        }
        catch (err) {
            console.log('err', err)
            setLoadingMore(false);
        }
    };


    const onRefreshPackage = async () => {
        setLoading(true)
        try {
            let response = await dispatch(driverPackagesActions.getUserPackageRequests());
            if (response.success == true) {
                if (response.data.length == 0) {
                    setpackagesRequests([])
                    setNoResault1(strings('lang.No_Results'));
                } else {
                    setpackagesRequests(response.data)
                    setNoResault1('');
                    console.log('package', response.data);
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const updateRequestStatusPackage = async (id, status) => {
        setLoadingMore(true)
        try {
            let response = await dispatch(driverPackagesActions.updateRequestStatus(id, status));
            if (response.success == true) {
                if (status == 'accept') {
                    props.navigation.navigate('DriverRequests')
                } else {
                    onRefreshPackage()
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false);
        }
        catch (err) {
            console.log('err', err)
            setLoadingMore(false);
        }
    };



    if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White, }}>
                <Header
                    title={strings('lang.Requests')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />
                <View style={{ height: screenHeight / 20, width: '95%', flexDirection: 'row', justifyContent: 'space-evenly', backgroundColor: WhiteGery, marginBottom: categoryId == 2 ? null : '2%' }}>
                    {categories.map((item, index) => {
                        return (
                            <Pressable
                                onPress={() => { setCategoryId(item.id) }}
                                style={item.id == categoryId ? styles.activeButton : styles.Button}
                            >
                                <Text style={item.id == categoryId ? styles.activeLabel : styles.label}>{item.name}</Text>
                            </Pressable>
                        )
                    })
                    }
                </View>
                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    {[{}, {}, {}, {}, {}].map((item) => {
                        return <View style={{ paddingTop: screenHeight / 50 }}>
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 40,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '5%',
                                    alignSelf: "flex-start",
                                    marginRight: "3%",
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>

                            <View
                                style={{
                                    width: '80%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '3%',
                                    alignSelf: "center"
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>

                            <View
                                style={{
                                    width: '30%',
                                    height: screenHeight / 50,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '3%',
                                    alignSelf: "flex-start",
                                    //  marginTop:"3%",
                                    marginVertical: "3%"
                                    //  borderRadius:'5%'
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>


                        </View>
                    })}

                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    } else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Requests')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />
                {loadingMore || loading
                    ?
                    <Loading />
                    :
                    <></>
                }
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    refreshControl={
                        <RefreshControl refreshing={Reload} onRefresh={() => {
                            onRefreshVip();
                            onRefreshPackage();
                        }} />
                    }
                >

                    <View style={{ height: screenHeight / 20, width: '100%', flexDirection: 'row', justifyContent: 'space-evenly', backgroundColor: WhiteGery, marginBottom: categoryId == 2 ? null : '2%', paddingHorizontal: '5%' }}>
                        {categories.map((item, index) => {
                            return (
                                <Pressable
                                    onPress={() => { setCategoryId(item.id) }}
                                    style={item.id == categoryId ? styles.activeButton : styles.Button}
                                >
                                    <Text style={item.id == categoryId ? styles.activeLabel : styles.label}>{item.name}</Text>
                                    {item.id == 2 ?
                                        packagesRequests.length == 0 ?
                                            <></>
                                            :
                                            <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center' }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: White }}>{packagesRequests.length}</Text>
                                            </View>
                                        :
                                        <></>
                                    }
                                    {item.id == 3 ?
                                        vipRequests.length == 0 ?
                                            <></>
                                            :
                                            <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center' }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: White }}>{vipRequests.length}</Text>
                                            </View>
                                        :
                                        <></>
                                    }
                                </Pressable>
                            )
                        })
                        }
                    </View>
                    <View style={{ height: screenHeight / 50 }}></View>

                    <>
                        {categoryId == 2 &&
                            <>
                                {
                                    noResault1 ? (
                                        <ScrollView style={{}}>
                                            <View style={{ width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center' }}>
                                                <Image source={require('../images/Group10500.png')} style={{ width: '100%', height: screenHeight / 5, resizeMode: 'contain', marginTop: screenHeight / 8 }} />

                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 24, color: Black, textAlign: 'center', marginVertical: screenHeight / 80 }}>{strings('lang.message10')}</Text>

                                                <Image source={require('../images/loading.png')} style={{ width: '100%', height: screenHeight / 20, resizeMode: 'contain' }} />
                                            </View>
                                        </ScrollView>
                                    ) : (
                                        <View></View>
                                    )
                                }
                                <FlatList
                                    data={packagesRequests}
                                    refreshControl={
                                        <RefreshControl refreshing={Reload} onRefresh={onRefreshPackage} />
                                    }
                                    showsVerticalScrollIndicator={false}
                                    renderItem={({ item, key }) =>
                                        // <View></View>
                                        <PackagRequest acceptPress={() => { updateRequestStatusPackage(item.id, 'accept') }} rejectPress={() => { updateRequestStatusPackage(item.id, 'reject') }} item={item} noLabel />
                                    }
                                    // keyExtractor={item => item.id}
                                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth / 1.05, backgroundColor: White, }}
                                />
                            </>
                        }
                        {categoryId == 3 &&
                            <>
                                {
                                    noResault2 ? (
                                        <ScrollView style={{}}>
                                            <View style={{ width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center' }}>
                                                <Image source={require('../images/Group10500.png')} style={{ width: '100%', height: screenHeight / 5, resizeMode: 'contain', marginTop: screenHeight / 8 }} />

                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 24, color: Black, textAlign: 'center', marginVertical: screenHeight / 80 }}>{strings('lang.message10')}</Text>

                                                <Image source={require('../images/loading.png')} style={{ width: '100%', height: screenHeight / 20, resizeMode: 'contain' }} />
                                            </View>
                                        </ScrollView>
                                    ) : (
                                        <View></View>
                                    )
                                }
                                <FlatList
                                    data={vipRequests}
                                    // refreshControl={
                                    //     <RefreshControl refreshing={Reload} onRefresh={onRefreshVip} />
                                    // }
                                    showsVerticalScrollIndicator={false}
                                    renderItem={({ item, key }) =>
                                        <VipRequest acceptPress={() => { updateRequestStatus(item.id, 'accept') }} rejectPress={() => { updateRequestStatus(item.id, 'reject') }} item={item} noLabel />
                                    }
                                    // keyExtractor={item => item.id}
                                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth / 1.05, backgroundColor: White, }}
                                />
                            </>
                        }
                    </>


                </ScrollView>

                <View style={{ height: 10 }}></View>

                <FooterDriver navigation={props.navigation} current={'DriverOffers'} />
            </View>
        );
    }
};

const styles = StyleSheet.create({
    connectedContainer: {
        flexDirection: 'row',
        alignItems: "center",
        justifyContent: 'center',
        alignSelf: 'center',
        width: '40%', height: screenHeight / 25,
        borderWidth: 1,
        borderColor: DarkYellow,
        borderRadius: 100,
        marginVertical: '10%', overflow: 'hidden'
    },
    connected: {
        width: '50%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center', borderRadius: 100
    },

    connectedText: {
        fontSize: screenWidth / 38,
        fontFamily: appFont,
        // alignSelf: 'flex-start',
        // marginHorizontal: '2.5%',
        color: Black,
    },
    star: {
        fontFamily: appFont,
        fontSize: screenWidth / 20,
        color: Red
    },
    input: {
        height: 45, borderWidth: 1, borderRadius: 5, borderColor: MediumGrey, flexDirection: 'row', justifyContent: 'center',
        width: "100%", fontSize: screenWidth / 28, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left'
    },

    loginError: {
        color: Red, fontFamily: appFont, alignSelf: 'flex-start', fontSize: screenWidth / 33
    },
    textImg: {
        fontSize: screenWidth / 30,
        fontFamily: appFont,
        color: Black,
        marginTop: '2%',
    },
    request: {
        width: '95%',
        height: screenHeight / 8,
        borderWidth: 1, borderColor: Grey,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center'
    },
    locationImage: {
        width: '90%',
        resizeMode: 'contain'
    },
    activeButton: { flexDirection: 'row', height: '95%', width: '25%', alignItems: "center", justifyContent: "center", alignSelf: 'center', borderBottomColor: DarkBlue, borderBottomWidth: 3, borderRadius: 2 },
    activeLabel: { fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, },
    Button: { flexDirection: 'row', height: '95%', width: '25%', alignItems: "center", justifyContent: "center", alignSelf: 'center', borderBottomColor: WhiteGery, borderBottomWidth: 3 },
    label: { fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, },
});

export default DriverOffers;
