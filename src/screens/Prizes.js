import React, { useRef, useState } from 'react';
import { Animated, Easing, I18nManager, Image, ImageBackground, Modal, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Black, DarkBlue, DarkYellow, White, appFontBold, screenHeight, screenWidth } from '../components/Styles';
import { strings } from './i18n';


import LinearGradient from 'react-native-linear-gradient';
import Svg, { G, Path } from 'react-native-svg';
import { useDispatch } from 'react-redux';
// import Loading from '../components/Loading';



const Prizes = props => {
    const indicatorXValue = useRef(new Animated.Value(0)).current; // For the indicator movement
    const [modalVisible, setModalVisible] = useState(false);

    const [rotation, setRotation] = useState(new Animated.Value(0));
    const rotationRef = useRef(new Animated.Value(0)).current;

    const [selectedPrize, setSelectedPrize] = useState(null);
    // const [items, setItems] = useState([]);
    // const [loading, setLoading] = useState(false);
    const dispatch = useDispatch();
    const items = [
        { id: 1, name: "IPhone 16 Pro Max", percentage: 0 },
        { id: 2, name: "كاب", percentage: 100 },
        { id: 3, name: "مج", percentage: 100 },
        { id: 4, name: "ميدالية", percentage: 100 },
        { id: 5, name: "مج حراري", percentage: 100 },
        { id: 6, name: "ميدالية", percentage: 100 },
        { id: 7, name: "500 ريال", percentage: 0 },
    ];

    const spinWheel = () => {
        const totalPercentage = items.reduce((acc, item) => acc + item.percentage, 0);
        const randomValue = Math.random() * totalPercentage;

        let accumulated = 0;
        let winnerIndex = 0;

        for (let i = 0; i < items.length; i++) {
            accumulated += items[i].percentage;
            if (randomValue < accumulated) {
                winnerIndex = i;
                break;
            }
        }
        startIndicatorAnimation()
        const anglePerSlice = 360 / items.length;
        const winnerAngle = 255 - winnerIndex * anglePerSlice - anglePerSlice / 2;

        Animated.timing(rotation, {
            toValue: (360 * 5 + winnerAngle) + 135, // Spins 5 full rotations plus the winner angle
            duration: 3000,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
        }).start(() => {
            setSelectedPrize(items[winnerIndex]);
            console.log('selectedPrize', items[winnerIndex]);
            setRotation(new Animated.Value(winnerAngle + 135)); // Reset rotation
            // rotationRef.current.setValue(winnerAngle + 135);
            // setRotation(new Animated.Value(0)); // Reset rotation
            // wonPrize(items[winnerIndex].id)
            setModalVisible(true)
            stopIndicatorAnimation()
        });
    };

    const interpolateRotation = rotation.interpolate({
        inputRange: [0, 360],
        outputRange: ["0deg", "360deg"],
    });

    // Start the indicator animation
    const startIndicatorAnimation = () => {
        Animated.loop(
            Animated.sequence([
                Animated.timing(indicatorXValue, {
                    toValue: 25, // Move 20 pixels to the right
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(indicatorXValue, {
                    toValue: -25, // Move 20 pixels to the left
                    duration: 300,
                    useNativeDriver: true,
                }),
            ])
        ).start();
    };

    // Stop the indicator animation
    const stopIndicatorAnimation = () => {
        indicatorXValue.stopAnimation();
        indicatorXValue.setValue(0); // Reset position
    };



    const indicatorRotation = indicatorXValue.interpolate({
        inputRange: [0, 360],
        outputRange: ['0deg', '80deg'],
    });

    const centerX = 150;
    const centerY = 150;
    const radiusOuter = screenWidth / 2;
    const radiusInner = screenWidth / 2.1;

    // Function to generate a circular path using SVG Arc Command
    const createCirclePath = (cx, cy, r) => {
        return `
            M ${cx + r}, ${cy} 
            A ${r},${r} 0 1,1 ${cx - r},${cy} 
            A ${r},${r} 0 1,1 ${cx + r},${cy} 
          `;
    };


    return (
        <ImageBackground source={require('../images/winBackground.jpg')} style={{ width: screenWidth, height: '100%', resizeMode: 'cover', alignSelf: 'center', }} >
            <View style={[styles.container, {}]}>
                <Image source={require('../images/allWin.png')} style={{ width: screenWidth / 1.2, height: screenHeight / 10, resizeMode: 'contain', marginTop: -screenHeight / 10, marginBottom: screenHeight / 15 }} />

                <Pressable
                    onPress={() => {
                        props.navigation.push('Home');
                        setRotation(new Animated.Value(0)); // Reset rotation
                        // rotationRef.current.setValue(0);
                    }}
                    style={{
                        position: 'absolute', top: 50, right: 5,
                        // height: screenHeight / 12, width: screenHeight / 12, borderRadius: screenHeight / 24, 
                        // backgroundColor: Black,
                        zIndex: 10000,
                        alignItems: 'center', justifyContent: 'center'
                    }}>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 20, color: White }}>{strings('lang.Skip')}</Text>

                </Pressable>
                <View style={{
                    position: 'absolute', top: screenHeight / 2.45,
                    height: screenHeight / 10, width: screenHeight / 10, borderRadius: screenHeight / 20, backgroundColor: White,
                    zIndex: 10000,
                    alignItems: 'center', justifyContent: 'center'
                }}>
                    <Image source={require('../images/Group10.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain' }} />
                </View>

                <View style={{
                    position: 'absolute', top: screenHeight / 2.45,
                    height: screenHeight / 10, width: screenHeight / 10, borderRadius: screenHeight / 20, backgroundColor: White,
                    zIndex: 10000,
                    alignItems: 'center', justifyContent: 'center'
                }}>
                    <Image source={require('../images/Group10.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain' }} />
                </View>
                <Animated.View
                    style={[
                        styles.indicator,
                        { transform: [{ rotate: indicatorRotation }] },
                    ]}
                    elevation={10}
                >
                    <View style={styles.triangle}
                        elevation={10}

                    />
                </Animated.View>


                <Animated.View
                    style={{
                        transform: [{ rotate: interpolateRotation }],
                    }}
                >
                    <View style={{
                        position: 'absolute', alignSelf: 'center', top: -10, elevation: 10,
                        shadowColor: '#000000',
                        shadowOffset: {
                            width: 0,
                            height: 0
                        },
                        shadowRadius: 20,
                        shadowOpacity: 1.0
                    }}>
                        <Svg width={320} height={320} viewBox="0 0 300 300">

                            <G rotation={-45} origin="150,150" stroke={'#9c8113'} strokeWidth={0}>

                                {items.length > 0 &&
                                    items.map((item, index) => {
                                        const angle = (360 / items.length) * index;
                                        const nextAngle = (360 / items.length) * (index + 1);
                                        const largeArcFlag = nextAngle - angle > 180 ? 1 : 0;
                                        const x1 = 150 + 150 * Math.cos((Math.PI / 180) * angle);
                                        const y1 = 150 + 150 * Math.sin((Math.PI / 180) * angle);
                                        const x2 = 150 + 150 * Math.cos((Math.PI / 180) * nextAngle);
                                        const y2 = 150 + 150 * Math.sin((Math.PI / 180) * nextAngle);

                                        return (
                                            <Path
                                                key={item.id}
                                                d={`M150,150 L${x1},${y1} A150,150 0 ${largeArcFlag},1 ${x2},${y2} Z`}
                                                fill={'#9c8113'}
                                            />
                                        );
                                    })}
                            </G>
                        </Svg>
                    </View>
                    <Svg width={300} height={300} viewBox="0 0 300 300">
                        <G rotation={-65} origin="150,150" stroke={'#9c8113'} strokeWidth={1}>
                            {items.length > 0 &&
                                items.map((item, index) => {
                                    const angle = (360 / items.length) * index;
                                    const nextAngle = (360 / items.length) * (index + 1);
                                    const largeArcFlag = nextAngle - angle > 180 ? 1 : 0;
                                    const x1 = 150 + 150 * Math.cos((Math.PI / 180) * angle);
                                    const y1 = 150 + 150 * Math.sin((Math.PI / 180) * angle);
                                    const x2 = 150 + 150 * Math.cos((Math.PI / 180) * nextAngle);
                                    const y2 = 150 + 150 * Math.sin((Math.PI / 180) * nextAngle);

                                    return (
                                        <Path
                                            key={item.id}
                                            d={`M150,150 L${x1},${y1} A150,150 0 ${largeArcFlag},1 ${x2},${y2} Z`}
                                            fill={DarkYellow}
                                        />
                                    );
                                })}
                        </G>

                        {items.length > 0 &&
                            items.map((item, index) => {
                                const angle = ((360 / items.length) * (index + 0.5)) + 245;
                                const x = 150 + 100 * Math.cos((Math.PI / 180) * angle);
                                const y = 150 + 100 * Math.sin((Math.PI / 180) * angle);
                                return (
                                    <>


                                        <View
                                            key={item.id}
                                            style={{
                                                position: 'absolute',
                                                top: y - 19,
                                                right: x - 50,
                                                width: 100, // Adjust width to fit text
                                                height: 40, // Adjust height to fit text
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                transform: [
                                                    { rotate: `${angle + 90}deg` }], // Rotate text
                                            }}
                                        >

                                            {item.id == 1 ?

                                                <Image source={
                                                    require('../images/1.png')
                                                } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                                :
                                                item.id == 2 ?
                                                    <Image source={
                                                        require('../images/2.png')
                                                    } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                                    :
                                                    item.id == 3 ?
                                                        <Image source={
                                                            require('../images/3.png')
                                                        } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                                        :
                                                        item.id == 4 ?
                                                            <Image source={
                                                                require('../images/4.png')
                                                            } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                                            :
                                                            item.id == 5 ?
                                                                <Image source={
                                                                    require('../images/5.png')
                                                                } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                                                :
                                                                item.id == 6 ?
                                                                    <Image source={
                                                                        require('../images/6.png')
                                                                    } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                                                    :

                                                                    <Image source={
                                                                        require('../images/7.png')
                                                                    } style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                                            }
                                            <Text
                                                numberOfLines={2}
                                                style={{
                                                    fontSize: item.id == 1 ? screenWidth / 48 : screenWidth / 38,
                                                    fontFamily: appFontBold,
                                                    color: Black,
                                                    textAlign: 'center',
                                                }}>
                                                {
                                                    item.name
                                                }
                                            </Text>
                                        </View>
                                    </>
                                );
                            })}
                    </Svg>
                </Animated.View>

                <TouchableOpacity onPress={spinWheel} style={[styles.button, { marginTop: '10%' }]}>
                    <Text style={styles.buttonText}>{'لف عجلة الحظ'}</Text>
                </TouchableOpacity>

                <Modal
                    transparent={true}
                    animationType='slide'
                    visible={modalVisible}
                    propagateSwipe={true}
                >


                    <View style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, minHeight: screenHeight }} >
                    </View>

                    <View style={[styles.modal]}>

                        <LinearGradient colors={['#d89d2a', '#e4c681']} style={[styles.modalView]}>
                            <Text style={[styles.buttonText, { fontSize: screenWidth / 15 }]}>{strings('lang.youwin')}</Text>
                            {selectedPrize ?

                                selectedPrize.id == 1 ?

                                    <Image source={
                                        require('../images/1.png')
                                    } style={{ marginRight: 10, resizeMode: 'contain', width: '40%', height: '40%', }} />
                                    :
                                    selectedPrize.id == 2 ?
                                        <Image source={
                                            require('../images/2.png')
                                        } style={{ marginRight: 10, resizeMode: 'contain', width: '40%', height: '40%', }} />
                                        :
                                        selectedPrize.id == 3 ?
                                            <Image source={
                                                require('../images/3.png')
                                            } style={{ marginRight: 10, resizeMode: 'contain', width: '40%', height: '40%', }} />
                                            :
                                            selectedPrize.id == 4 ?
                                                <Image source={
                                                    require('../images/4.png')
                                                } style={{ marginRight: 10, resizeMode: 'contain', width: '40%', height: '40%', }} />
                                                :
                                                selectedPrize.id == 5 ?
                                                    <Image source={
                                                        require('../images/5.png')
                                                    } style={{ marginRight: 10, resizeMode: 'contain', width: '40%', height: '40%', }} />
                                                    :
                                                    selectedPrize.id == 6 ?
                                                        <Image source={
                                                            require('../images/6.png')
                                                        } style={{ marginRight: 10, resizeMode: 'contain', width: '40%', height: '40%', }} />
                                                        :
                                                        <Image source={
                                                            require('../images/7.png')
                                                        } style={{ marginRight: 10, resizeMode: 'contain', width: '40%', height: '40%', }} />
                                :
                                <>
                                </>
                            }
                            <Text style={[styles.buttonText, { fontSize: screenWidth / 25, color: White, fontFamily: appFontBold }]}>{selectedPrize &&
                                selectedPrize.name
                            }</Text>

                            <Pressable onPress={() => {
                                setModalVisible(false);
                                setRotation(new Animated.Value(0)); // Reset rotation
                                // rotationRef.current.setValue(0);
                                props.navigation.push('Home');
                            }} style={[styles.button, { zIndex: 100, width: screenWidth / 2 }]}>
                                <Text style={styles.buttonText}>{strings('lang.congratulations')}</Text>
                            </Pressable>
                        </LinearGradient>

                    </View>

                </Modal>
            </View>
        </ImageBackground>
    );
};

const styles = StyleSheet.create({
    modal: {
        alignItems: 'center',
        width: screenWidth / 1.1,
        height: screenHeight / 2.8,
        position: 'absolute',
        top: screenHeight / 3.8,
        alignSelf: 'center',
        backgroundColor: DarkYellow,
        borderRadius: 20,
        // paddingVertical: '2%',
        justifyContent: 'center',
        padding: '2%',
    },
    modalView: {
        alignItems: 'center',
        width: '100%',
        height: '100%',
        alignSelf: 'center',
        // backgroundColor: DarkYellow,
        borderRadius: 20,
        justifyContent: 'center',
        zIndex: 10000,
        shadowColor: '#000000',
        shadowOffset: {
            width: 0,
            height: 0
        },
        shadowRadius: 12,
        shadowOpacity: 1.0,
        elevation: 10
    },
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'black'
    },
    indicator: {
        position: 'absolute',
        top: I18nManager.isRTL ? screenHeight / 4 : screenHeight / 3.45,
        zIndex: 10000,
        shadowColor: '#000000',
        shadowOffset: {
            width: 0,
            height: 0
        },
        shadowRadius: 12,
        shadowOpacity: 1.0,
        elevation: 10
    },
    triangle: {
        width: 0,
        height: 0,
        borderLeftWidth: 15,
        borderRightWidth: 15,
        borderBottomWidth: 30,
        borderLeftColor: 'transparent',
        borderRightColor: 'transparent',
        borderBottomColor: DarkBlue,
        elevation: 10,
        transform: [{ rotate: '180deg' }],
        elevation: 10

    },
    resultText: {
        marginTop: 20,
        fontSize: 18,
        fontWeight: 'bold',
        position: 'absolute'
    },

    container: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
    button: {
        // marginTop: 20,
        paddingVertical: 20,
        width: screenWidth / 1.4, alignItems: 'center', justifyContent: 'center',
        backgroundColor: DarkBlue,
        borderRadius: 100,
        borderColor: White,
        borderWidth: 5,

    },
    buttonText: {
        color: "#fff",
        fontFamily: appFontBold,
        fontSize: screenWidth / 20
    },
    resultText: {
        marginTop: 20,
        fontSize: 18,
        fontFamily: appFontBold,
    },
});
export default Prizes;
