import React, { Component, useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  StyleSheet,
  I18nManager,
  TouchableOpacity,
  Platform,
  Pressable,
  Modal,
  BackHandler,
} from 'react-native';
import {
  appFont,
  appFontBold,
  Green,
  WhiteGreen,
  screenWidth,
  White,
  DarkGreen,
  Blue,
  MediumGrey,
  DarkGrey,
  WhiteGery,
  Black,
  screenHeight,
  Red,
  appColor1,
  DarkYellow,
  Grey,
  Gold,
  Green2,
  MediumGreen,
  DarkBlue,
} from '../components/Styles';
import { Button } from 'native-base';
import Header from '../components/Header';
import { FlatList, ScrollView, TextInput } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import * as generalAction from '../../Store/Actions/addresses';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as addressesActions from '../../Store/Actions/addresses';
import Loading from '../components/Loading';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import AddressContainer from '../components/AddressContainer';
import RBSheet from 'react-native-raw-bottom-sheet';

// import Model from './Model';
// import Loading from '../components/Loading';
const Search = props => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const ref = useRef(null);
  const [siteId, setSiteId] = useState(null)
  const [recentAddress, setRecentAddress] = useState([])
  const [favouriteAddress, setFavouriteAddress] = useState([])
  const [savedPlaces, setSavedPlaces] = useState([])
  const [modalVisible, setModalVisible] = useState(false);
  const [isFav, setIsFav] = useState(false);
  const [search, setSearch] = useState('');
  const [labelError, setLabelError] = useState('');
  const [label, setLabel] = useState('');
  const [activeLabel, setActiveLabel] = useState(false);
  const [addressId, setAddressId] = useState(0);
  const isFocused = useIsFocused();
  const refRbSheet = useRef();

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        props.navigation.goBack()
        return true;
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );

  useEffect(() => {

    const getAddresses = async () => {
      let favAddress = []
      setLoading(true)
      try {
        let response = await dispatch(addressesActions.getAddresses());
        if (response.success == true) {
          favAddress = response.data.favorites
          setRecentAddress(response.data.recent);
          setFavouriteAddress(favAddress);
          setSavedPlaces(response.data.saved);
        }
        else {
          if (response.message) {
            // Toaster(
            //   'top',
            //   'danger',
            //   Red,
            //   response.message,
            //   White,
            //   1500,
            //   screenHeight / 15,
            // );
          }
        }
        setLoading(false);
      }
      catch (err) {
        console.log('err', err)
        setLoading(false);
      }
    };

    getAddresses();
  }, [isFocused]);

  const getAddresses = async () => {
    let favAddress = []
    setLoadingMore(true)
    try {
      let response = await dispatch(addressesActions.getAddresses());
      if (response.success == true) {
        favAddress = response.data.favorites
        setRecentAddress(response.data.recent);
        setFavouriteAddress(favAddress);
        setSavedPlaces(response.data.saved);
      }
      else {
        if (response.message) {
          // Toaster(
          //   'top',
          //   'danger',
          //   Red,
          //   response.message,
          //   White,
          //   1500,
          //   screenHeight / 15,
          // );
        }
      }
      setLoadingMore(false);
    }
    catch (err) {
      console.log('err', err)
      setLoadingMore(false);
    }
  };

  const favoriteAddress = async (id) => {
    setLoadingMore(true)
    try {
      let favoriteAddress = await dispatch(addressesActions.favoriteAddress(id));
      if (favoriteAddress.success == true) {
        Toaster(
          'top',
          'success',
          'green',
          (strings('lang.Login_Successfuly')),
          White,
          1500,
          screenHeight / 15,
        );
        getAddresses();
        setLoadingMore(false)
      }
      else {
        Toaster(
          'top',
          'danger',
          Red,
          favoriteAddress.message,
          White,
          1500,
          screenHeight / 15,
        );
        setLoadingMore(false)
      }
    }
    catch (err) {
      console.log('err', err)
      setLoadingMore(false);
    }
  };

  const saveAddress = async () => {
    setLoadingMore(true)
    try {
      let saveAddress = await dispatch(addressesActions.saveAddress(addressId, label));
      if (saveAddress.success == true) {
        Toaster(
          'top',
          'success',
          'green',
          (strings('lang.Login_Successfuly')),
          White,
          1500,
          screenHeight / 15,
        );
        getAddresses();
        setLabel('')
        setLoadingMore(false)
      }
      else {
        Toaster(
          'top',
          'danger',
          Red,
          saveAddress.message,
          White,
          1500,
          screenHeight / 15,
        );
        setLoadingMore(false)
      }
    }
    catch (err) {
      console.log('err', err)
      setLoadingMore(false);
    }
  };

  if (loading) {
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.destination')}
          drawerPress={() => {
            props.navigation.navigate('More');
          }}
          backPress={() => {
            props.navigation.goBack();
          }}
        // HideMore
        />

        <ScrollView
          style={{ width: '100%', height: '100%', marginBottom: '2%' }}
          showsVerticalScrollIndicator={false}
        >

          {[{ height: 20 }, { height: 10 }, { height: 10 }, { height: 10 }, { height: 10 }, { height: 10 }, { height: 10 }].map((item) => {
            return <View style={{ width: '90%', height: screenHeight / item.height, backgroundColor: WhiteGery, alignSelf: "center", marginTop: "5%" }} >
              <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                <View style={{ width: '100%', height: '100%', backgroundColor: WhiteGery, }} />
              </SkeletonPlaceholder>
            </View>
          })}


          <View style={{ height: screenHeight / 10 }}></View>
        </ScrollView>
        <Footer current={'Search'} navigation={props.navigation} />
      </View>
    );
  }
  else {
    let screen = props.route.params.screen
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.destination')}
          // drawerPress={() => {
          //   props.navigation.navigate('More');
          // }}
          backPress={() => {
            props.navigation.goBack();
          }}
        />
        {loadingMore ? <Loading /> : <></>}

        <ScrollView
          style={{ width: '100%', height: '100%', }}
          showsVerticalScrollIndicator={false}
          stickyHeaderIndices={[1]} stickyHeaderHiddenOnScroll={true}
        >

          {screen == 'Home' ?
            <></>
            :

            <TouchableOpacity
              onPress={() => { props.navigation.navigate('Search', { screen: props.route.params.screen, source: props.route.params.source, pending: false }) }}
              style={{
                width: screenWidth, backgroundColor: WhiteGery, alignSelf: 'center', height: screenHeight / 20, alignItems: 'center', marginBottom: '2%',
                justifyContent: 'center',
                // backgroundColor: MediumGrey,
                flexDirection: 'row'
              }}>
              <View style={{ flexDirection: 'row', height: '100%', alignItems: 'center', justifyContent: 'center', alignSelf: 'center', }}>
                <Image source={require('../images/lightlocation.png')} style={styles.locationImage} />
                <Text numberOfLines={1} style={styles.text}>{strings('lang.Choosethelocationonthemap')}</Text>
              </View>
            </TouchableOpacity>
          }

          {savedPlaces.length == 0

            ?
            <></>
            :
            <>
              <View style={[styles.searchContainer, {}]}>
                <Text numberOfLines={1} style={styles.textblack}>{strings('lang.savedplaces')}</Text>

                {savedPlaces.map((item, index) => {
                  return (
                    <View style={{ flexDirection: 'row', maxHeight: screenHeight / 10, width: '100%', borderBottomWidth: index >= savedPlaces.length - 1 ? 0 : 1, alignItems: 'flex-start', justifyContent: 'space-between', borderBottomColor: WhiteGery }}>
                      {item.label.toUpperCase().includes('HOME') || (item.label.includes('منزل'))
                        ? <TouchableOpacity style={{ width: '8%', }}>
                          <Image source={require('../images/Group10486.png')} style={[styles.image, { alignSelf: 'center' }]} />
                        </TouchableOpacity>
                        :
                        <></>}

                      <TouchableOpacity
                        onPress={() => { if (screen != 'Home') { setSiteId(item.id); props.navigation.navigate(props.route.params.screen, { source: props.route.params.source, addressDesription: item.address, lat: item.lat, lng: item.lng, addressLabel: item.label, addressId: item.id, pending: false }) } }}
                        style={{ width: '75%', alignItems: 'flex-start' }}>
                        <Text numberOfLines={1} style={styles.textblack1}>{item.label}</Text>
                        <Text numberOfLines={1} style={styles.textgrey}>{item.address}</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => { refRbSheet.current.open(); setLabel(item.label); setAddressId(item.id) }}
                        // onPress={() => { setModalVisible(!modalVisible); setAddressId(item.id) }}
                        style={{ width: '12%', alignItems: 'flex-end', height: '100%', justifyContent: 'center' }}>
                        <View style={{ alignItems: 'center', justifyContent: 'center', backgroundColor: Gold, width: screenWidth / 12, height: screenWidth / 12, borderRadius: screenWidth / 24, }}>
                          <Image source={require('../images/edit.png')} style={[styles.image, { tintColor: White, alignSelf: 'center' }]} />
                        </View>
                      </TouchableOpacity>
                    </View>
                  )
                })}
              </View>

              <View style={{ height: screenHeight / 100, width: '100%', backgroundColor: WhiteGery }}></View>
            </>
          }

          {
            recentAddress.length == 0
              ?

              <></>
              :
              <>
                <View style={[styles.searchContainer, {}]}>
                  <Text numberOfLines={1} style={styles.textblack}>{strings('lang.Recentlyaccessedsites')}</Text>
                  <FlatList
                    data={recentAddress}
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item, index }) =>
                      <AddressContainer
                        screen={screen}
                        item={item}
                        addressPress={() => { props.navigation.navigate(props.route.params.screen, { source: props.route.params.source, addressDesription: item.address, lat: item.lat, lng: item.lng, addressLabel: item.label, addressId: item.id, pending: false }) }}
                        favPress={() => { favoriteAddress(item.id) }}
                        modalPress={() => { refRbSheet.current.open(); setAddressId(item.id) }}
                      // modalPress={() => { setModalVisible(!modalVisible); setAddressId(item.id) }}
                      />
                    }
                    // keyExtractor={item => item.id}
                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth / 1.05, }}
                  />
                </View>

                <View style={{ height: screenHeight / 100, width: '100%', backgroundColor: WhiteGery }}></View>
              </>
          }

          {favouriteAddress.length == 0
            ?
            <></>
            :
            <>
              <View style={styles.searchContainer}>
                <Text numberOfLines={1} style={styles.textblack}>{strings('lang.Favoritesites')}</Text>

                <FlatList
                  data={favouriteAddress}
                  showsVerticalScrollIndicator={false}
                  renderItem={({ item, index }) =>
                    <AddressContainer
                      screen={screen}
                      item={item}
                      addressPress={() => { props.navigation.navigate(props.route.params.screen, { source: props.route.params.source, addressDesription: item.address, lat: item.lat, lng: item.lng, addressLabel: item.label, addressId: item.id, pending: false }) }}
                      favPress={() => { favoriteAddress(item.id) }}
                      modalPress={() => { refRbSheet.current.open(); setAddressId(item.id) }}
                    // modalPress={() => { setModalVisible(!modalVisible); setAddressId(item.id) }}
                    />
                  }
                  // keyExtractor={item => item.id}
                  style={{ alignSelf: "center", overflow: "hidden", width: screenWidth / 1.05, }}
                />

                {/* </ScrollView> */}
              </View>


            </>
          }

        </ScrollView>
        <View style={{ height: screenHeight / 60 }}></View>
        {/* <Footer current=s{'Search'} navigation={props.navigation} /> */}

        <RBSheet
          ref={refRbSheet}
          height={screenHeight / 3}
          openDuration={280}
          customStyles={{
            container: {
              // justifyContent: "center",
              alignItems: 'center',
              borderTopRightRadius: 30,
              borderTopLeftRadius: 30,
            },
            wrapper: {},
            draggableIcon: {
              width: '25%',
              backgroundColor: DarkGrey,
            },
          }}
        // closeOnDragDown={true}
        >
          <View style={{ width: '90%', alignSelf: 'center', }}>
            <View
              style={{
                flexDirection: 'row',
                width: '100%',
                height: screenHeight / 20,
                alignSelf: 'flex-end',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >

              <Button
                onPress={() => {
                  refRbSheet.current.close();
                  setLabel('')
                }}
                style={{
                  width: screenWidth / 15,
                  height: screenWidth / 15,
                  alignItems: 'center',
                  alignSelf: 'center',
                  justifyContent: 'center',
                  backgroundColor: White,
                  elevation: 0,
                }}
              >
                <Image source={require('../images/xx.png')} style={{ resizeMode: 'contain', width: '75%', height: '75%' }} />
              </Button>
            </View>

            <Text style={{ ...styles.labelModal, ...{ alignSelf: 'center', marginStart: '0%', fontSize: screenWidth / 30, marginBottom: '3%', color: DarkBlue } }}>
              {strings('lang.addressMessage')}
            </Text>

            <View style={styles.inputModal}>
              <TextInput
                placeholderTextColor={'#707070'}
                placeholder={strings('lang.addressMessage')}
                style={[styles.inputTextModal, { borderColor: activeLabel ? DarkBlue : MediumGrey, borderRadius: 10, }]}
                // keyboardType='ascii-capable'
                value={label}
                onChangeText={text => {
                  setLabel(text);
                  setLabelError('');
                }}
                onFocus={() => setActiveLabel(true)}
                onBlur={() => {
                  setActiveLabel(false);
                }}
              />
            </View>
            {labelError ?
              <Text style={styles.loginError}>{labelError}</Text>
              :
              <View style={{ height: 0 }}></View>
            }

            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: '5%' }}>
              <Button
                style={{
                  width: '47%',
                  alignSelf: 'center',
                  height: 45,
                  backgroundColor: Red,
                  alignItems: 'center',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  borderRadius: 10,
                  flexDirection: 'row',
                }}
                onPress={() => {
                  if (label.length != 0) {
                    refRbSheet.current.close();
                    saveAddress()
                  }
                }}
              >
                <Text
                  style={{
                    fontSize: screenWidth / 28,
                    fontFamily: appFontBold,
                    color: White,
                  }}
                >
                  {strings('lang.Yes')}
                </Text>
              </Button>
              <Button
                style={{
                  width: '47%',
                  alignSelf: 'center',
                  height: 45,
                  backgroundColor: DarkBlue,
                  alignItems: 'center',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  borderRadius: 10,
                  flexDirection: 'row',
                }}
                onPress={() => {
                  refRbSheet.current.close();
                  setLabel('')
                }}
              >
                <Text
                  style={{
                    fontSize: screenWidth / 28,
                    fontFamily: appFontBold,
                    color: White,
                  }}
                >
                  {strings('lang.No')}
                </Text>
              </Button>
            </View>


          </View>

        </RBSheet>

        <Modal
          transparent={true}
          animationType="fade"
          visible={modalVisible}
          propagateSwipe={true}
        >

          <ScrollView style={{ zIndex: 1000000, width: screenWidth, minHeight: screenHeight }}>

            {/* <View style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight / 14 + 2 * screenHeight / 5.5, minHeight: screenHeight }} > */}
            <View style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, minHeight: screenHeight }} >
            </View>

            <View style={[styles.modal]}>

              <Text numberOfLines={1} style={[styles.textblack1, { alignSelf: 'flex-start' }]}>{strings('lang.addressMessage')}</Text>
              <View style={styles.inputModal}>
                <TextInput
                  placeholderTextColor={'#707070'}
                  placeholder={strings('lang.addressMessage')}
                  style={[styles.inputTextModal, { borderColor: activeLabel ? DarkBlue : MediumGrey }]}
                  // keyboardType='ascii-capable'
                  value={label}
                  onChangeText={text => {
                    setLabel(text);
                    setLabelError('');
                  }}
                  onFocus={() => setActiveLabel(true)}
                  onBlur={() => {
                    setActiveLabel(false);
                  }}
                />
              </View>
              {labelError ?
                <Text style={styles.loginError}>{labelError}</Text>
                :
                <View style={{ height: 0 }}></View>
              }
              <View style={{ height: screenHeight / 20, flexDirection: 'row-reverse', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '100%', }}>
                <Button transparent style={styles.modalbuttonContainer2} onPress={() => { setModalVisible(!modalVisible) }}>
                  <Text style={styles.modalbuttonText2}>{strings('lang.cancel')}</Text>
                </Button>
                <Button transparent style={styles.modalbuttonContainer} onPress={() => { if (label.length != 0) { setModalVisible(!modalVisible); saveAddress() } }}>
                  <Text style={styles.modalbuttonText}>{strings('lang.Save')}</Text>
                </Button>
              </View>
            </View>

            {/* <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'center',
                alignSelf: 'center',
                paddingBottom: screenHeight / 30,
                alignItems: 'center',
                backgroundColor: '#7F7F7F',
                position: 'absolute',
                bottom: 0
                // backgroundColor: '#000', opacity: 0.5,
              }}
            >
              <Button
                onPress={() => { setModalVisible(!modalVisible); }}
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '95%',
                  height: screenHeight / 18,
                  backgroundColor: appColor1,
                  borderRadius: 100,
                }}
              >
                <Text
                  style={{
                    fontFamily: appFontBold,
                    fontSize: screenWidth / 25,
                    color: White,
                  }}
                >
                  {strings('lang.Tripcancellation')}
                </Text>
              </Button>
            </View> */}

          </ScrollView>



        </Modal>

      </View >
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: 'white',
  },
  inputModal: {
    height: screenHeight / 18,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    marginBottom: 10,
    backgroundColor: White,
    alignSelf: 'center',
  },
  inputTextModal: {
    color: Black,
    width: '100%',
    fontFamily: appFontBold,
    fontSize: screenWidth / 35,
    alignSelf: 'center',
    textAlign: I18nManager.isRTL ? 'right' : 'left',
    borderWidth: 1,
    borderColor: MediumGrey,
    borderRadius: 15,
    height: '100%', paddingHorizontal: 10
  },
  loginError: {
    color: 'red', fontFamily: appFont, alignSelf: 'flex-start', fontSize: screenWidth / 35, marginStart: '25%'
  },
  modal: {
    alignItems: 'center',
    width: screenWidth / 1.1,
    height: screenHeight / 5,
    position: 'absolute',
    top: screenHeight / 2.8,
    alignSelf: 'center',
    backgroundColor: White,
    borderRadius: 20,
    paddingVertical: '2%',
    justifyContent: 'space-between',
    paddingHorizontal: '5%',
  },
  modalbuttonContainer: {
    height: screenHeight / 25,
    width: '60%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: DarkBlue,
    alignSelf: 'center',
    borderRadius: 100
  },
  modalbuttonContainer2: {
    height: screenHeight / 25,
    width: '35%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: WhiteGery,
    alignSelf: 'center',
    borderRadius: 100
  },
  modalbuttonText: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 36, color: White
  },
  modalbuttonText2: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 36, color: Black
  },

  section: { width: '90%', alignSelf: 'center' },
  labelContanier: {
    width: '100%',
    marginVertical: '3%',
    alignItems: 'flex-start',
  },
  searchContainer: {
    // maxHeight: screenHeight / 2.75,
    width: '95%',
    // backgroundColor: Black,
    alignSelf: 'center',
    marginBottom: '2%'
  },
  textContanier: {
    width: '100%',
    marginVertical: '2%',
    alignItems: 'flex-start',
  },
  label: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 20, marginTop: screenHeight / 50 },
  body: {
    fontFamily: appFontBold,
    color: DarkGrey,
    fontSize: screenWidth / 30,
    alignSelf: I18nManager.isRTL ? 'flex-start' : 'flex-end',
  },
  imageContainer: {
    flex: 1,
    marginBottom: Platform.select({ ios: 0, android: 1 }), // Prevent a random Android rendering issue
    backgroundColor: White,
    borderRadius: 0,
    justifyContent: 'center',
    borderWidth: 0.8,
    borderColor: MediumGrey,
    // overflow:'hidden'
  },
  catActive: {
    height: 33,
    alignSelf: 'center',
    backgroundColor: appColor1,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: screenWidth / 8,
    marginEnd: 10,
    paddingHorizontal: 10
  },
  catUnactive: {
    height: 33,
    alignSelf: 'center',
    backgroundColor: WhiteGery,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: screenWidth / 8,
    marginEnd: 10,
    paddingHorizontal: 10
  },
  textActive: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 28, },
  textblack: { fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, alignSelf: 'flex-start', marginVertical: 10 },
  textblack1: { fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' },
  textgrey: { fontFamily: appFontBold, fontSize: screenWidth / 30, color: MediumGrey },
  text: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 30, alignSelf: 'center' },
  locationImage: {
    resizeMode: 'contain',
    width: '10%',
    height: '40%',
    marginEnd: 5,
    tintColor: DarkGrey
  },
  imagefav: {
    resizeMode: 'contain',
    width: '60%',
    height: '60%',
    tintColor: DarkYellow,
    alignSelf: 'flex-end',

  },
  image: {
    resizeMode: 'contain',
    width: '60%',
    height: '60%',
    // tintColor: Green,
    alignSelf: 'flex-end',

  },
  inputText: {
    color: DarkGrey,
    width: '85%',
    fontFamily: appFontBold,
    fontSize: screenWidth / 30,
    textAlign: Platform.OS == 'ios' ? I18nManager.isRTL ? 'right' : 'left' : null,
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    paddingHorizontal: 5,
  },
  labelModal: { color: Black, fontSize: screenWidth / 26, fontFamily: appFontBold, alignSelf: 'center', marginStart: '13%' },

});

export default Search;
