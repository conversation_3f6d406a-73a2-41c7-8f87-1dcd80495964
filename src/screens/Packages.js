import { useFocusEffect } from '@react-navigation/native';
import { Button } from 'native-base';
import React, { useEffect, useState } from 'react';
import {
    BackHandler,
    RefreshControl,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useDispatch } from 'react-redux';
import * as packagesActions from '../../Store/Actions/packages';
import Footer from '../components/Footer';
import Header from '../components/Header';
import {
    appFontBold,
    Black,
    DarkBlue,
    DarkGreen,
    Grey,
    MediumGrey,
    Red,
    screenHeight,
    screenWidth,
    <PERSON>,
    WhiteGery
} from '../components/Styles';
import Toaster from '../components/Toaster';
import { strings } from './i18n';


const Packages = props => {
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [loadingPackages, setLoadingPackages] = useState(false);
    const [subscribeId, setSubscribeId] = useState(false)
    const [packages, setPackages] = useState([]);
    const [noResault, setNoResault] = useState('');
    const [page, setPage] = useState(0);
    const [lastPage, setLastPage] = useState(0);
    const [Reload, setReload] = useState(false);

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                props.navigation.goBack()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    useEffect(() => {
        const getMyPackages = async () => {
            setLoading(true)
            try {
                let response = await dispatch(packagesActions.getMyPackages(1));
                if (response.success == true) {
                    if (response.data.items.length == 0) {
                        setNoResault(strings('lang.No_Results'));
                    } else {
                        setNoResault('');
                        setPackages(response.data.items)
                        setPage(1)
                        setLastPage(response.data.last_page)
                    }
                }
                else {
                    if (response.message) {
                        // Toaster(
                        //     'top',
                        //     'danger',
                        //     Red,
                        //     response.message,
                        //     White,
                        //     1500,
                        //     screenHeight / 15,
                        // );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getMyPackages();
    }, []);

    const onRefresh = async () => {
        setLoading(true)
        try {
            let response = await dispatch(packagesActions.getMyPackages(1));
            if (response.success == true) {
                if (response.data.items.length == 0) {
                    setNoResault(strings('lang.No_Results'));
                } else {
                    setNoResault('');
                    setPackages(response.data.items)
                    setPage(1)
                    setLastPage(response.data.last_page)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const LoadMore = async () => {
        console.log('page', page);
        console.log('lastpage', lastPage);
        if (page < lastPage) {
            setLoadingPackages(true)
            try {
                let response = await dispatch(packagesActions.getMyPackages(page + 1));
                if (response.success == true) {
                    setPackages([...packages, ...response.data.items])
                    setPage(page + 1);
                    setLastPage(response.data.last_page)
                    setLoadingPackages(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingPackages(false);
                }
            } catch (err) {
                setLoadingPackages(false);
            }

        }
        else {
        }
    }


    if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.socialist1')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    {[{}, {}, {}, {}, {}, {}].map((item) => {
                        return <View style={styles.container}>
                            <View
                                style={{
                                    width: '50%',
                                    height: screenHeight / 45,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '3%',
                                    alignSelf: "flex-start",
                                    marginRight: "3%"
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>

                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 30,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '3%',
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>

                            <View
                                style={{
                                    width: '90%',
                                    height: screenHeight / 50,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '3%',
                                    alignSelf: "center",
                                    marginTop: "3%"
                                    //  borderRadius:'5%'
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>


                        </View>
                    })}
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    } else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.socialist1')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                {
                    noResault ? (
                        <Text
                            style={{
                                fontSize: screenWidth / 18,
                                fontFamily: appFontBold,
                                color: Red,
                                marginTop: screenHeight / 5,
                                alignSelf: 'center',
                            }}
                        >
                            {noResault}
                        </Text>
                    ) : (
                        <View></View>
                    )
                }
                {/* <View style={{
                    width: '95%',
                    borderColor: Grey,
                    borderWidth: 1,
                    borderRadius: 15,
                    marginVertical: 10,
                    alignSelf: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    paddingHorizontal: 10,
                    paddingVertical: '5%'

                }}>
                    <View style={{
                        alignItems: 'center',
                        flexDirection: 'row',
                        justifyContent: 'center',
                    }}>
                        <Text style={{
                            fontFamily: appFontBold,
                            fontSize: screenWidth / 28,
                            color: Black,
                        }}>{strings('lang.newOffer')}</Text>

                        <View
                            style={{
                                width: screenHeight / 40,
                                height: screenHeight / 40,
                                borderRadius: screenHeight / 80,
                                backgroundColor: Red,
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginStart: 10,
                            }}
                        >
                            <Text style={{
                                fontFamily: appFontBold,
                                fontSize: screenWidth / 40,
                                color: White,
                            }}>{'1'}</Text>
                        </View>
                    </View>


                    <View
                        style={{
                            width: '30%',
                            justifyContent: 'center',
                            alignSelf: 'center',
                            alignItems: 'center',
                            height: screenHeight / 20,
                        }}
                    >
                        <Button
                            onPress={() => { }}
                            style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                alignSelf: 'center',
                                width: '95%',
                                height: '100%',
                                backgroundColor: DarkBlue,
                                borderRadius: 100,
                            }}
                        >

                            <Text
                                style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 28,
                                    color: White,
                                }}
                            >
                                {strings('lang.thedetails')}
                            </Text>
                        </Button>
                    </View>
                </View> */}

                <FlatList
                    data={packages}
                    refreshControl={
                        <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                    }
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item, key }) =>
                        <View style={{ width: '95%', padding: 5, alignItems: 'center', justifyContent: 'center', alignSelf: 'center', paddingVertical: screenHeight / 60, marginVertical: '1%', borderWidth: 1, borderColor: Grey, borderRadius: 20 }}>
                            {item.package && item.package.name
                                ?
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, alignSelf: 'flex-start', marginStart: '1%' }}>{item.package && item.package.name} - {item.package && item.package.days} {strings('lang.days')} </Text>
                                :
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: Black, alignSelf: 'flex-start', marginStart: '1%', marginBottom: '2%' }}>{strings('lang.NoAcceptedOfferYet')}</Text>
                            }
                            {item.package && item.package.name
                                ?
                                <></>
                                :
                                <View style={{
                                    alignItems: 'center',
                                    flexDirection: 'row',
                                    justifyContent: 'flex-start',
                                    width: '100%',
                                    marginVertical: 5
                                }}>
                                    <Text style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 28,
                                        color: Black,
                                    }}>{strings('lang.newOffer')}</Text>

                                    <View
                                        style={{
                                            width: screenHeight / 40,
                                            height: screenHeight / 40,
                                            borderRadius: screenHeight / 80,
                                            backgroundColor: Red,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginStart: 10,
                                        }}
                                    >
                                        <Text style={{
                                            fontFamily: appFontBold,
                                            fontSize: screenWidth / 40,
                                            color: White,
                                        }}>{item.offers_count}</Text>
                                    </View>
                                </View>
                            }
                            {item.package && item.package.name
                                &&
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%', alignSelf: 'center', backgroundColor: WhiteGery, alignItems: 'center', marginVertical: '2%', paddingHorizontal: '2%', height: screenHeight / 25, }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, }}> {strings('lang.Status')} </Text>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: DarkGreen, }}>{strings('lang.endsIn')} {item.end_date_formatted} </Text>
                                </View>
                            }
                            <TouchableOpacity
                                onPress={() => { props.navigation.push('MyPackageDetails', { package: item }) }}
                                style={{ height: screenHeight / 25, width: '90%', alignItems: 'center', justifyContent: 'center', backgroundColor: DarkBlue, borderRadius: 20 }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: White, }}> {strings('lang.thedetails')} </Text>
                            </TouchableOpacity>
                        </View>}
                    keyExtractor={item => item.id}
                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth, backgroundColor: White, }}
                    onEndReachedThreshold={0.1}
                    onEndReached={LoadMore}
                />
                {loadingPackages ? <ActivityIndicator size={Platform.OS == 'ios' ? 80 : 40} color={DarkBlue} /> : <></>}

                {/* <ScrollView
                    style={{ width: '100%', }}
                    showsVerticalScrollIndicator={false}
                >
                    {packages.map((item, index) => {
                        return (
                            <View style={{ width: '95%', alignItems: 'center', justifyContent: 'center', alignSelf: 'center', paddingVertical: screenHeight / 60, marginVertical: '1%', borderWidth: 1, borderColor: Grey, borderRadius: 20 }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, alignSelf: 'flex-start', marginStart: '1%' }}> {'اشتراك شهري - 30 يوم'} </Text>
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%', alignSelf: 'center', backgroundColor: WhiteGery, alignItems: 'center', marginVertical: '2%', paddingHorizontal: '2%', height: screenHeight / 25, }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, }}> {'الحالة'} </Text>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Red, }}>{'منتهى'} </Text>
                                </View>
                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('MyPackageDetails') }}
                                    style={{ height: screenHeight / 25, width: '90%', alignItems: 'center', justifyContent: 'center', backgroundColor: DarkBlue, borderRadius: 20 }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: White, }}> {strings('lang.thedetails')} </Text>
                                </TouchableOpacity>
                            </View>
                        )
                    })}
                </ScrollView> */}


                <View style={{ height: screenHeight / 100 }}></View>

                <Footer current={'Packages'} navigation={props.navigation} />
            </View>
        );
    }
};

const styles = StyleSheet.create({

});

export default Packages;
