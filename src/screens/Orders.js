import React, { Component, useEffect, useRef, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    TextInput,
    FlatList,
    RefreshControl,
    ActivityIndicator,
    BackHandler,
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    appColor1,
    MediumGreen,
    DarkYellow,
    DarkBlue,
    Gold,
    WhiteBlue,
} from '../components/Styles';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import OrdersContainer from '../components/OrdersContainer';
import * as cityTripsActions from '../../Store/Actions/cityTrips';
import * as vipTripsActions from '../../Store/Actions/vipTrips';
import * as dailyTripsActions from '../../Store/Actions/dailyTrips';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';


const Orders = props => {
    const dispatch = useDispatch();
    const [termsAndConditionData, setTermsAndConditionData] = useState('');
    const [loading, setLoading] = useState(false);
    const [Reload, setReload] = useState(false);
    const [LoadingBetweenCities, setLoadingBetweenCities] = useState(false);
    const [LoadingTripsVip, setLoadingTripsVip] = useState(false);
    const [loadingDailyTrip, setLoadingDailyTrip] = useState(false);
    const [page1, setPage1] = useState(0);
    const [lastPage1, setLastPage1] = useState(0);
    const [page, setPage] = useState(0);
    const [lastPage, setLastPage] = useState(0);
    const [page2, setPage2] = useState(0);
    const [lastPage2, setLastPage2] = useState(0);
    const [paymentMethodId, setPaymentMethodId] = useState(0);
    const [noResault, setNoResault] = useState('');
    const [noResault1, setNoResault1] = useState('');
    const [noResault2, setNoResault2] = useState('');
    const [activeText, setActiveText] = useState(0);
    const [tripsBetweenCities, setTripsBetweenCities] = useState([]);
    const [tripsOrdersVip, setTripsOrdersVip] = useState([]);
    const [dailyTrips, setDailyTrips] = useState([]);
    const [review, setReview] = useState('');
    const [changeOrdersScreen, setchangeOrdersScreen] = useState([
        {
            id: 0,
            name: strings('lang.Dailyrides')
        },
        {
            id: 1,
            name: strings('lang.VIPrequests')
        },
        {
            id: 2,
            name: strings('lang.Intercityrequests')
        },
    ]);
    const [changeOrdersScreenId, setchangeOrdersScreenId] = useState(0);
    const isFocused = useIsFocused();

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                props.navigation.navigate('Home')
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    useEffect(() => {

        const getDailyTrips = async () => {
            setLoading(true)
            try {
                let response = await dispatch(dailyTripsActions.getTrips(1));
                if (response.success == true) {
                    if (response.data.items.length == 0) {
                        setNoResault2(strings('lang.No_Results'));
                        setDailyTrips([])
                    } else {
                        setNoResault('');
                        setDailyTrips(response.data.items)
                        setLastPage2(response.data.last_page);
                        setPage2(1)
                    }
                }
                else {
                    if (response.message) {
                        // Toaster(
                        //     'top',
                        //     'danger',
                        //     Red,
                        //     response.message,
                        //     White,
                        //     1500,
                        //     screenHeight / 15,
                        // );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getTripsOrdersVip = async () => {
            setLoading(true)
            try {
                let response = await dispatch(vipTripsActions.getTrips(1));
                if (response.success == true) {
                    if (response.data.items.length == 0) {
                        setNoResault(strings('lang.No_Results'));
                        setTripsOrdersVip([])
                    } else {
                        setNoResault('');
                        setTripsOrdersVip(response.data.items)
                        setLastPage(response.data.last_page);
                        setPage(1)
                    }
                }
                else {
                    if (response.message) {
                        // Toaster(
                        //     'top',
                        //     'danger',
                        //     Red,
                        //     response.message,
                        //     White,
                        //     1500,
                        //     screenHeight / 15,
                        // );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getTripsBetweenCities = async () => {
            setLoading(true)
            try {
                let response = await dispatch(cityTripsActions.getTrips(1));
                if (response.success == true) {
                    if (response.data.items.length == 0) {
                        setNoResault1(strings('lang.No_Results'));
                        setTripsBetweenCities([])
                    } else {
                        setNoResault1('');
                        setTripsBetweenCities(response.data.items);
                        setLastPage1(response.data.last_page);
                        setPage1(1)
                    }
                }
                else {
                    if (response.message) {
                        // Toaster(
                        //     'top',
                        //     'danger',
                        //     Red,
                        //     response.message,
                        //     White,
                        //     1500,
                        //     screenHeight / 15,
                        // );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };
        getDailyTrips();
        getTripsOrdersVip();
        getTripsBetweenCities();

    }, [Reload, isFocused]);

    const onRefresh = async () => {
        setLoading(true)
        try {
            let response = await dispatch(vipTripsActions.getTrips(1));
            if (response.success == true) {
                if (response.data.items.length == 0) {
                    setNoResault(strings('lang.No_Results'));
                    setTripsOrdersVip([])
                } else {
                    setNoResault('');
                    setTripsOrdersVip(response.data.items);
                    setLastPage(response.data.last_page);
                    setPage(1)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const LoadMore = async () => {
        console.log('page', page);
        console.log('lastpage', lastPage);
        if (page < lastPage) {
            setLoadingTripsVip(true)
            try {
                let response = await dispatch(vipTripsActions.getTrips(page + 1));
                if (response.success == true) {
                    setTripsOrdersVip([...tripsOrdersVip, ...response.data.items])
                    setPage(page + 1);
                    setNoResault('')
                    setLoadingTripsVip(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingTripsVip(false);
                }
            } catch (err) {
                setLoadingTripsVip(false);
            }

        }
        else {
        }
    }

    const onRefresh1 = async () => {
        setLoading(true)
        try {
            let response = await dispatch(cityTripsActions.getTrips(1));
            if (response.success == true) {
                if (response.data.items.length == 0) {
                    setNoResault1(strings('lang.No_Results'));
                    setTripsBetweenCities([])
                } else {
                    setNoResault1('');
                    setTripsBetweenCities(response.data.items);
                    setLastPage1(response.data.last_page);
                    setPage1(1)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const LoadMore1 = async () => {
        console.log('page1', page1);
        console.log('lastpage1', lastPage1);
        if (page1 < lastPage1) {
            setLoadingBetweenCities(true)
            try {
                let response = await dispatch(cityTripsActions.getTrips(page1 + 1));
                if (response.success == true) {
                    setTripsBetweenCities([...tripsBetweenCities, ...response.data.items])
                    setPage1(page1 + 1);
                    setNoResault1('')
                    setLoadingBetweenCities(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingBetweenCities(false);
                }
            } catch (err) {
                setLoadingBetweenCities(false);
            }

        }
        else {
        }
    }

    const onRefresh2 = async () => {
        setLoading(true)
        try {
            let response = await dispatch(dailyTripsActions.getTrips(1));
            if (response.success == true) {
                if (response.data.items.length == 0) {
                    setNoResault2(strings('lang.No_Results'));
                    setDailyTrips([])
                } else {
                    setNoResault('');
                    setDailyTrips(response.data.items)
                    setLastPage2(response.data.last_page);
                    setPage2(1)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const LoadMore2 = async () => {
        console.log('page2', page2);
        console.log('lastpage2', lastPage2);
        if (page2 < lastPage2) {
            setLoadingDailyTrip(true)
            try {
                let response = await dispatch(dailyTripsActions.getTrips(page2 + 1));
                if (response.success == true) {
                    setDailyTrips([...dailyTrips, ...response.data.items]);
                    setPage2(page2 + 1)
                    console.log('aasdasfdasf', dailyTrips);
                    setNoResault2('')
                    setLoadingDailyTrip(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingDailyTrip(false);
                }
            } catch (err) {
                setLoadingDailyTrip(false);
            }

        }
        else {
        }
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Orders')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.navigate('Home');
                    }}
                />

                <ScrollView
                    style={{
                        width: '90%',
                        height: '100%',
                        marginBottom: '2%',
                        alignSelf: 'center',
                    }}
                    showsVerticalScrollIndicator={false}
                >
                    {[{}, {}, {}, {}, {}, {}].map((item) => {
                        return <View style={styles.container}>
                            <View
                                style={{
                                    width: '50%',
                                    height: screenHeight / 55,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '3%',
                                    alignSelf: "flex-start",
                                    marginRight: "3%"
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: screenHeight / 55,
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>

                            <View
                                style={{
                                    width: '55%',
                                    height: screenHeight / 65,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '3%',
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: screenHeight / 65,
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>

                            <View
                                style={{
                                    width: '35%',
                                    height: screenHeight / 65,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '3%',
                                    alignSelf: "flex-end"
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: screenHeight / 65,
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>

                            <View
                                style={{
                                    width: '35%',
                                    height: screenHeight / 60,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '3%',
                                    alignSelf: "flex-end"
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: screenHeight / 60,
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>
                        </View>
                    })}
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>
            </View>
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Orders')}
                    backPress={() => {
                        props.navigation.navigate('Home');
                    }}
                />
                <View style={styles.changeData}>
                    {changeOrdersScreen.map((item, idex) => {
                        return (
                            <Pressable
                                onPress={() => { setchangeOrdersScreenId(item.id) }}
                                style={[item.id == changeOrdersScreenId ? styles.active : styles.unActive]}>
                                <Text style={[styles.textData, { color: item.id == changeOrdersScreenId ? DarkBlue : Black }]}>{item.name}</Text>
                                {item.id == 0 ?
                                    dailyTrips.length == 0 ?
                                        <></>
                                        :
                                        <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center' }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: White }}>{dailyTrips.length}</Text>
                                        </View>
                                    :
                                    <></>
                                }
                                {item.id == 1 ?
                                    tripsOrdersVip.length == 0 ?
                                        <></>
                                        :
                                        <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center' }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: White }}>{tripsOrdersVip.length}</Text>
                                        </View>
                                    :
                                    <></>
                                }
                                {item.id == 2 ?
                                    tripsBetweenCities.length == 0 ?
                                        <></>
                                        :
                                        <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center' }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: White }}>{tripsBetweenCities.length}</Text>
                                        </View>
                                    :
                                    <></>
                                }
                            </Pressable>
                        )
                    })}
                </View>
                {changeOrdersScreenId == 0
                    &&
                    <>
                        {
                            noResault2 ? (
                                <Text
                                    style={{
                                        fontSize: screenWidth / 18,
                                        fontFamily: appFontBold,
                                        color: Red,
                                        marginTop: screenHeight / 5,
                                        alignSelf: 'center',
                                    }}
                                >
                                    {noResault2}
                                </Text>
                            ) : (
                                <View></View>
                            )
                        }
                        <FlatList
                            data={dailyTrips}
                            refreshControl={
                                <RefreshControl refreshing={Reload} onRefresh={onRefresh2} />
                            }
                            showsVerticalScrollIndicator={false}
                            renderItem={({ item, key }) =>
                                <OrdersContainer onPress={() => { props.navigation.navigate('DetailsTrip', { item: item, type: 'dailytTrip' }) }} item={item} />
                            }
                            keyExtractor={item => item.id}
                            style={{ alignSelf: "center", overflow: "hidden", width: screenWidth, backgroundColor: White, }}
                            onEndReachedThreshold={0.1}
                            onEndReached={LoadMore2}
                        />
                        {loadingDailyTrip ? <ActivityIndicator size={Platform.OS == 'ios' ? 80 : 40} color={DarkBlue} /> : <></>}

                    </>
                }

                {changeOrdersScreenId == 1
                    &&
                    <>
                        {
                            noResault ? (
                                <Text
                                    style={{
                                        fontSize: screenWidth / 18,
                                        fontFamily: appFontBold,
                                        color: Red,
                                        marginTop: screenHeight / 5,
                                        alignSelf: 'center',
                                    }}
                                >
                                    {noResault}
                                </Text>
                            ) : (
                                <View></View>
                            )
                        }
                        <FlatList
                            data={tripsOrdersVip}
                            refreshControl={
                                <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                            }
                            showsVerticalScrollIndicator={false}
                            renderItem={({ item, key }) =>
                                <OrdersContainer onPress={() => { props.navigation.navigate('DetailsTrip', { item: item, type: 'orderVip' }) }} item={item} />
                            }
                            keyExtractor={item => item.id}
                            style={{ alignSelf: "center", overflow: "hidden", width: screenWidth, backgroundColor: White, }}
                            onEndReachedThreshold={0.1}
                            onEndReached={LoadMore}
                        />
                        {LoadingTripsVip ? <ActivityIndicator size={Platform.OS == 'ios' ? 80 : 40} color={DarkBlue} /> : <></>}

                    </>
                }
                {changeOrdersScreenId == 2
                    &&
                    <>
                        {
                            noResault1 ? (
                                <Text
                                    style={{
                                        fontSize: screenWidth / 18,
                                        fontFamily: appFontBold,
                                        color: Red,
                                        marginTop: screenHeight / 5,
                                        alignSelf: 'center',
                                    }}
                                >
                                    {noResault1}
                                </Text>
                            ) : (
                                <View></View>
                            )
                        }
                        <FlatList
                            data={tripsBetweenCities}
                            refreshControl={
                                <RefreshControl refreshing={Reload} onRefresh={onRefresh1} />
                            }
                            showsVerticalScrollIndicator={false}
                            renderItem={({ item, key }) =>
                                <OrdersContainer onPress={() => { props.navigation.navigate('DetailsTrip', { item: item, type: 'betweenCities' }) }} item={item} />
                            }
                            keyExtractor={item => item.id}
                            style={{ alignSelf: "center", overflow: "hidden", width: screenWidth, backgroundColor: White, }}
                            onEndReachedThreshold={0.1}
                            onEndReached={LoadMore1}
                        />
                        {LoadingBetweenCities ? <ActivityIndicator size={Platform.OS == 'ios' ? 80 : 40} color={DarkBlue} /> : <></>}
                    </>

                }


                <View style={{ height: screenHeight / 100 }}></View>

                <Footer current={'Orders'} navigation={props.navigation} />
            </View>
        );
    }
};

const styles = StyleSheet.create({
    changeData: {
        width: screenWidth,
        height: screenHeight / 18,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        marginBottom: 10,
        backgroundColor: WhiteGery,
        flexDirection: 'row',
        paddingHorizontal: '5%'
    },
    unActive: {
        width: '30%',
        height: '100%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center', flexDirection: 'row'
    },
    active: {
        width: '30%',
        height: '100%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        borderBottomWidth: 3,
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
        borderBottomColor: DarkBlue, flexDirection: 'row'
    },
    textData: {
        fontFamily: appFont,
        fontSize: screenWidth / 30
    },
});

export default Orders;
