import React, { Component, useCallback, useEffect, useRef, useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, PermissionsAndroid, BackHandler, I18nManager, Keyboard, Modal, Linking, Pressable, AppState, Alert, Platform } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor2, appColor1, Black, Red, MediumGreen, Grey, DarkBlue, Green2, DarkYellow, WhiteRed } from '../components/Styles';
import { Button, Container, Textarea } from 'native-base';
import Geolocation from 'react-native-geolocation-service';
import MapView, { <PERSON><PERSON>, <PERSON>yl<PERSON> } from 'react-native-maps';


import { strings } from './i18n';
import { ScrollView, TextInput, TouchableOpacity } from 'react-native-gesture-handler';
import Loading from '../components/Loading';
import { Modalize } from 'react-native-modalize';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import MapViewDirections from 'react-native-maps-directions';
import * as driverDailyTripsActions from '../../Store/Actions/driverDailyTrips';
import * as driverOffersActions from '../../Store/Actions/driverOffers';
import * as driverPackagesActions from '../../Store/Actions/driverPackages';
import * as driverVipTripsActions from '../../Store/Actions/driverVipTrips';
import * as driverCityTripsActions from '../../Store/Actions/driverCityTrips';

import { useDispatch, useSelector } from 'react-redux';
import Toaster from '../components/Toaster';
import StarRating from 'react-native-star-rating';
import RBSheet from 'react-native-raw-bottom-sheet';
import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from 'moment';
// import openMap, { createMapLink } from 'react-native-open-maps';
// import { NativeModules } from 'react-native';
// const { NotificationHelper } = NativeModules;
export const CLEARDID = 'CLEARDID';
export const CLEARUPDATEDDAILYTRIP1 = 'CLEARUPDATEDDAILYTRIP1';
export const CLEARDAILYTRIPCANCELLED1 = 'CLEARDAILYTRIPCANCELLED1';
export const CLEAROFFERUPDATED = 'CLEAROFFERUPDATED';
export const CLEARUPDATEDCITYTRIP1 = 'CLEARUPDATEDCITYTRIP1';
export const CLEARUPDATEDVIPTRIPDRIVER = 'CLEARUPDATEDVIPTRIPDRIVER';


const ASPECT_RATIO = screenWidth / screenHeight
const GOOGLE_MAPS_APIKEY = '…';


const MapDriver = props => {
    const dispatch = useDispatch();
    const [region, SetRegion] = useState({
        latitude: 0,
        longitude: 0,
        longitudeDelta: 0.1 * ASPECT_RATIO,
        latitudeDelta: 0.1
    })
    const [lat, SetLat] = useState(0)
    const [lng, SetLng] = useState(0)
    const [chosePrice, setChosePrice] = useState([
        {
            id: 1,
            price: (props.route.params.pending == true ? 0 : props.route.params.trip && props.route.params.trip.client_price - 2)
        },
        {
            id: 2,
            price: (props.route.params.pending == true ? 0 : props.route.params.trip && props.route.params.trip.client_price)
        },
        {
            id: 3,
            price: (props.route.params.pending == true ? 0 : props.route.params.trip && props.route.params.trip.client_price + 2)
        },
    ]);
    const [payment_methodId, setPayment_methodId] = useState(null);
    const [choosePriceId, setChoosePriceId] = useState(0);
    const [request, setRequest] = useState(0);
    const [tripId, setTripId] = useState(0);
    const [offerId, setOfferId] = useState(0);
    const [price, setPrice] = useState('');
    const [modalVisible1, setModalVisible1] = useState(false);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [timeOffer, setTimeOffer] = useState(false);
    const [detailsTrip, setDetailsTrip] = useState({});
    const [detailsTripVip, setDetailsTripVip] = useState({});
    const [data, setData] = useState({});
    const [markers, setMarkers] = useState([
        {}, {}, {}
    ]);
    const [timer, setTimer] = useState(false)
    const [hiddenHeader, setHiddenHeader] = useState(false)
    const [IsPaid, setIspaid] = useState(false)
    const [minutes, setMinutes] = useState(0)
    const [seconds, setSeconds] = useState(0)
    const [min, setMin] = useState(0)
    const [sec, setSec] = useState(-1)
    const [starCount, setStarCount] = useState(0)
    const modalizeRef = useRef();
    const modalizeRef1 = useRef();
    const modalizeRef2 = useRef();
    const modalizeRef3 = useRef();
    const modalizeRef4 = useRef();
    const contentRef = useRef();
    const contentRef1 = useRef();
    const contentRef2 = useRef();
    const contentRef3 = useRef();
    const contentRef4 = useRef();
    const modalizeRefTaimer = useRef();
    const contentRefTaimer = useRef();
    const modalizeRefRat = useRef();
    const contentRefRat = useRef();
    const inputRef = useRef();
    const refRBSheet = useRef();
    const refRBSheetReasons = useRef();
    const refRBSheetIsPaid = useRef();
    const [review, setReview] = useState('');
    const [report, setReport] = useState('');
    const [ViewID, setViewID] = useState(0);
    const IsFocused = useIsFocused();
    const [Reload, setReload] = useState(false)
    const [newMessgae, setNewMessgae] = useState(false);

    let origin = {

        latitude:
            props.route.params.pending == true ?
                (props.route.params.driverPendingTrip.type == "DailyTrip" || props.route.params.driverPendingTrip.type == "VipTrip" || props.route.params.driverPendingTrip.type == "CityTrip") &&
                    props.route.params.driverPendingTrip.trip != null
                    ?
                    props.route.params.driverPendingTrip.trip.from_address_id
                        ?
                        props.route.params.driverPendingTrip.trip.address_from.lat
                        :
                        props.route.params.driverPendingTrip.trip.from_lat
                    :
                    props.route.params.driverPendingTrip.type == "UserPackage" && props.route.params.driverPendingTrip.trip != null
                        ?
                        props.route.params.driverPendingTrip.trip.user_package &&
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0] &&
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].from_address_id
                            ?
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].address_from.lat
                            :
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].from_lat
                        :
                        0
                :
                // props.route.params.request == 4 ?
                //     0
                //     :
                props.route.params.request == 2
                    ?
                    props.route.params.trip.user_package &&
                        props.route.params.trip.user_package.destinations[0] &&
                        props.route.params.trip.user_package.destinations[0].from_address_id
                        ?
                        props.route.params.trip.user_package.destinations[0].address_from.lat
                        :
                        props.route.params.trip.user_package.destinations[0].from_lat
                    :
                    props.route.params.trip && props.route.params.trip.from_address_id
                        ?
                        props.route.params.trip.address_from.lat
                        :
                        props.route.params.trip && props.route.params.trip.from_lat,
        longitude:
            props.route.params.pending == true ?
                (props.route.params.driverPendingTrip.type == "DailyTrip" || props.route.params.driverPendingTrip.type == "VipTrip" || props.route.params.driverPendingTrip.type == "CityTrip") &&
                    props.route.params.driverPendingTrip.trip != null
                    ?
                    props.route.params.driverPendingTrip.trip.from_address_id
                        ?
                        props.route.params.driverPendingTrip.trip.address_from.lat
                        :
                        props.route.params.driverPendingTrip.trip.from_lng
                    :
                    props.route.params.driverPendingTrip.type == "UserPackage" && props.route.params.driverPendingTrip.trip != null
                        ?
                        props.route.params.driverPendingTriptrip.user_package &&
                            props.route.params.driverPendingTriptrip.user_package.destinations[0] &&
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].from_address_id
                            ?
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].address_from.lat
                            :
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].from_lng
                        :
                        0
                :
                // props.route.params.request == 4 ?
                //     0
                //     :
                props.route.params.request == 2
                    ?
                    props.route.params.trip.user_package &&
                        props.route.params.trip.user_package.destinations[0] &&
                        props.route.params.trip.user_package.destinations[0].from_address_id
                        ?
                        props.route.params.trip.user_package.destinations[0].address_from.lat
                        :
                        props.route.params.trip.user_package.destinations[0].from_lng
                    :
                    props.route.params.trip && props.route.params.trip.from_address_id
                        ?
                        props.route.params.trip.address_from.lat
                        :
                        props.route.params.trip && props.route.params.trip.from_lng,
    };
    let destination = {
        latitude:
            props.route.params.pending == true ?
                (props.route.params.driverPendingTrip.type == "DailyTrip" || props.route.params.driverPendingTrip.type == "VipTrip" || props.route.params.driverPendingTrip.type == "CityTrip") &&
                    props.route.params.driverPendingTrip.trip != null

                    ?
                    props.route.params.driverPendingTrip.trip.to_address_id
                        ?
                        props.route.params.driverPendingTrip.trip.address_to.lat
                        :
                        props.route.params.driverPendingTrip.trip.to_lat
                    :
                    props.route.params.driverPendingTrip.type == "UserPackage" && props.route.params.driverPendingTrip.trip != null
                        ?
                        props.route.params.driverPendingTrip.trip.user_package &&
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0] &&
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].to_address_id
                            ?
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].address_to.lat
                            :
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].to_lat
                        :
                        0
                :

                // props.route.params.request == 4 ?
                //     0
                //     :
                props.route.params.request == 2
                    ?
                    props.route.params.trip.user_package &&
                        props.route.params.trip.user_package.destinations[0] &&
                        props.route.params.trip.user_package.destinations[0].to_address_id
                        ?
                        props.route.params.trip.user_package.destinations[0].address_to.lat
                        :
                        props.route.params.trip.user_package.destinations[0].to_lat
                    :
                    props.route.params.trip && props.route.params.trip.to_address_id
                        ?
                        props.route.params.trip.address_to.lat
                        :
                        props.route.params.trip && props.route.params.trip.to_lat,

        longitude:
            props.route.params.pending == true ?
                (props.route.params.driverPendingTrip.type == "DailyTrip" || props.route.params.driverPendingTrip.type == "VipTrip" || props.route.params.driverPendingTrip.type == "CityTrip") &&
                    props.route.params.driverPendingTrip.trip != null
                    ?
                    props.route.params.driverPendingTrip.trip.to_address_id
                        ?
                        props.route.params.driverPendingTrip.trip.address_to.lng
                        :
                        props.route.params.driverPendingTrip.trip.to_lng
                    :
                    props.route.params.driverPendingTrip.type == "UserPackage" && props.route.params.driverPendingTrip.trip != null
                        ?
                        props.route.params.driverPendingTrip.trip.user_package &&
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0] &&
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].to_address_id
                            ?
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].address_to.lng
                            :
                            props.route.params.driverPendingTrip.trip.user_package.destinations[0].to_lng
                        :
                        0
                :
                // props.route.params.request == 4 ?
                //     0
                //     :
                props.route.params.request == 2
                    ?
                    props.route.params.trip.user_package &&
                        props.route.params.trip.user_package.destinations[0] &&
                        props.route.params.trip.user_package.destinations[0].to_address_id
                        ?
                        props.route.params.trip.user_package.destinations[0].address_to.lng
                        :
                        props.route.params.trip.user_package.destinations[0].to_lng
                    :
                    props.route.params.trip && props.route.params.trip.to_address_id
                        ?
                        props.route.params.trip.address_to.lng
                        :
                        props.route.params.trip && props.route.params.trip.to_lng,
    };


    const [reasonsId, setReasonId] = useState(null);
    const [reasonText, setReasonText] = useState('');
    const reasonss = useSelector(state => state.general.driverReasons);
    const [reasons, setReasons] = useState(reasonss);

    const [maxPrice, setMaxPrice] = useState(0);
    const [minPrice, setMinPrice] = useState(0);
    const [newPrice, setNewPrice] = useState(0);
    const [priceLimit, setPriceLimit] = useState(false);
    const [MapDirection, setMapDirection] = useState('');
    const [latClient, setLatClient] = useState(0);
    const [lngClient, setLngClient] = useState(0);
    const [labelAddressClient, setLabelAddressClient] = useState('');
    const [latDistenation, setLatDistenation] = useState(0);
    const [lngDistenation, setLngDistenation] = useState(0);
    const [labelDistenation, setLabelDistenation] = useState('');
    const [openMap, setOpenMap] = useState('');
    const five_offer = useSelector(state => state.general.five_offer);


    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );



    const trips = useSelector((state) => state.trips);
    const newMessage = useSelector((state) => state.message);

    useEffect(() => {
        console.log('Message reducer In Screen', newMessage);
        if (newMessage.message.chat_message) {
            setNewMessgae(true)
        }
    }, [newMessage]);

    useEffect(() => {
        // setTimeout(() => {
        //     if (NotificationHelper && NotificationHelper.clearAllNotifications) {
        //         NotificationHelper.clearAllNotifications();
        //         console.log('NotificationHelper', NotificationHelper.clearAllNotifications());
        //     }
        // }, 500);
        console.log('tripsio', trips);
        if (trips.offerUpdated.offer) {
            if (trips.offerUpdated.offer.status == 'rejected') {
                setSec(-1)
                console.log('new');
                setViewID(0)
                dispatch({ type: CLEARUPDATEDDAILYTRIP1 });
                props.navigation.navigate('DriverRequests')
            } if (trips.offerUpdated.offer.is_cancelled == true) {
                setViewID(0)
                console.log('new');
                setSec(-1)
                dispatch({ type: CLEARUPDATEDDAILYTRIP1 });
                props.navigation.navigate('DriverRequests')
            }
            if (trips.offerUpdated.offer.status == 'accepted') {
                console.log('new');
                setSec(-1)
                setTimer(false)
                setViewID(3);
                setMapDirection('toClient')
                setOpenMap('toClient')
                dispatch({ type: CLEARUPDATEDDAILYTRIP1 });
            }
        }

        if (trips.updatedDailyTrip.daily_trip) {
            let DailyTrip = detailsTrip
            let updated = trips.updatedDailyTrip.daily_trip
            if (DailyTrip.id === updated.id) {
                Object.assign(DailyTrip, updated);
                setIspaid(updated.is_paid);
                if (updated.payment_method) {
                    setPayment_methodId(updated.payment_method.id);
                }
            }
            dispatch({ type: CLEARDAILYTRIPCANCELLED1 });

        }

        if (trips.updatedDailyTrip.daily_trip) {
            if (trips.updatedDailyTrip.daily_trip.id == tripIdd) {
                console.log('mmmm', tripIdd);
                if (trips.updatedDailyTrip.daily_trip.status == "cancelled") {
                    setViewID(0)
                    refRBSheet.current.close();
                    refRBSheetReasons.current.close();
                    console.log('new');
                    setTimer(false)
                    props.navigation.navigate('DriverRequests')
                    dispatch({ type: CLEARUPDATEDDAILYTRIP1 });
                    dispatch({ type: CLEARDID });
                }
                if (trips.updatedDailyTrip.daily_trip.status == "finished") {
                    refRBSheet.current.close();
                    refRBSheetReasons.current.close();
                    console.log('new');
                    setTimer(false)
                    dispatch({ type: CLEARUPDATEDDAILYTRIP1 });
                    dispatch({ type: CLEARDID });
                }
            }
        }

        if (trips.dailyTripCancelled.daily_trip) {
            if (trips.dailyTripCancelled.daily_trip.id == tripIdd) {
                console.log('ttttt', tripIdd);
                if (trips.dailyTripCancelled.daily_trip.status == "cancelled") {
                    setViewID(0)
                    refRBSheet.current.close();
                    refRBSheetReasons.current.close();
                    console.log('new');
                    setTimer(false)
                    props.navigation.navigate('DriverRequests')
                    dispatch({ type: CLEARDAILYTRIPCANCELLED1 });
                    dispatch({ type: CLEARDID });
                }
            }
        }

        if (trips.updatedCityTrip.city_trip) {
            if (trips.updatedCityTrip.city_trip.status == "cancelled") {
                setViewID(0)
                refRBSheet.current.close();
                refRBSheetReasons.current.close();
                console.log('new');
                setTimer(false)
                props.navigation.navigate('DriverRequests')
                dispatch({ type: CLEARUPDATEDCITYTRIP1 });
            }
            if (trips.updatedCityTrip.city_trip.status == "finished") {
                refRBSheet.current.close();
                refRBSheetReasons.current.close();
                console.log('new');
                dispatch({ type: CLEARUPDATEDCITYTRIP1 });
            }
        }

        if (trips.updatedVipTrip.vip_trip) {
            if (trips.updatedVipTrip.vip_trip.status == "cancelled") {
                setViewID(0);
                refRBSheet.current.close();
                refRBSheetReasons.current.close();
                console.log('new');
                setTimer(false)
                props.navigation.navigate('DriverRequests')
                dispatch({ type: CLEARUPDATEDVIPTRIPDRIVER });
            }
            if (trips.updatedVipTrip.vip_trip.status == "finished") {
                refRBSheet.current.close();
                refRBSheetReasons.current.close();
                console.log('new');
                dispatch({ type: CLEARUPDATEDVIPTRIPDRIVER });
            }
        }

    }, [trips]);

    useEffect(() => {
        const timerId = setInterval(async () => {
            if (seconds >= 59) {
                // if (minutes >= 2) {
                //     setDisabledButton(true)
                // }
                // else {
                setMinutes(m => m + 1)
                setSeconds(0);

                // }
            }
            else setSeconds(s => s + 1)
        }, 1000)
        return () => clearInterval(timerId);

    }, [seconds, minutes]);

    // useEffect(() => {
    //     let timer;

    //     const handleAppStateChange = (nextAppState) => {
    //         if (nextAppState === 'active') {
    //             // App is in the foreground, start or resume the timer
    //             timer = setInterval(updateTimer, 1000);
    //         } else {
    //             // App is in the background, clear the timer
    //             clearInterval(timer);
    //         }
    //     };

    //     const updateTimer = () => {
    //         if (seconds === 59) {
    //             setMinutes((prevMinutes) => prevMinutes + 1);
    //             setSeconds(0);
    //         } else {
    //             setSeconds((prevSeconds) => prevSeconds + 1);
    //         }
    //     };

    //     // Set up the initial timer
    //     timer = setInterval(updateTimer, 1000);

    //     // Add event listener for app state changes
    //     AppState.addEventListener('change', handleAppStateChange);

    //     // Clean up timers and event listener when the component unmounts
    //     return () => {
    //         clearInterval(timer);
    //         AppState.removeEventListener('change', handleAppStateChange);
    //     };
    // }, [seconds]);



    useEffect(() => {
        const timerIdd = setInterval(() => {
            if (sec <= 0) {
                if (min <= 0) {

                    if (min == 0 && sec == 0) {
                        setViewID(0)
                        dispatch({ type: CLEARUPDATEDDAILYTRIP1 });
                        props.navigation.navigate('DriverRequests')
                        setSec(-1)
                    }

                }
                else {
                    // setMin(m => m - 1)
                    // setSec(29)
                }
            }
            else setSec(s => s - 1)
        }, 1000)
        return () => clearInterval(timerIdd);

    }, [sec, min]);

    const tripIdd = useSelector((state) => state.trips.tripId);


    useEffect(() => {

        console.log('tripId', tripIdd);

        setHiddenHeader(false)

        if (props.route.params.request == 1) {
            const showTrip = async () => {
                setLoading(true)
                try {
                    let response = await dispatch(driverDailyTripsActions.showTrip(props.route.params.driverPendingOffer != null ? props.route.params.driverPendingOffer.daily_trip_id : props.route.params.trip.id));
                    if (response.success == true) {
                        setDetailsTrip(response.data)
                        setMaxPrice(response.data.max_price)
                        setMinPrice(response.data.min_price)
                        setMapDirection('toClient')
                        setLatClient(
                            response.data.from_address_id
                                ?
                                response.data.address_from.lat
                                :
                                response.data.from_lat);
                        setLngClient(
                            response.data.from_address_id
                                ?
                                response.data.address_from.lng
                                :
                                response.data.from_lng
                        );
                        setLabelAddressClient(
                            response.data.from_address_id
                                ?
                                response.data.address_from.address
                                :
                                response.data.from_address
                        );
                        setLatDistenation(
                            response.data.to_address_id
                                ?
                                response.data.address_to.lat
                                :
                                response.data.to_lat);
                        setLngDistenation(
                            response.data.to_address_id
                                ?
                                response.data.address_lng.lng
                                :
                                response.data.to_lng);
                        setLabelDistenation(
                            response.data.to_address_id
                                ?
                                response.data.address_to.address
                                :
                                response.data.to_address);
                        // origin = { latitude: response.data.from_lat, longitude: response.data.from_lng };
                        // destination = { latitude: response.data.to_lat, longitude: response.data.to_lng };
                    }
                    else {
                        if (response.message) {
                            Toaster(
                                'top',
                                'danger',
                                Red,
                                response.message,
                                White,
                                1500,
                                screenHeight / 15,
                            );
                        }
                    }
                    setLoading(false);
                }
                catch (err) {
                    console.log('err', err)
                    setLoading(false);
                }
            };

            showTrip()
        } else if (props.route.params.request == 2) {
            const showTrip = async () => {
                setLoading(true)
                try {
                    let response = await dispatch(driverPackagesActions.getShowUserPackage(props.route.params.trip.id));
                    if (response.success == true) {
                        setDetailsTrip(response.data)
                        setLatClient(
                            response.data.user_package.destinations[0].from_address_id
                                ?
                                response.data.user_package.destinations[0].address_from.lat
                                :
                                response.data.user_package.destinations[0].from_lat);
                        setLngClient(
                            response.data.user_package.destinations[0].from_address_id
                                ?
                                response.data.user_package.destinations[0].address_from.lng
                                :
                                response.data.user_package.destinations[0].from_lng
                        );
                        setLabelAddressClient(
                            response.data.user_package.destinations[0].from_address_id
                                ?
                                response.data.user_package.destinations[0].address_from.address
                                :
                                response.data.user_package.destinations[0].from_address
                        );
                        setLatDistenation(
                            response.data.user_package.destinations[0].to_address_id
                                ?
                                response.data.user_package.destinations[0].address_to.lat
                                :
                                response.data.user_package.destinations[0].to_lat);
                        setLngDistenation(
                            response.data.user_package.destinations[0].to_address_id
                                ?
                                response.data.user_package.destinations[0].address_lng.lng
                                :
                                response.data.user_package.destinations[0].to_lng);
                        setLabelDistenation(
                            response.data.user_package.destinations[0].to_address_id
                                ?
                                response.data.user_package.destinations[0].address_to.address
                                :
                                response.data.user_package.destinations[0].to_address);
                        // origin = { latitude: response.data.from_lat, longitude: response.data.from_lng };
                        // destination = { latitude: response.data.to_lat, longitude: response.data.to_lng };
                    }
                    else {
                        if (response.message) {
                            Toaster(
                                'top',
                                'danger',
                                Red,
                                response.message,
                                White,
                                1500,
                                screenHeight / 15,
                            );
                        }
                    }
                    setLoading(false);
                }
                catch (err) {
                    console.log('err', err)
                    setLoading(false);
                }
            };

            showTrip()
        } else if (props.route.params.request == 3) {
            const showTrip = async () => {
                setLoading(true)
                try {
                    let response = await dispatch(driverVipTripsActions.showTrip(props.route.params.trip.id));
                    if (response.success == true) {
                        setDetailsTrip(response.data)
                        setLatClient(
                            response.data.from_address_id
                                ?
                                response.data.address_from.lat
                                :
                                response.data.from_lat);
                        setLngClient(
                            response.data.from_address_id
                                ?
                                response.data.address_from.lng
                                :
                                response.data.from_lng
                        );
                        setLabelAddressClient(
                            response.data.from_address_id
                                ?
                                response.data.address_from.address
                                :
                                response.data.from_address
                        );
                        setLatDistenation(
                            response.data.to_address_id
                                ?
                                response.data.address_to.lat
                                :
                                response.data.to_lat);
                        setLngDistenation(
                            response.data.to_address_id
                                ?
                                response.data.address_lng.lng
                                :
                                response.data.to_lng);
                        setLabelDistenation(
                            response.data.to_address_id
                                ?
                                response.data.address_to.address
                                :
                                response.data.to_address);
                        // origin = { latitude: response.data.from_lat, longitude: response.data.from_lng };
                        // destination = { latitude: response.data.to_lat, longitude: response.data.to_lng };
                    }
                    else {
                        if (response.message) {
                            Toaster(
                                'top',
                                'danger',
                                Red,
                                response.message,
                                White,
                                1500,
                                screenHeight / 15,
                            );
                        }
                    }
                    setLoading(false);
                }
                catch (err) {
                    console.log('err', err)
                    setLoading(false);
                }
            };

            showTrip()
        } else if (props.route.params.request == 4) {
            const showTrip = async () => {
                setLoading(true)
                try {
                    let response = await dispatch(driverCityTripsActions.showTrip(props.route.params.trip.id));
                    if (response.success == true) {
                        setDetailsTrip(response.data)
                        setLatClient(
                            response.data.from_address_id
                                ?
                                response.data.address_from.lat
                                :
                                response.data.from_lat);
                        setLngClient(
                            response.data.from_address_id
                                ?
                                response.data.address_from.lng
                                :
                                response.data.from_lng
                        );
                        setLabelAddressClient(
                            response.data.from_address_id
                                ?
                                response.data.address_from.address
                                :
                                response.data.from_address
                        );
                        setLatDistenation(
                            response.data.to_address_id
                                ?
                                response.data.address_to.lat
                                :
                                response.data.to_lat);
                        setLngDistenation(
                            response.data.to_address_id
                                ?
                                response.data.address_lng.lng
                                :
                                response.data.to_lng);
                        setLabelDistenation(
                            response.data.to_address_id
                                ?
                                response.data.address_to.address
                                :
                                response.data.to_address);
                        // origin = { latitude: response.data.from_lat, longitude: response.data.from_lng };
                        // destination = { latitude: response.data.to_lat, longitude: response.data.to_lng };
                    }
                    else {
                        if (response.message) {
                            Toaster(
                                'top',
                                'danger',
                                Red,
                                response.message,
                                White,
                                1500,
                                screenHeight / 15,
                            );
                        }
                    }
                    setLoading(false);
                }
                catch (err) {
                    console.log('err', err)
                    setLoading(false);
                }
            };

            showTrip()
        }


        // setLoading(true)

        let request = props.route.params.request
        setRequest(request)
        console.log('request', request);

        const onRegionChangeComplete = (region) => {
            var initial_Region = {
                longitudeDelta: region.longitudeDelta,
                latitudeDelta: region.latitudeDelta,
                longitude: region.longitude,
                latitude: region.latitude
            }

            onRegionChangeComplete.bind(initial_Region)
            console.log('region', region);
        }

        const requestLocationPermission = async () => {
            try {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                    {
                        'title': 'Location Permission',
                        'message': 'MyMapApp needs access to your location'
                    }
                )
                if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                    get_Lat_User()
                    // Alert.alert("Please allow location")
                } else {
                    console.log("Location permission denied")
                    setLoading(false)
                }
            } catch (err) {
                console.warn(err)
                setLoading(false)
            }
        }

        const RequestIosPermissio = () => {
            Geolocation.requestAuthorization('always').then((res) => {
                console.log('res', res);
                if (res == 'denied') {
                    //  Alert.alert("Please allow location to continuo")/
                    // Linking.openURL('app-settings:');
                    showCustomAlert()
                }
            });
            setLoading(false)
        }
        const showCustomAlert = () => {
            Alert.alert(
                'Permission Alert',
                'The app will not work properly without this permission ',
                [
                    {
                        text: 'Access permission from settings',
                        onPress: () => {
                            console.log('start permission');
                            Linking.openURL('app-settings:');
                            // get_Lat_User()
                        },
                    },
                    {
                        text: 'Cancel',
                        onPress: () => {
                            console.log('exit');
                            props.navigation.navigate('DriverRequests')
                        },
                    },
                ],
                { cancelable: false }
            );
        };

        const get_Lat_User = async () => {
            if (Platform.OS == 'ios') {
                RequestIosPermissio()
            }
            Geolocation.getCurrentPosition(
                (position) => {
                    console.log('position', position);
                    var lat = parseFloat(position.coords.latitude)
                    var longi = parseFloat(position.coords.longitude)
                    var initialRegion = {
                        latitude: lat,
                        longitude: longi,
                        longitudeDelta: 0.01 * ASPECT_RATIO,
                        latitudeDelta: 0.01
                    }
                    SetRegion(initialRegion)
                    SetLat(lat)
                    SetLng(longi)

                },
                (error) => {
                    console.log('error', error);

                },
                { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 1000 },
            );
            setLoading(false)
        }

        if (Platform.OS == 'ios') {
            get_Lat_User()
        }
        else {
            requestLocationPermission();
        }

        const pindingTrips = async () => {


            if (props.route.params.pending == true) {

                if (props.route.params.driverPendingOffer != null) {
                    setViewID(2);
                    setOfferId(props.route.params.driverPendingOffer.id)
                    setHiddenHeader(true)
                    const now = moment().format('YYYY-MM-DDTHH:mm:ss').toString();
                    const offer_start_time = moment(props.route.params.driverPendingOffer.offer_start_time).format('YYYY-MM-DDTHH:mm:ss').toString();

                    console.log("now", now);
                    console.log("offer_start_time", offer_start_time);

                    var date1 = new Date(offer_start_time);
                    var date2 = new Date(now);

                    var timeDifference = parseInt(date2 - date1, 10);
                    var seconds = Math.floor(timeDifference / 1000);
                    var minutes = Math.floor(seconds / 60);
                    var hours = Math.floor(minutes / 60);
                    var days = Math.floor(hours / 24);

                    console.log("Time difference:", timeDifference);
                    console.log("Days:", days);
                    console.log("Hours:", hours % 24);
                    console.log("Minutes:", minutes % 60);
                    console.log("Seconds:", seconds % 60);


                    // let date = '2023-12-22 00:00:55';
                    // let arrived_at = '2023-12-21 23:59:56';
                    var seconds = 30 - ((new Date(now).getTime() - new Date(offer_start_time).getTime()) / 1000);

                    // if (seconds <= 0) {
                    //     setSec(-1)
                    // } else {
                    setSec(seconds);
                    console.log(seconds);
                    // }
                }
            }


            // pendingDailyTrip
            if (props.route.params.pending == true) {
                if (props.route.params.driverPendingTrip.type == "DailyTrip") {
                    if (props.route.params.driverPendingTrip.trip != null) {
                        if (props.route.params.driverPendingTrip.trip.status == "accepted") {
                            console.log('asd');
                            setMapDirection('toClient')
                            setOpenMap('toClient')
                            setDetailsTrip(props.route.params.driverPendingTrip.trip)
                            setOfferId(props.route.params.driverPendingTrip.trip.my_offer.id)
                            setViewID(3)
                            setSec(-1)
                            setIspaid(props.route.params.driverPendingTrip.trip.is_paid)
                            if (props.route.params.driverPendingTrip.trip.payment_method) {
                                setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);
                            }
                            setHiddenHeader(true)
                        }
                        if (props.route.params.driverPendingTrip.trip.status == "waiting_for_client") {
                            setMapDirection('toDestination')
                            setOpenMap('toDestination')
                            setDetailsTrip(props.route.params.driverPendingTrip.trip)
                            setOfferId(props.route.params.driverPendingTrip.trip.my_offer.id)
                            setIspaid(props.route.params.driverPendingTrip.trip.is_paid);
                            if (props.route.params.driverPendingTrip.trip.payment_method) {
                                setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);
                            }
                            setTimer(true)
                            setHiddenHeader(true)
                            setViewID(3)
                            setSec(-1)
                            // let date = '2023-12-22 00:00:57';
                            // let arrived_at = '2023-12-22 00:00:57';
                            // let date = moment().format('YYYY-MM-DD HH:mm:ss');
                            // let arrived_at = props.route.params.driverPendingTrip.trip.driver_offer.arrived_at;

                            // var timeDiff = Math.abs(arrived_at - date);

                            // // Calculate hours, minutes, and seconds
                            // var hours = Math.floor(timeDiff / (1000 * 60 * 60));
                            // var minutes = Math.floor((timeDiff / (1000 * 60 * 60)) / (1000 * 60));
                            // var seconds = Math.floor((timeDiff / (1000 * 60)) / 1000);

                            const now = moment().format('YYYY-MM-DDTHH:mm:ss').toString();
                            const offer_start_time = moment(props.route.params.driverPendingTrip.trip.driver_offer.arrived_at).format('YYYY-MM-DDTHH:mm:ss').toString();

                            console.log("now", now);
                            console.log("offer_start_time", offer_start_time);

                            var date1 = new Date(offer_start_time);
                            var date2 = new Date(now);

                            var timeDifference = parseInt(date2 - date1, 10);
                            var seconds = Math.floor(timeDifference / 1000);
                            var minutes = Math.floor(seconds / 60);
                            var hours = Math.floor(minutes / 60);
                            var days = Math.floor(hours / 24);

                            console.log("Time difference:", timeDifference);
                            console.log("Days:", days);
                            console.log("Hours:", hours % 24);
                            console.log("Minutes:", minutes % 60);
                            console.log("Seconds:", seconds % 60);



                            var diff = Math.abs(new Date(now) - new Date(offer_start_time));
                            var minutes = Math.floor((diff / 1000) / 60);
                            var seconds = Math.floor((diff / 1000) % 60);
                            // var seconds = (((new Date(date).getTime() - new Date(arrived_at).getTime()) / (1000 * 60)) / 1000);

                            console.log('minutes', minutes);
                            console.log('seconds', seconds);
                            // console.log('seconds', seconds);
                            setMinutes(minutes);
                            setSeconds(seconds)
                        } if (props.route.params.driverPendingTrip.trip.status == "started") {
                            setData(props.route.params.driverPendingTrip.trip)
                            setIspaid(props.route.params.driverPendingTrip.trip.is_paid);
                            if (props.route.params.driverPendingTrip.trip.payment_method) {
                                setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);
                            }
                            setHiddenHeader(true)
                            setMapDirection('toDestination')
                            setOpenMap('toDestination')
                            setViewID(4)
                            setSec(-1)
                        }
                    }
                    else if (props.route.params.driverPendingTrip.trip == null) {
                        props.navigation.navigate('DriverRequests')
                        setDetailsTrip({});
                    }
                }



            }

            // pendingPackage
            if (props.route.params.pending == true) {
                if (props.route.params.driverPendingTrip.type == "DriverPackageTrip") {
                    if (props.route.params.driverPendingTrip.trip.user_package.status == "accepted") {
                        setDetailsTrip(props.route.params.driverPendingTrip.trip)
                        setTimer(false)
                        setHiddenHeader(true)
                        setViewID(3)
                        setIspaid(props.route.params.driverPendingTrip.trip.is_paid)
                        setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);
                    } if (props.route.params.driverPendingTrip.trip.user_package.status == "waiting_for_client") {
                        setDetailsTrip(props.route.params.driverPendingTrip.trip)
                        setTimer(true)
                        setMapDirection(true)
                        setHiddenHeader(true)
                        setViewID(3)
                        setIspaid(props.route.params.driverPendingTrip.trip.is_paid)
                        setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);


                        const now = moment().format('YYYY-MM-DDTHH:mm:ss').toString();
                        const offer_start_time = moment(props.route.params.driverPendingTrip.trip.user_package.driver_package_trip.arrived_at).format('YYYY-MM-DDTHH:mm:ss').toString();

                        console.log("now", now);
                        console.log("offer_start_time", offer_start_time);

                        var date1 = new Date(offer_start_time);
                        var date2 = new Date(now);

                        var timeDifference = parseInt(date2 - date1, 10);
                        var seconds = Math.floor(timeDifference / 1000);
                        var minutes = Math.floor(seconds / 60);
                        var hours = Math.floor(minutes / 60);
                        var days = Math.floor(hours / 24);

                        console.log("Time difference:", timeDifference);
                        console.log("Days:", days);
                        console.log("Hours:", hours % 24);
                        console.log("Minutes:", minutes % 60);
                        console.log("Seconds:", seconds % 60);



                        var diff = Math.abs(new Date(now) - new Date(offer_start_time));
                        var minutes = Math.floor((diff / 1000) / 60);
                        var seconds = Math.floor((diff / 1000) % 60);


                        console.log('minutes', minutes);
                        console.log('seconds', seconds);
                        setMinutes(minutes)
                        setSeconds(seconds)


                    }
                    if (props.route.params.driverPendingTrip.trip.user_package.status == "started") {
                        setData(props.route.params.driverPendingTrip.trip)
                        setHiddenHeader(true)
                        setViewID(4)
                        setMapDirection(true)
                        setIspaid(props.route.params.driverPendingTrip.trip.is_paid)
                        setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);
                    }

                }
            }


            // pendingVip
            if (props.route.params.pending == true) {
                if (props.route.params.driverPendingTrip.type == "VipTrip") {
                    if (props.route.params.driverPendingTrip.trip.status == "accepted") {
                        setDetailsTrip(props.route.params.driverPendingTrip.trip)
                        setTimer(false)
                        setHiddenHeader(true)
                        setViewID(3)
                        setIspaid(props.route.params.driverPendingTrip.trip.is_paid)
                        setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);

                    } if (props.route.params.driverPendingTrip.trip.status == "waiting_for_client") {
                        setDetailsTrip(props.route.params.driverPendingTrip.trip)
                        setTimer(true)
                        setHiddenHeader(true)
                        setViewID(3);
                        setMapDirection(true)
                        setIspaid(props.route.params.driverPendingTrip.trip.is_paid)
                        setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);

                        // let hoursNow = 0;
                        // let minsNow = 0;
                        // let secsNow = 0;
                        // let hours = 0;
                        // let mins = 0;
                        // let secs = 0;

                        // let date = moment().format('YYYY-MM-DD HH:mm:ss');
                        // let arrived_at = props.route.params.driverPendingTrip.trip.driver_vip_trip.arrived_at;
                        // let date = '2023-12-22 00:00:57';
                        // let arrived_at = '2023-12-21 23:59:56';
                        const now = moment().format('YYYY-MM-DDTHH:mm:ss').toString();
                        const offer_start_time = moment(props.route.params.driverPendingTrip.trip.driver_vip_trip.arrived_at).format('YYYY-MM-DDTHH:mm:ss').toString();

                        console.log("now", now);
                        console.log("offer_start_time", offer_start_time);

                        var date1 = new Date(offer_start_time);
                        var date2 = new Date(now);

                        var timeDifference = parseInt(date2 - date1, 10);
                        var seconds = Math.floor(timeDifference / 1000);
                        var minutes = Math.floor(seconds / 60);
                        var hours = Math.floor(minutes / 60);
                        var days = Math.floor(hours / 24);

                        console.log("Time difference:", timeDifference);
                        console.log("Days:", days);
                        console.log("Hours:", hours % 24);
                        console.log("Minutes:", minutes % 60);
                        console.log("Seconds:", seconds % 60);



                        var diff = Math.abs(new Date(now) - new Date(offer_start_time));
                        var minutes = Math.floor((diff / 1000) / 60);
                        var seconds = Math.floor((diff / 1000) % 60);

                        // var seconds = ((new Date(date).getTime() - new Date(arrived_at).getTime()) / 1000);

                        console.log('minutes', minutes);
                        console.log('seconds', seconds);
                        setMinutes(minutes)
                        setSeconds(seconds)

                        // hoursNow = date.slice(11, 13).startsWith('00') ? 0 : date.slice(11, 13).startsWith('0') ? date.slice(12, 13) : date.slice(11, 13);
                        // minsNow = date.slice(14, 16).startsWith('00') ? 0 : date.slice(14, 16).startsWith('0') ? date.slice(15, 16) : date.slice(14, 16);
                        // secsNow = date.slice(17, 19);
                        // hours = arrived_at.slice(11, 13).startsWith('00') ? 0 : arrived_at.slice(11, 13).startsWith('0') ? arrived_at.slice(12, 13) : arrived_at.slice(11, 13);
                        // mins = arrived_at.slice(14, 16).startsWith('00') ? 0 : arrived_at.slice(14, 16).startsWith('0') ? arrived_at.slice(15, 16) : arrived_at.slice(14, 16);
                        // secs = arrived_at.slice(17, 19);
                        // console.log('data now', date);
                        // console.log('hoursNow ', JSON.parse(hoursNow));
                        // console.log('minsNow ', JSON.parse(minsNow));
                        // console.log('secsNow ', JSON.parse(secsNow));
                        // console.log('hours ', JSON.parse(hours));
                        // console.log('mins ', JSON.parse(mins));
                        // console.log('secs ', JSON.parse(secs));
                        // setMinutes(JSON.parse(minsNow) - JSON.parse(mins) + ((Math.abs(JSON.parse(hoursNow) - JSON.parse(hours)) > 12 ? (24 - Math.abs(JSON.parse(hoursNow) - JSON.parse(hours))) * 60 : Math.abs(JSON.parse(hoursNow) - JSON.parse(hours)) * 60)));
                        // setSeconds(JSON.parse(secsNow) - JSON.parse(secs))
                    }
                    if (props.route.params.driverPendingTrip.trip.status == "started") {
                        setData(props.route.params.driverPendingTrip.trip)
                        setHiddenHeader(true)
                        setViewID(4)
                        setMapDirection(true)
                        setIspaid(props.route.params.driverPendingTrip.trip.is_paid)
                        setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);

                    }
                }
            }

            // pendingCityTrip
            if (props.route.params.pending == true) {
                if (props.route.params.driverPendingTrip.type == "CityTrip") {
                    if (props.route.params.driverPendingTrip.trip.status == "accepted") {
                        setDetailsTrip(props.route.params.driverPendingTrip.trip)
                        setTimer(false)
                        setHiddenHeader(true)
                        setViewID(3)
                        setIspaid(props.route.params.driverPendingTrip.trip.is_paid)
                        setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);
                    } if (props.route.params.driverPendingTrip.trip.status == "waiting_for_client") {
                        setDetailsTrip(props.route.params.driverPendingTrip.trip)
                        setTimer(true)
                        setHiddenHeader(true)
                        setViewID(3)
                        setMapDirection(true)
                        setIspaid(props.route.params.driverPendingTrip.trip.is_paid)
                        setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);

                        // let date = moment().format('YYYY-MM-DD HH:mm:ss');
                        // let arrived_at = props.route.params.driverPendingTrip.trip.driver_city_trip.arrived_at;
                        // let date = '2023-12-22 00:00:57';
                        // let arrived_at = '2023-12-21 23:59:56';
                        const now = moment().format('YYYY-MM-DDTHH:mm:ss').toString();
                        const offer_start_time = moment(props.route.params.driverPendingTrip.trip.driver_city_trip.arrived_at).format('YYYY-MM-DDTHH:mm:ss').toString();

                        console.log("now", now);
                        console.log("offer_start_time", offer_start_time);

                        var date1 = new Date(offer_start_time);
                        var date2 = new Date(now);

                        var timeDifference = parseInt(date2 - date1, 10);
                        var seconds = Math.floor(timeDifference / 1000);
                        var minutes = Math.floor(seconds / 60);
                        var hours = Math.floor(minutes / 60);
                        var days = Math.floor(hours / 24);

                        console.log("Time difference:", timeDifference);
                        console.log("Days:", days);
                        console.log("Hours:", hours % 24);
                        console.log("Minutes:", minutes % 60);
                        console.log("Seconds:", seconds % 60);



                        var diff = Math.abs(new Date(now) - new Date(offer_start_time));
                        var minutes = Math.floor((diff / 1000) / 60);
                        var seconds = Math.floor((diff / 1000) % 60);

                        // var seconds = ((new Date(date).getTime() - new Date(arrived_at).getTime()) / 1000);

                        setMinutes(minutes)
                        setSeconds(seconds)

                    }
                    if (props.route.params.driverPendingTrip.trip.status == "started") {
                        setData(props.route.params.driverPendingTrip.trip)
                        setHiddenHeader(true)
                        setViewID(4)
                        setMapDirection(true)
                        setIspaid(props.route.params.driverPendingTrip.trip.is_paid)
                        setPayment_methodId(props.route.params.driverPendingTrip.trip.payment_method.id);

                    }
                }
            }

        };

        pindingTrips()
    }, [Reload, props,]);



    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    // dailyTrip
    const sendOffer = async (price) => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverOffersActions.sendOffer(props.route.params.trip.id, price));

            if (response.success == true) {
                setHiddenHeader(true)
                setOfferId(response.data.id)
                setLoadingMore(false)
                console.log('price', price);
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Orderconfirmed'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setViewID(2)
                setSec(45);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const confirmArrival = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverDailyTripsActions.confirmArrival(
                props.route.params.driverPendingOffer != null ?
                    props.route.params.driverPendingOffer.daily_trip_id :
                    props.route.params.trip.id
            ));

            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Orderconfirmed'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setTripId(response.data.id);
                setOpenMap('toDestination')
                setMapDirection('toDestination')
                setTimer(true);
                setMapDirection(true);
                setMinutes(0);
                setSeconds(0);
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const cancelOffer = async () => {
        if (reasonsId == null && reasonText == '') {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.Pleasechooseorwriteareasonforcancellation'),
                White,
                1500,
                screenHeight / 50,
            );
        } else {
            try {
                setLoadingMore(true)
                let response = await dispatch(driverOffersActions.cancelOffer(offerId, reasonsId, reasonText));

                if (response.success == true) {
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Orderconfirmed'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    setTripId(response.data.id);
                    props.navigation.navigate('DriverRequests');
                    setViewID(0)
                    refRBSheetReasons.current.close();
                    setLoadingMore(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false)
                }
            } catch (err) {
                console.log(err);
                setLoadingMore(false)
            }
        }
    }

    const confirmClientArrival = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverDailyTripsActions.confirmClientArrival(
                props.route.params.driverPendingOffer != null ?
                    props.route.params.driverPendingOffer.daily_trip_id :
                    props.route.params.trip.id
            ));

            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Orderconfirmed'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setTimer(false);
                setTripId(response.data.id);
                setData(response.data);
                setViewID(4)
                setMinutes(0);
                setSeconds(0);
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const finishTrip = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverDailyTripsActions.finishTrip(
                props.route.params.driverPendingOffer != null ?
                    props.route.params.driverPendingOffer.daily_trip_id :
                    props.route.params.trip.id
            ));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Thetripwascompletedsuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                modalizeRefRat.current.open();
                refRBSheetIsPaid.current.close();
                setViewID(5)
                setMapDirection(false)
                setData(response.data)
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const leaveReview = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverDailyTripsActions.leaveReview(
                props.route.params.driverPendingOffer != null ?
                    props.route.params.driverPendingOffer.daily_trip_id :
                    props.route.params.trip.id,
                starCount,
                review
            ));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Thetripwascompletedsuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setViewID(0)
                setStarCount(0)
                modalizeRefRat.current.close()
                props.navigation.navigate('DriverRequests');
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }


    // packageTrip
    const updateStatusPackage = async (status) => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverPackagesActions.updateStatus(props.route.params.trip.id, status));
            if (response.success == true) {
                if (status == 'accepted') {
                    setHiddenHeader(true)
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Orderconfirmed'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    setTripId(response.data.id);
                    setData(response.data);
                    setViewID(3)
                    setLoadingMore(false)
                } else if (status == 'rejected') {
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Thetripwassuccessfullyrejected'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    props.navigation.navigate('DriverRequests')
                    setLoadingMore(false)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const confirmArrivalPackage = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverPackagesActions.confirmArrival(props.route.params.trip.id));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Orderconfirmed'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setTripId(response.data.id);
                setTimer(true);
                setMinutes(0);
                setSeconds(0);
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const confirmClientArrivalPackageTrip = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverPackagesActions.confirmClientArrival(props.route.params.trip.id));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Orderconfirmed'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setTimer(false);
                setTripId(response.data.id);
                setData(response.data);
                setViewID(4)
                setMinutes(0);
                setSeconds(0);
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const finishTripPackages = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverPackagesActions.finishTrip(props.route.params.trip.id));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Thetripwascompletedsuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                modalizeRefRat.current.open();
                refRBSheetIsPaid.current.close();
                setViewID(5)
                setData(response.data)
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const cancelPackageTrip = async () => {
        if (reasonsId == null && reasonText == '') {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.Pleasechooseorwriteareasonforcancellation'),
                White,
                1500,
                screenHeight / 50,
            );
        } else {
            try {
                setLoadingMore(true)
                let response = await dispatch(driverPackagesActions.cancel(props.route.params.trip.id, reasonsId, reasonText));
                if (response.success == true) {
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Successfullycancelled'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    refRBSheetReasons.current.close();
                    setViewID(0)
                    props.navigation.navigate('DriverRequests')
                    setLoadingMore(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false)
                }
            } catch (err) {
                console.log(err);
                setLoadingMore(false)
            }
        };
    };

    const reportTripPackages = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverPackagesActions.report(props.route.params.trip.id));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Reportedsuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                refRBSheet.current.close();
                props.navigation.navigate('DriverRequests')
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const leaveReviewPackages = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverPackagesActions.leaveReview(
                props.route.params.trip.id,
                starCount,
                review
            ));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Thetripwascompletedsuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setViewID(0)
                setStarCount(0)
                modalizeRefRat.current.close()
                props.navigation.navigate('DriverRequests');
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }


    // VipTrip
    const updateStatusVip = async (status) => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverVipTripsActions.updateStatus(props.route.params.trip.id, status));
            if (response.success == true) {
                if (status == 'accepted') {
                    setHiddenHeader(true)
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Orderconfirmed'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    setTripId(response.data.id);
                    setData(response.data);
                    setViewID(3)
                    setLoadingMore(false)
                } else if (status == 'rejected') {
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Thetripwassuccessfullyrejected'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    props.navigation.navigate('DriverRequests')
                    setLoadingMore(false)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const confirmArrivalVipTrip = async () => {
        let expected_distance = 0;
        if (origin.latitude && origin.longitude && destination.latitude && destination.longitude) {
            let GOOGLE_DISTANCES_API_KEY = 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'
            let ApiURL = "https://maps.googleapis.com/maps/api/distancematrix/json?";
            let mode = 'driving'
            let params = `origins=${origin.latitude},${origin.longitude}&destinations=${destination.latitude},${destination.longitude}&mode=${mode}&key=${GOOGLE_DISTANCES_API_KEY}`;
            let finalApiURL = `${ApiURL}${encodeURI(params)}`;

            console.log("finalApiURL:\n");
            console.log(finalApiURL);

            // get duration/distance from base to each target
            try {
                let response = await fetch(finalApiURL);
                let responseJson = await response.json();
                console.log("responseJson:\n");
                console.log(responseJson);
                let distance = (responseJson.rows[0].elements[0].distance.value);
                let duration = (Math.round(responseJson.rows[0].elements[0].duration.value / 60));
                expected_distance = distance
            }
            catch (error) {
                console.error(error);
            }
        }
        console.log('sad', expected_distance);

        try {
            setLoadingMore(true)
            let response = await dispatch(driverVipTripsActions.confirmArrival(props.route.params.trip.id, expected_distance));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Orderconfirmed'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setTripId(response.data.id);
                setTimer(true);
                setMinutes(0);
                setSeconds(0);
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const confirmClientArrivalVipTrip = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverVipTripsActions.confirmClientArrival(props.route.params.trip.id));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Orderconfirmed'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setTimer(false);
                setTripId(response.data.id);
                setData(response.data);
                setViewID(4)
                setMinutes(0);
                setSeconds(0);
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const finishVipTrip = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverVipTripsActions.finishTrip(props.route.params.trip.id));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Thetripwascompletedsuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                modalizeRefRat.current.open();
                setViewID(5)
                setData(response.data)
                setLoadingMore(false);
                refRBSheetIsPaid.current.close();
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const cancelVipTrip = async () => {
        if (reasonsId == null && reasonText == '') {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.Pleasechooseorwriteareasonforcancellation'),
                White,
                1500,
                screenHeight / 50,
            );
        } else {
            try {
                setLoadingMore(true)
                let response = await dispatch(driverVipTripsActions.cancel(props.route.params.trip.id, reasonsId, reasonText));
                if (response.success == true) {
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Successfullycancelled'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    refRBSheetReasons.current.close();
                    setViewID(0)
                    props.navigation.navigate('DriverRequests')
                    setLoadingMore(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false)
                }
            } catch (err) {
                console.log(err);
                setLoadingMore(false)
            }
        };
    };

    const reportVipTrip = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverVipTripsActions.report(props.route.params.trip.id, report));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Reportedsuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                refRBSheet.current.close();
                props.navigation.navigate('DriverRequests')
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const leaveReviewVip = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverVipTripsActions.leaveReview(
                props.route.params.trip.id,
                starCount,
                review
            ));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Thetripwascompletedsuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setViewID(0)
                setStarCount(0)
                modalizeRefRat.current.close()
                props.navigation.navigate('DriverRequests');
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }


    // betwwenCitiesTrip
    const updateStatusCity = async (status) => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverCityTripsActions.updateStatus(props.route.params.trip.id, status));
            if (response.success == true) {
                if (status == 'accepted') {
                    setHiddenHeader(true)
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Orderconfirmed'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    setTripId(response.data.id);
                    setData(response.data);
                    setViewID(3)
                    setLoadingMore(false)
                } else if (status == 'rejected') {
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Thetripwassuccessfullyrejected'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    props.navigation.navigate('DriverRequests')
                    setLoadingMore(false)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const confirmArrivalCityTrip = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverCityTripsActions.confirmArrival(props.route.params.trip.id));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Orderconfirmed'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setTripId(response.data.id);
                setTimer(true);
                setMinutes(0);
                setSeconds(0);
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const confirmClientArrivalCityTrip = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverCityTripsActions.confirmClientArrival(props.route.params.trip.id));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Orderconfirmed'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setTimer(false);
                setTripId(response.data.id);
                setData(response.data);
                setViewID(4)
                setMinutes(0);
                setSeconds(0);
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const finishCityTrip = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverCityTripsActions.finishTrip(props.route.params.trip.id));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Thetripwascompletedsuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                modalizeRefRat.current.open();
                setViewID(5)
                setData(response.data)
                setLoadingMore(false);
                refRBSheetIsPaid.current.close();
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const canceCityTrip = async () => {
        if (reasonsId == null || reasonText == '') {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.Pleasechooseorwriteareasonforcancellation'),
                White,
                1500,
                screenHeight / 50,
            );
        } else {
            try {
                setLoadingMore(true)
                let response = await dispatch(driverCityTripsActions.cancelTrip(props.route.params.trip.id, reasonsId, reasonText));
                if (response.success == true) {
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Successfullycancelled'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    refRBSheetReasons.current.close();
                    props.navigation.navigate('DriverRequests')
                    setViewID(0)
                    setLoadingMore(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false)
                }
            } catch (err) {
                console.log(err);
                setLoadingMore(false)
            }
        };
    };

    const reportCityTrip = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverCityTripsActions.report(props.route.params.trip.id, report));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Reportedsuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                refRBSheet.current.close();
                props.navigation.navigate('DriverRequests')
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const leaveReviewCity = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(driverCityTripsActions.leaveReview(
                props.route.params.trip.id,
                starCount,
                review
            ));
            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.Thetripwascompletedsuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                dispatch({ type: CLEARUPDATEDCITYTRIP1 });

                setViewID(0)
                setStarCount(0)
                modalizeRefRat.current.close()
                props.navigation.navigate('DriverRequests');
                setLoadingMore(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const get_Lat_User = async () => {
        // Geolocation.requestAuthorization();
        // Geolocation.setRNConfiguration({
        //   skipPermissionRequests: false,
        //   authorizationLevel: 'whenInUse',
        // });
        if (Platform.OS == 'ios') {
            RequestIosPermissio()

        }
        // console.log('SetRegion', region);
        Geolocation.getCurrentPosition(
            (position) => {
                console.log('position', position);
                var lat = parseFloat(position.coords.latitude)
                var longi = parseFloat(position.coords.longitude)
                var initialRegion = {
                    latitude: lat,
                    longitude: longi,
                    longitudeDelta: 0.01 * ASPECT_RATIO,
                    latitudeDelta: 0.01
                }
                SetRegion(initialRegion)
                SetLat(lat)
                SetLng(longi)
            },
            (error) => {
                console.log('error', error);

            },
            { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 1000 },
        );
        setLoading(false)
    }

    // const openAddressOnMapToClient = () => {
    //     openMap({ latitude: latClient, longitude: lngClient });
    //     createMapLink({ provider: 'google', query: labelAddressClient, latitude: latClient, longitude: lngClient })
    //     const latitude = latClient;
    //     const longitude = lngClient;
    //     const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
    //     Linking.openURL(url);

    //     const scheme = Platform.select({
    //         // ios: 'maps:0,0?q=',
    //         ios: 'geo:0,0?q=',
    //         android: 'geo:0,0?q=',
    //     });
    //     const latLng = `${latClient},${lngClient}`;
    //     const label = labelAddressClient;
    //     const url = Platform.select({
    //         ios: `${scheme}${label}@${latLng}`,
    //         android: `${scheme}${latLng}(${label})`,
    //     });
    //     Linking.openURL(url);
    // };
    const openAddressOnMapToClient = async () => {
        if (Platform.OS == 'ios') {
            const latitude = latClient; // Replace with desired latitude
            const longitude = lngClient; // Replace with desired longitude
            const label = labelAddressClient; // Replace with desired label
            const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;
            Linking.canOpenURL(url)
                .then((supported) => {
                    if (supported) {
                        Linking.openURL(url);
                    } else {
                        console.log('Don\'t know how to open URI: ' + url);
                    }
                })
                .catch((err) => console.error('An error occurred', err));
        } else {
            const scheme = Platform.select({
                // ios: 'maps:0,0?q=',
                // ios: 'geo:0,0?q=',
                android: 'geo:0,0?q=',
            });
            const latLng = `${latClient},${lngClient}`;
            const label = labelAddressClient;
            const url = Platform.select({
                ios: `${scheme}${label}@${latLng}`,
                android: `${scheme}${latLng}(${label})`,
            });
            Linking.openURL(url);
        }
    };

    const openAddressOnMapToDestination = () => {
        if (Platform.OS == 'ios') {
            const latitude = latDistenation; // Replace with desired latitude
            const longitude = lngDistenation; // Replace with desired longitude
            const label = labelDistenation; // Replace with desired label
            const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;
            Linking.canOpenURL(url)
                .then((supported) => {
                    if (supported) {
                        Linking.openURL(url);
                    } else {
                        console.log('Don\'t know how to open URI: ' + url);
                    }
                })
                .catch((err) => console.error('An error occurred', err));
        } else {
            const scheme = Platform.select({
                // ios: 'maps:0,0?q=',
                // ios: 'geo:0,0?q=',
                android: 'geo:0,0?q=',
            });
            const latLng = `${latDistenation},${lngDistenation}`;
            const label = labelDistenation;
            const url = Platform.select({
                ios: `${scheme}${label}@${latLng}`,
                android: `${scheme}${latLng}(${label})`,
            });
            Linking.openURL(url);
        }

    };


    return (
        <View style={{ flex: 1, alignItems: 'center', backgroundColor: WhiteGery }}>
            {loadingMore ? <Loading /> : <></>}
            {hiddenHeader ?
                <></>
                :
                <View style={{ position: 'absolute', top: screenHeight / 18, start: '6%', backgroundColor: White, width: screenWidth / 10, zIndex: 10000000, alignSelf: 'center', height: screenWidth / 10, borderRadius: screenWidth / 20, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                    <TouchableOpacity
                        onPress={() => { props.navigation.navigate('DriverRequests'); }}
                        // onPress={() => { props.navigation.navigate('More'); }}
                        style={{ width: screenWidth / 8, alignItems: 'center', justifyContent: 'center', height: '100%', }}>
                        <Image source={require('../images/Back.png')} style={{ height: '40%', resizeMode: 'contain', transform: I18nManager.isRTL ? [{ scaleX: 1 }] : [{ scaleX: -1 }] }} />
                    </TouchableOpacity>
                </View>
            }
            {ViewID == 3 && timer == false
                ?
                <View style={{ position: 'absolute', top: screenHeight / 1.8, end: '6%', backgroundColor: DarkBlue, width: screenWidth / 2.5, zIndex: 10000000, alignSelf: 'center', height: screenWidth / 10, borderRadius: 15, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                    <TouchableOpacity
                        onPress={() => { openAddressOnMapToClient() }}
                        // onPress={() => { props.navigation.navigate('More'); }}
                        style={{ width: '100%', alignItems: 'center', justifyContent: 'center', height: '100%', }}>
                        <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White }}>{strings('lang.Displaythedestinationtothecustomer')}</Text>

                    </TouchableOpacity>
                </View>
                :
                <></>
            }
            {((ViewID == 3 && timer == true) || ViewID == 4)
                ?
                <View style={{ position: 'absolute', top: ViewID == 4 ? screenHeight / 1.8 : screenHeight / 2.1, end: '6%', backgroundColor: DarkBlue, width: screenWidth / 2.5, zIndex: 10000000, alignSelf: 'center', height: screenWidth / 10, borderRadius: 15, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                    <TouchableOpacity
                        onPress={() => { openAddressOnMapToDestination() }}
                        // onPress={() => { props.navigation.navigate('More'); }}
                        style={{ width: '100%', alignItems: 'center', justifyContent: 'center', height: '100%', }}>
                        <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White }}>{strings('lang.Showdestination')}</Text>

                    </TouchableOpacity>
                </View>
                :
                <></>
            }


            {ViewID == 2
                ?
                <View style={{
                    height: screenHeight / 1,
                    width: '100%',
                    alignSelf: 'center',
                    backgroundColor: 'rgba(119, 119, 119,0.5)',
                    alignItems: 'center',
                    // opacity: .5,
                    justifyContent: 'center',
                    zIndex: 10000000,
                    position: 'absolute'
                }}>
                    <Text style={{ fontSize: screenWidth / 10, fontFamily: appFontBold, color: Black }}>{min}:{sec < 10 && 0}{sec}</Text>
                    <Text style={{ fontSize: screenWidth / 20, fontFamily: appFontBold, color: Black }}>{strings('lang.message21')}</Text>
                </View>
                :
                <></>}



            {/* <View style={{ position: 'absolute', top: screenHeight / 20, end: '6%', backgroundColor: White, width: screenWidth / 10, zIndex: 1000, alignSelf: 'center', height: screenWidth / 10, borderRadius: screenWidth / 20, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                <TouchableOpacity
                    onPress={() => { props.navigation.openDrawer(); }}
                    style={{ width: screenWidth / 8, alignItems: 'center', justifyContent: 'center', height: '100%', }}>
                    <Image source={require('../images//filter.png')} style={{ height: '50%', resizeMode: 'contain', tintColor: Black, }} />
                </TouchableOpacity>
            </View> */}

            {/* <View style={{ position: 'absolute', top: screenHeight / 20, start: '6%', backgroundColor: White, width: screenWidth / 10, zIndex: 1000, alignSelf: 'center', height: screenWidth / 10, borderRadius: screenWidth / 20, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                <TouchableOpacity
                    onPress={() => { props.navigation.openDrawer(); }}
                    style={{ width: screenWidth / 8, alignItems: 'center', justifyContent: 'center', height: '100%', }}>
                    <Image source={require('../images//filter.png')} style={{ height: '50%', resizeMode: 'contain', tintColor: Black, }} />
                </TouchableOpacity>
            </View> */}

            {/* <View style={{ zIndex: 1000000, width: screenWidth / 8, height: screenWidth / 8, position: "absolute", bottom: '15%', left: '7%', borderRadius: screenWidth / 16, elevation: 20, shadowColor: DarkGrey, shadowOpacity: 1, shadowOffset: { width: 1, height: 1 }, backgroundColor: "white", alignItems: 'center', justifyContent: 'center' }} >
                <TouchableOpacity
                    onPress={() => {
                        get_Lat_User()
                    }}
                >
                    <Image
                        style={{ width: 25, height: 25, resizeMode: 'contain', tintColor: DarkBlue }}
                        source={require('../images/mylocation.png')}
                    />
                </TouchableOpacity>
            </View> */}

            <MapView
                initialRegion={region}
                region={region}
                onRegionChangeComplete={null}
                style={{ width: "100%", height: ViewID == 2 ? screenHeight : screenHeight / 1.5, zIndex: 1000 }}
            >
                {/* {MapDirection == 'toClient'
                    &&
                    <> */}
                {lat && lng
                    ?
                    <Marker
                        coordinate={{
                            latitude: lat,
                            longitude: lng
                        }}
                        style={{ width: 20, height: 20 }}
                        resizeMode="contain"
                        image={require('../images/carxx.png')}
                    />
                    :
                    <></>
                }
                {origin.latitude && origin.longitude
                    ?
                    <Marker
                        coordinate={{
                            latitude: origin.latitude,
                            longitude: origin.longitude
                        }}
                        style={{ width: 20, height: 20 }}
                        resizeMode="contain"
                        image={require('../images/Group-288.png')}
                    />
                    :
                    <></>
                }
                {destination.latitude && destination.longitude
                    ?
                    <Marker
                        coordinate={{
                            latitude: destination.latitude,
                            longitude: destination.longitude
                        }}
                        style={{ width: 20, height: 20 }}
                        resizeMode="contain"
                        image={require('../images/Group-277.png')}
                    />
                    :
                    <></>
                }



                {detailsTrip && (detailsTrip.status != "started") && lat && lng && origin.latitude && origin.longitude
                    ?
                    <MapViewDirections
                        origin={{ latitude: lat, longitude: lng }}
                        destination={origin}
                        // origin={origin}
                        // destination={destination}
                        apikey={'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'}
                        strokeWidth={3}
                        strokeColor={DarkBlue}
                    />
                    :
                    <></>
                }
                {origin.latitude && origin.longitude && destination.latitude && destination.longitude
                    ?
                    <MapViewDirections
                        origin={origin}
                        destination={destination}
                        apikey={'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'}
                        strokeWidth={3}
                        strokeColor={Red}
                    />
                    :
                    <></>
                }


                {/* </>
               } */}
                {/* {MapDirection == 'toDestination'
                    &&
                    <> */}

                {/* <MapViewDirections
                            origin={origin}
                            destination={destination}
                            apikey={'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'}
                            strokeWidth={3}
                            strokeColor={Red}
                        /> */}
                {/* </>
                } */}


            </MapView>

            {/* all trips */}
            {ViewID == 0 &&
                <View style={[
                    styles.bottomView,
                    {
                        height: screenHeight / 1.8,
                        backgroundColor: WhiteGery,
                        width: screenWidth,
                        zIndex: 10000000
                    },
                ]}
                >
                    <ScrollView
                        style={{ width: '100%', alignSelf: 'center', paddingBottom: '10%', paddingTop: '4%', }}
                        showsVerticalScrollIndicator={false}
                    >
                        <View style={styles.modal}>
                            <View style={{ width: '95%', height: screenHeight / 15, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: .8, borderBottomColor: MediumGrey }}>
                                {request == 2
                                    ?
                                    <View style={{ width: '60%', height: '90%', flexDirection: 'row', alignItems: 'center', }}>
                                        <Image source={detailsTrip.user_package && detailsTrip.user_package.user && detailsTrip.user_package.user.image ? { uri: detailsTrip.user_package.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '90%', resizeMode: 'contain', width: '20%' }} />
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: 20, marginStart: '2%' }}>{detailsTrip.user_package && detailsTrip.user_package.user && detailsTrip.user_package.user.name}</Text>
                                    </View>
                                    :
                                    <View style={{ width: '70%', height: '90%', flexDirection: 'row', alignItems: 'center', }}>
                                        <Image source={detailsTrip.user && detailsTrip.user.image ? { uri: detailsTrip.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: screenWidth / 10, resizeMode: 'contain', width: screenWidth / 10 }} />
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: 20, marginStart: '2%' }}>{detailsTrip.user && detailsTrip.user.name}</Text>
                                    </View>
                                }

                                {request == 4 || request == 3 ?
                                    <View style={{ width: '20%', height: '90%', alignItems: 'flex-start', justifyContent: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: MediumGreen, marginStart: '1%' }}>{detailsTrip.price} {strings('lang.SR')}</Text>
                                    </View>
                                    :
                                    request == 2
                                        ?
                                        <View style={{ width: '40%', height: '90%', alignItems: 'flex-start', justifyContent: 'center' }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: MediumGreen, marginStart: '1%' }}>{detailsTrip.user_package && detailsTrip.user_package.package && detailsTrip.user_package.package.name} </Text>
                                        </View>
                                        :
                                        <View style={{ width: '20%', height: '90%', alignItems: 'flex-start', justifyContent: 'center' }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: MediumGreen, marginStart: '1%' }}>{five_offer && detailsTrip.original_price ? detailsTrip.original_price : detailsTrip.client_price} {strings('lang.SR')}</Text>
                                        </View>
                                }
                            </View>


                            <View style={[styles.container, { borderBottomWidth: request == 4 ? .8 : 0, borderBottomColor: MediumGrey }]}>
                                <View style={styles.imageContainer}>
                                    <View style={styles.image}>
                                        <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                                    </View>
                                    <View style={{ height: '50%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: Green, zIndex: 0, }}></View>
                                    <View style={styles.image}>
                                        <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                                    </View>
                                </View>

                                {
                                    // request == 4
                                    //     ?

                                    //     <View style={styles.addressContainer}>
                                    //         <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.from_city_id ? detailsTrip.from_city.name : detailsTrip.from_address}</Text>
                                    //         {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, marginBottom: '4%', color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}
                                    //         <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.to_city_id ? detailsTrip.to_city.name : detailsTrip.to_address}</Text>
                                    //         {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}
                                    //     </View>
                                    //     :
                                    request == 2
                                        ?
                                        <View style={styles.addressContainer}>
                                            <Text
                                                style={[styles.blackText,
                                                {
                                                    fontSize: screenWidth / 35,
                                                    lineHeight: screenHeight / 50,
                                                    textAlign: I18nManager.isRTL ? 'left' : 'right'
                                                }]}>
                                                {detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].from_address_id
                                                    ? detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].address_from.address
                                                    : detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].from_address}</Text>
                                            {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, marginBottom: '4%', color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}
                                            <Text
                                                style={[styles.blackText,
                                                {
                                                    fontSize: screenWidth / 35,
                                                    lineHeight: screenHeight / 50,
                                                    textAlign: I18nManager.isRTL ? 'left' : 'right'
                                                }]}>
                                                {detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].to_address_id
                                                    ? detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].address_to.address
                                                    : detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].to_address}</Text>
                                            {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}
                                        </View>
                                        :
                                        <View style={styles.addressContainer}>
                                            <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.from_address_id ? detailsTrip.address_from.address : detailsTrip.from_address}</Text>
                                            {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, marginBottom: '4%', color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}
                                            <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.to_address_id ? detailsTrip.address_to.address : detailsTrip.to_address}</Text>
                                            {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}
                                        </View>
                                }

                            </View>

                            {detailsTrip.notes &&
                                <>
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 35, color: DarkGrey, alignSelf: 'flex-start' }]}>{strings('lang.note')}</Text>
                                    <View style={{
                                        borderColor: MediumGrey,
                                        width: '95%',
                                        alignSelf: 'center',
                                        padding: '1%'
                                    }}
                                    >
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 30, alignSelf: 'flex-start' }]}>{detailsTrip.notes}</Text>
                                    </View>

                                </>

                            }


                            {request == 4 || request == 3 ?
                                <>
                                    <View
                                        style={{
                                            borderBottomColor: MediumGrey, borderBottomWidth: .8, height: screenHeight / 20,
                                            width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                                        }}>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{request == 4 ? strings('lang.Tripdate') : strings('lang.timetomoveon')}</Text>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{request == 4 ? detailsTrip.date : detailsTrip.time}</Text>
                                    </View>
                                    <View
                                        style={{
                                            borderBottomColor: MediumGrey, borderBottomWidth: .8, height: screenHeight / 20,
                                            width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                                        }}>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{strings('lang.Numberofindividuals')}</Text>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{detailsTrip.passengers_count}</Text>
                                    </View>
                                    {request == 3 &&
                                        <View
                                            style={{
                                                borderBottomColor: MediumGrey, borderBottomWidth: .8, height: screenHeight / 20,
                                                width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                                            }}>
                                            <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{strings('lang.Thenumberofseatsinthecar')}</Text>
                                            <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{detailsTrip.seat_count}</Text>
                                        </View>
                                    }
                                </>
                                :
                                request == 2
                                    ?
                                    <>
                                        <View
                                            style={{
                                                borderBottomColor: MediumGrey, borderBottomWidth: .8, height: screenHeight / 20,
                                                width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                                            }}>
                                            <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{strings('lang.Tripdate')}</Text>
                                            <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{detailsTrip.user_package && detailsTrip.user_package.start_date_formatted}</Text>
                                        </View>
                                        <View
                                            style={{
                                                borderBottomColor: MediumGrey, borderBottomWidth: .8, height: screenHeight / 20,
                                                width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                                            }}>
                                            <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{strings('lang.timetomoveon')}</Text>
                                            <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{detailsTrip.user_package && detailsTrip.user_package.start_time}</Text>
                                        </View>

                                    </>
                                    :
                                    <>

                                        {/* <TextInput
                                    autoFocus={true}
                                    onChangeText={text => { setMinutes(text); }}
                                    value={minutes}
                                    keyboardType={'number-pad'}
                                    placeholderTextColor={Black}
                                    placeholder={strings('lang.Enterthearrivaltimeinminutes')}
                                    style={{
                                        alignItems: 'center', justifyContent: 'center', textAlignVertical: 'center', backgroundColor: WhiteGery,
                                        fontFamily: appFont,
                                        fontSize: screenWidth / 33, paddingHorizontal: '5%', marginBottom: '1%',
                                        textAlign: I18nManager.isRTL ? 'right' : 'left', color: Black, width: '95%', borderRadius: 20, height: screenHeight / 22
                                    }}
                                /> */}
                                        <View
                                            style={{
                                                width: '100%',
                                                flexDirection: 'row',
                                                justifyContent: 'center',
                                                alignSelf: 'center',
                                                alignItems: 'center',
                                                // marginTop: screenHeight / 100,
                                                height: screenHeight / 16,
                                            }}
                                        >
                                            <Button
                                                onPress={() => { sendOffer(detailsTrip.client_price) }}
                                                style={{
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    alignSelf: 'center',
                                                    width: '95%',
                                                    height: screenHeight / 22,
                                                    backgroundColor: DarkBlue,
                                                    borderRadius: 20,
                                                }}
                                            >
                                                <Text
                                                    style={{
                                                        fontFamily: appFontBold,
                                                        fontSize: screenWidth / 28,
                                                        color: White,
                                                    }}
                                                >
                                                    {strings('lang.Acceptanceinreturn')} {five_offer && detailsTrip.original_price ? detailsTrip.original_price : detailsTrip.client_price} {strings('lang.SR')}
                                                </Text>
                                            </Button>

                                        </View>



                                    </>
                            }

                            {request == 1
                                ?
                                five_offer
                                    ?
                                    <View style={{ height: screenHeight / 20, width: '95%', backgroundColor: DarkYellow, borderWidth: 0, borderColor: DarkGrey, borderRadius: 20, alignItems: 'center', justifyContent: 'center', marginTop: 10 }}>
                                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: DarkBlue, }}>{strings('lang.message24')}</Text>
                                    </View>
                                    :
                                    <>
                                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start', marginVertical: '3%', marginStart: '2.5%' }}>{strings('lang.Showyourquote')}</Text>
                                        <View style={{ height: screenHeight / 20, width: '95%', marginBottom: '2%', alignItems: 'center', justifyContent: 'space-between', alignSelf: 'center', flexDirection: 'row' }}>
                                            {chosePrice.map((item, index) => {
                                                return (
                                                    <Button
                                                        onPress={() => { setChoosePriceId(item.price) }}
                                                        style={{ height: '100%', width: screenWidth / 5, backgroundColor: choosePriceId == item.price ? DarkBlue : White, borderWidth: 1, borderColor: MediumGrey, borderRadius: 10, alignItems: 'center', justifyContent: 'center', }}>
                                                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: choosePriceId == item.price ? White : Black, }}>{item.price} {strings('lang.SR')}</Text>
                                                    </Button>
                                                )
                                            })}

                                            <Button
                                                onPress={() => { setViewID(1) }}
                                                style={{ height: '100%', backgroundColor: White, width: screenWidth / 5, borderRadius: 20, alignItems: 'center', justifyContent: 'center' }}>
                                                <Image source={require('../images/edit.png')} style={{ resizeMode: 'contain', width: '20%', tintColor: Black }} />
                                            </Button>
                                        </View>
                                    </>
                                :
                                <></>
                            }


                            {request == 4 || request == 3 || request == 2 ?
                                <View
                                    style={{
                                        width: '100%',
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        alignSelf: 'center',
                                        alignItems: 'center',
                                        marginTop: '10%',
                                        height: screenHeight / 16,
                                    }}
                                >
                                    <Button
                                        onPress={() => {
                                            if (request == 2) {
                                                updateStatusPackage('accepted')
                                            } else if (request == 3) {
                                                updateStatusVip('accepted')
                                            } else if (request == 4) {
                                                updateStatusCity('accepted')
                                            }

                                        }}
                                        style={{
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                            width: '63%',
                                            height: screenHeight / 22,
                                            backgroundColor: DarkBlue,
                                            borderRadius: 20,
                                        }}
                                    >

                                        <Text
                                            style={{
                                                fontFamily: appFontBold,
                                                fontSize: screenWidth / 28,
                                                color: White,
                                            }}
                                        >
                                            {strings('lang.RequestAccept')}
                                        </Text>
                                    </Button>
                                    <Button
                                        onPress={() => {
                                            if (request == 2) {
                                                updateStatusPackage('rejected')
                                            } else if (request == 3) {
                                                updateStatusVip('rejected')
                                            } else if (request == 4) {
                                                updateStatusCity('rejected')
                                            }
                                        }}
                                        style={{
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                            width: '35%',
                                            height: screenHeight / 22,
                                            backgroundColor: appColor1,
                                            borderRadius: 20,
                                        }}
                                    >

                                        <Text
                                            style={{
                                                fontFamily: appFontBold,
                                                fontSize: screenWidth / 28,
                                                color: White,
                                            }}
                                        >
                                            {strings('lang.Refusal')}
                                        </Text>
                                    </Button>
                                </View>
                                :
                                choosePriceId
                                    ?
                                    <View
                                        style={{
                                            width: '100%',
                                            flexDirection: 'row',
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                            alignItems: 'center',
                                            // marginTop: screenHeight / 100,
                                            height: screenHeight / 22, marginTop: '3%'
                                        }}
                                    >
                                        <Button
                                            onPress={() => { sendOffer(choosePriceId) }}
                                            style={{
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                alignSelf: 'center',
                                                width: '95%',
                                                height: screenHeight / 22,
                                                backgroundColor: DarkBlue,
                                                borderRadius: 20,
                                            }}
                                        >

                                            <Text
                                                style={{
                                                    fontFamily: appFontBold,
                                                    fontSize: screenWidth / 28,
                                                    color: White,
                                                }}
                                            >
                                                {strings('lang.Acceptanceinreturn')} {choosePriceId} {strings('lang.SR')}
                                            </Text>
                                        </Button>
                                    </View>
                                    :

                                    <></>
                            }
                        </View>
                        <View style={{ height: screenHeight / 7 }}></View>
                    </ScrollView>
                </View>
            }


            <>
                {/* <Modalize
                ref={modalizeRef}
                contentRef={contentRef}
                disableScrollIfPossible={false}
                modalHeight={screenHeight / 1.8}
                // modalTopOffset={screenHeight / 1}
                alwaysOpen={request == 4 || request == 3
                    ?
                    screenHeight / 2
                    :
                    request == 2
                        ?
                        screenHeight / 2.2
                        :
                        screenHeight / 1.8}
                // alwaysOpen={screenHeight / 2}
                // snapPoint={100}
                handlePosition={'inside'}
                // adjustToContentHeight={true}
                // useNativeDriver={false}
                panGestureEnabled={true}
                keyboardAvoidingBehavior={'padding'}
                dragToss={0.05}
                threshold={150}
                velocity={2800}
                withReactModal={false}
                withOverlay={false}
                withHandle={true}
                // scrollViewProps={}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
                closeOnOverlayTap={true}
                tapGestureEnabled={false}
                panGestureComponentEnabled={false}
            // closeSnapPointStraightEnabled={false}
            >
                <ScrollView
                    style={{ flex: 1, alignSelf: 'center', paddingBottom: '10%', paddingTop: '4%', }}
                    contentContainerStyle={{ flexGrow: 1 }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.modal}>
                        <View style={{ width: '95%', height: screenHeight / 15, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: .8, borderBottomColor: MediumGrey }}>
                            {request == 2
                                ?
                                <View style={{ width: '60%', height: '90%', flexDirection: 'row', alignItems: 'center', }}>
                                    <Image source={detailsTrip.user_package && detailsTrip.user_package.user && detailsTrip.user_package.user.image ? { uri: detailsTrip.user_package.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '90%', resizeMode: 'contain', width: '20%' }} />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: 20, marginStart: '2%' }}>{detailsTrip.user_package && detailsTrip.user_package.user && detailsTrip.user_package.user.name}</Text>
                                </View>
                                :
                                <View style={{ width: '70%', height: '90%', flexDirection: 'row', alignItems: 'center', }}>
                                    <Image source={detailsTrip.user && detailsTrip.user.image ? { uri: detailsTrip.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '90%', resizeMode: 'contain', width: '20%' }} />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: 20, marginStart: '2%' }}>{detailsTrip.user && detailsTrip.user.name}</Text>
                                </View>
                            }

                            {request == 4 || request == 3 ?
                                <View style={{ width: '20%', height: '90%', alignItems: 'flex-start', justifyContent: 'center' }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: MediumGreen, marginStart: '1%' }}>{detailsTrip.price} {strings('lang.SR')}</Text>
                                </View>
                                :
                                request == 2
                                    ?
                                    <View style={{ width: '40%', height: '90%', alignItems: 'flex-start', justifyContent: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: MediumGreen, marginStart: '1%' }}>{detailsTrip.user_package && detailsTrip.user_package.package && detailsTrip.user_package.package.name} </Text>
                                    </View>
                                    :
                                    <View style={{ width: '20%', height: '90%', alignItems: 'flex-start', justifyContent: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: MediumGreen, marginStart: '1%' }}>{detailsTrip.client_price} {strings('lang.SR')}</Text>
                                    </View>
                            }
                        </View>


                        <View style={[styles.container, { borderBottomWidth: request == 4 ? .8 : 0, borderBottomColor: MediumGrey }]}>
                            <View style={styles.imageContainer}>
                                <View style={styles.image}>
                                    <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                                </View>
                                <View style={{ height: '50%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: Green, zIndex: 0, }}></View>
                                <View style={styles.image}>
                                    <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                                </View>
                            </View>

                            {request == 4
                                ?

                                <View style={styles.addressContainer}>
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.from_city_id ? detailsTrip.from_city.name : detailsTrip.from_address}</Text>
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.to_city_id ? detailsTrip.to_city.name : detailsTrip.to_address}</Text>
                                </View>
                                :
                                request == 2
                                    ?
                                    <View style={styles.addressContainer}>
                                        <Text
                                            style={[styles.blackText,
                                            {
                                                fontSize: screenWidth / 35,
                                                lineHeight: screenHeight / 50,
                                                textAlign: I18nManager.isRTL ? 'left' : 'right'
                                            }]}>
                                            {detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].from_address_id
                                                ? detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].address_from.address
                                                : detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].from_address}</Text>
                                        <Text
                                            style={[styles.blackText,
                                            {
                                                fontSize: screenWidth / 35,
                                                lineHeight: screenHeight / 50,
                                                textAlign: I18nManager.isRTL ? 'left' : 'right'
                                            }]}>
                                            {detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].to_address_id
                                                ? detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].address_to.address
                                                : detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].to_address}</Text>
                                    </View>
                                    :
                                    <View style={styles.addressContainer}>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.from_address_id ? detailsTrip.address_from.address : detailsTrip.from_address}</Text>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.to_address_id ? detailsTrip.address_to.address : detailsTrip.to_address}</Text>
                                    </View>
                            }

                        </View>

                        {detailsTrip.notes &&
                            <>
                                <Text style={[styles.blackText, { fontSize: screenWidth / 35, color: DarkGrey, alignSelf: 'flex-start' }]}>{strings('lang.note')}</Text>
                                <View style={{
                                    borderColor: MediumGrey,
                                    width: '95%',
                                    alignSelf: 'center',
                                    padding: '1%'
                                }}
                                >
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 30, alignSelf: 'flex-start' }]}>{detailsTrip.notes}</Text>
                                </View>

                            </>

                        }


                        {request == 4 || request == 3 ?
                            <>
                                <View
                                    style={{
                                        borderBottomColor: MediumGrey, borderBottomWidth: .8, height: screenHeight / 20,
                                        width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                                    }}>
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{request == 4 ? strings('lang.Tripdate') : strings('lang.timetomoveon')}</Text>
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{request == 4 ? detailsTrip.date : detailsTrip.time}</Text>
                                </View>
                                <View
                                    style={{
                                        borderBottomColor: MediumGrey, borderBottomWidth: .8, height: screenHeight / 20,
                                        width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                                    }}>
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{strings('lang.Numberofindividuals')}</Text>
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{detailsTrip.passengers_count}</Text>
                                </View>
                                {request == 3 &&
                                    <View
                                        style={{
                                            borderBottomColor: MediumGrey, borderBottomWidth: .8, height: screenHeight / 20,
                                            width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                                        }}>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{strings('lang.Thenumberofseatsinthecar')}</Text>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{detailsTrip.seat_count}</Text>
                                    </View>
                                }
                            </>
                            :
                            request == 2
                                ?
                                <>
                                    <View
                                        style={{
                                            borderBottomColor: MediumGrey, borderBottomWidth: .8, height: screenHeight / 20,
                                            width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                                        }}>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{strings('lang.Tripdate')}</Text>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{detailsTrip.user_package && detailsTrip.user_package.start_date_formatted}</Text>
                                    </View>
                                    <View
                                        style={{
                                            borderBottomColor: MediumGrey, borderBottomWidth: .8, height: screenHeight / 20,
                                            width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                                        }}>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{strings('lang.timetomoveon')}</Text>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{detailsTrip.user_package && detailsTrip.user_package.start_time}</Text>
                                    </View>

                                </>
                                :
                                <>

                                 
                                    <View
                                        style={{
                                            width: '100%',
                                            flexDirection: 'row',
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                            alignItems: 'center',
                                            // marginTop: screenHeight / 100,
                                            height: screenHeight / 16,
                                        }}
                                    >
                                        <Button
                                            onPress={() => { sendOffer(detailsTrip.client_price) }}
                                            style={{
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                alignSelf: 'center',
                                                width: '95%',
                                                height: screenHeight / 22,
                                                backgroundColor: DarkBlue,
                                                borderRadius: 20,
                                            }}
                                        >
                                            <Text
                                                style={{
                                                    fontFamily: appFontBold,
                                                    fontSize: screenWidth / 28,
                                                    color: White,
                                                }}
                                            >
                                                {strings('lang.Acceptanceinreturn')} {detailsTrip.client_price} {strings('lang.SR')}
                                            </Text>
                                        </Button>

                                    </View>



                                </>
                        }

                        {request == 1 &&
                            <>
                                <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start', marginVertical: '3%', marginStart: '2.5%' }}>{strings('lang.Showyourquote')}</Text>
                                <View style={{ height: screenHeight / 20, width: '95%', marginBottom: '2%', alignItems: 'center', justifyContent: 'space-between', alignSelf: 'center', flexDirection: 'row' }}>
                                    {chosePrice.map((item, index) => {
                                        return (
                                            <Button
                                                onPress={() => { setChoosePriceId(item.price) }}
                                                style={{ height: '100%', width: screenWidth / 5, backgroundColor: choosePriceId == item.price ? DarkBlue : White, borderWidth: 1, borderColor: MediumGrey, borderRadius: 10, alignItems: 'center', justifyContent: 'center', }}>
                                                <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: choosePriceId == item.price ? White : Black, }}>{item.price} {strings('lang.SR')}</Text>
                                            </Button>
                                        )
                                    })}

                                    <Button
                                        onPress={() => { modalizeRef1.current.open(); modalizeRef.current.close() }}
                                        style={{ height: '100%', backgroundColor: WhiteGery, width: screenWidth / 5, borderRadius: 20, alignItems: 'center', justifyContent: 'center' }}>
                                        <Image source={require('../images/edit.png')} style={{ resizeMode: 'contain', width: '20%', tintColor: Black }} />
                                    </Button>
                                </View>
                            </>

                        }


                        {request == 4 || request == 3 || request == 2 ?
                            <View
                                style={{
                                    width: '100%',
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    alignSelf: 'center',
                                    alignItems: 'center',
                                    marginTop: '10%',
                                    height: screenHeight / 16,
                                }}
                            >
                                <Button
                                    onPress={() => {
                                        if (request == 2) {
                                            updateStatusPackage('accepted')
                                        } else if (request == 3) {
                                            updateStatusVip('accepted')
                                        } else if (request == 4) {
                                            updateStatusCity('accepted')
                                        }

                                    }}
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        alignSelf: 'center',
                                        width: '63%',
                                        height: screenHeight / 22,
                                        backgroundColor: DarkBlue,
                                        borderRadius: 20,
                                    }}
                                >

                                    <Text
                                        style={{
                                            fontFamily: appFontBold,
                                            fontSize: screenWidth / 28,
                                            color: White,
                                        }}
                                    >
                                        {strings('lang.RequestAccept')}
                                    </Text>
                                </Button>
                                <Button
                                    onPress={() => {
                                        if (request == 2) {
                                            updateStatusPackage('rejected')
                                        } else if (request == 3) {
                                            updateStatusVip('rejected')
                                        } else if (request == 4) {
                                            updateStatusCity('rejected')
                                        }
                                    }}
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        alignSelf: 'center',
                                        width: '35%',
                                        height: screenHeight / 22,
                                        backgroundColor: appColor1,
                                        borderRadius: 20,
                                    }}
                                >

                                    <Text
                                        style={{
                                            fontFamily: appFontBold,
                                            fontSize: screenWidth / 28,
                                            color: White,
                                        }}
                                    >
                                        {strings('lang.Refusal')}
                                    </Text>
                                </Button>
                            </View>
                            :
                            choosePriceId
                                ?
                                <View
                                    style={{
                                        width: '100%',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                        alignSelf: 'center',
                                        alignItems: 'center',
                                        // marginTop: screenHeight / 100,
                                        height: screenHeight / 22, marginTop: '3%'
                                    }}
                                >
                                    <Button
                                        onPress={() => { sendOffer(choosePriceId) }}
                                        style={{
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                            width: '95%',
                                            height: screenHeight / 22,
                                            backgroundColor: DarkBlue,
                                            borderRadius: 20,
                                        }}
                                    >

                                        <Text
                                            style={{
                                                fontFamily: appFontBold,
                                                fontSize: screenWidth / 28,
                                                color: White,
                                            }}
                                        >
                                            {strings('lang.Acceptanceinreturn')} {choosePriceId} {strings('lang.SR')}
                                        </Text>
                                    </Button>
                                </View>
                                :

                                <></>
                        }
                    </View>
                </ScrollView>
            </Modalize> */}
            </>

            {/* Price */}
            {ViewID == 1 &&
                <View style={[
                    styles.bottomView,
                    {
                        height: screenHeight / 1.8,
                        backgroundColor: WhiteGery,
                        width: screenWidth,
                        zIndex: 10000000
                    },
                ]}
                >
                    <ScrollView
                        style={{ width: '100%', alignSelf: 'center', paddingVertical: '3%' }}
                        showsVerticalScrollIndicator={false}
                        keyboardShouldPersistTaps={'always'}
                    >
                        <View style={{ width: '95%', alignSelf: 'center', }}>
                            <View style={styles.contentContainer}>

                                <View style={[styles.textInput, { height: screenHeight / 14, borderColor: priceLimit ? Red : MediumGrey, }]}>
                                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', width: '45%' }}>
                                        <TextInput
                                            ref={inputRef}
                                            autoFocus={true}
                                            onChangeText={text => {
                                                if (text) {
                                                    if (minPrice != 0) {
                                                        // if (JSON.parse(text) > maxPrice) {
                                                        //     console.log('Max');
                                                        //     setPrice(text);
                                                        //     setNewPrice(text)
                                                        //     setPriceLimit(true)
                                                        // }
                                                        if (Number(text) < minPrice) {
                                                            setPrice(text);
                                                            setNewPrice(text)
                                                            setPriceLimit(true)
                                                            console.log('Min');
                                                        }
                                                        else {
                                                            setPrice(text);
                                                            setNewPrice(text)
                                                            setPriceLimit(false)
                                                            console.log('Moderate');
                                                        }
                                                    }
                                                    else {
                                                        setPrice(text);
                                                        setNewPrice(text)
                                                        setPriceLimit(false)
                                                    }
                                                }
                                                else {
                                                    setPrice(text);
                                                    setNewPrice(text)
                                                    setPriceLimit(false)
                                                }
                                            }}
                                            value={price}
                                            keyboardType={'number-pad'}
                                            placeholderTextColor={MediumGrey}
                                            placeholder={strings('lang.Showyourquote')}
                                            style={{
                                                alignItems: 'center', justifyContent: 'center', textAlignVertical: 'center',
                                                fontFamily: appFont,
                                                fontSize: screenWidth / 28,
                                                textAlign: I18nManager.isRTL ? 'right' : 'left', color: priceLimit ? Red : Black, width: '60%',
                                            }}
                                        />
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black }} > {'SR'} </Text>
                                    </View>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: priceLimit ? Red : DarkGrey, textAlign: I18nManager.isRTL ? 'right' : 'left' }} > {priceLimit ? `${strings('lang.message16')} ${minPrice} ${strings('lang.SR')}` : strings('lang.Showyourquote')} </Text>
                                </View>
                            </View>

                            <View
                                style={{
                                    width: '100%',
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    alignSelf: 'center',
                                    marginTop: screenHeight / 100,
                                    flexDirection: 'row'
                                }}
                            >
                                <Button
                                    onPress={() => { sendOffer(price) }}
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: '60%',
                                        height: screenHeight / 20,
                                        backgroundColor: DarkBlue,
                                        borderRadius: 20,
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontFamily: appFontBold,
                                            fontSize: screenWidth / 28,
                                            color: White,
                                        }}
                                    >
                                        {strings('lang.Confirm')}
                                    </Text>
                                </Button>
                                <Button
                                    onPress={() => { setViewID(0) }}
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: '30%',
                                        height: screenHeight / 20,
                                        backgroundColor: White,
                                        borderRadius: 20,
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontFamily: appFontBold,
                                            fontSize: screenWidth / 28,
                                            color: Black,
                                        }}
                                    >
                                        {strings('lang.Close')}
                                    </Text>
                                </Button>
                            </View>
                        </View>

                    </ScrollView>
                </View>
            }
            <>
                {/* <Modalize
                ref={modalizeRef1}
                contentRef={contentRef1}
                disableScrollIfPossible={true}
                modalHeight={screenHeight / 1.8}
                // modalTopOffset={screenHeight / 1}
                // alwaysOpen={screenHeight / 2}
                // snapPoint={100}
                handlePosition={'inside'}
                // adjustToContentHeight={true}
                // useNativeDriver={false}
                panGestureEnabled={false}
                keyboardAvoidingBehavior={'padding'}
                // dragToss={0.05}
                // threshold={150}
                // velocity={2800}
                // withReactModal={false}
                withOverlay={false}
                withHandle={true}
                scrollViewProps={screenHeight}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
            // closeOnOverlayTap={true}
            // tapGestureEnabled={false}
            // panGestureComponentEnabled={true}
            // closeSnapPointStraightEnabled={false}
            >
                <ScrollView
                    style={{ width: '100%', alignSelf: 'center', paddingVertical: '3%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={{ width: '95%', alignSelf: 'center', }}>
                        <View style={styles.contentContainer}>

                            <View style={[styles.textInput, { height: screenHeight / 14 }]}>
                                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', width: '45%' }}>
                                    <TextInput
                                        autoFocus={true}
                                        onChangeText={text => setPrice(text)}
                                        value={price}
                                        keyboardType={'number-pad'}
                                        placeholderTextColor={MediumGrey}
                                        placeholder={strings('lang.Showyourquote')}
                                        style={{
                                            alignItems: 'center', justifyContent: 'center', textAlignVertical: 'center',
                                            fontFamily: appFont,
                                            fontSize: screenWidth / 28,
                                            textAlign: I18nManager.isRTL ? 'right' : 'left', color: Black, width: '60%',
                                        }}
                                    />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black }} > {'SR'} </Text>
                                </View>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: DarkGrey, textAlign: 'right' }} > {'عرض تسعيرة معقولة'} </Text>
                            </View>
                        </View>

                        <View
                            style={{
                                width: '100%',
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignSelf: 'center',
                                marginTop: screenHeight / 100,
                                flexDirection: 'row'
                            }}
                        >
                            <Button
                                onPress={() => { sendOffer(price) }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '60%',
                                    height: screenHeight / 20,
                                    backgroundColor: DarkBlue,
                                    borderRadius: 20,
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 28,
                                        color: White,
                                    }}
                                >
                                    {strings('lang.Confirm')}
                                </Text>
                            </Button>
                            <Button
                                onPress={() => { props.navigation.goBack() }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '30%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 28,
                                        color: Black,
                                    }}
                                >
                                    {strings('lang.Close')}
                                </Text>
                            </Button>
                        </View>
                    </View>

                </ScrollView>

            </Modalize> */}
            </>

            <>
                {/* timer offer*/}
                {/* <Modalize
                ref={modalizeRefTaimer}
                contentRef={contentRefTaimer}
                disableScrollIfPossible={true}
                modalHeight={screenHeight / 3}
                // modalTopOffset={screenHeight / 1}
                // alwaysOpen={request == 2 ? screenHeight / 3 : null}
                // snapPoint={100}
                handlePosition={'inside'}
                // adjustToContentHeight={true}
                // useNativeDriver={false}
                panGestureEnabled={false}
                keyboardAvoidingBehavior={'padding'}
                // dragToss={0.05}
                // threshold={150}
                // velocity={2800}
                // withReactModal={false}
                withOverlay={false}
                withHandle={true}
                // scrollViewProps={screenHeight}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
                closeOnOverlayTap={true}
                tapGestureEnabled={false}
                panGestureComponentEnabled={true}
                closeSnapPointStraightEnabled={false}
            >
                <View
                    style={{ width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', height: screenHeight / 3, }}
                >



                    <View style={{
                        height: screenHeight / 10,
                        width: '95%',
                        alignSelf: 'center',
                        backgroundColor: WhiteGery,
                        borderRadius: 10,
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                        <Text style={{ fontSize: screenWidth / 10, fontFamily: appFontBold, color: Black }}>{min}:{sec < 10 && 0}{sec}</Text>
                    </View>




                </View>
            </Modalize> */}
            </>


            {/* timer client*/}
            {ViewID == 3 &&
                <View style={[
                    styles.bottomView,
                    {
                        height: timer ? screenHeight / 2.2 : screenHeight / 2.75,
                        backgroundColor: WhiteGery,
                        width: screenWidth,
                        zIndex: 10000000
                    },
                ]}
                >
                    <ScrollView
                        style={{ width: '100%', alignSelf: 'center', paddingBottom: '3%' }}
                        showsVerticalScrollIndicator={false}
                    >
                        {request == 2
                            ?
                            <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 16, marginTop: '5%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: .8, borderColor: MediumGrey }}>
                                <View style={{ width: screenWidth / 10, height: screenWidth / 10, marginEnd: '2%' }}>
                                    <Image source={detailsTrip.user_package && detailsTrip.user_package.user && detailsTrip.user_package.user.image ? { uri: detailsTrip.user_package.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '100%' }} />
                                </View>

                                <View style={{ width: '36%', height: '90%', alignItems: 'flex-start', alignSelf: 'center', justifyContent: 'center', }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, }}>{detailsTrip.user_package && detailsTrip.user_package.user && detailsTrip.user_package.user.name}</Text>
                                </View>

                                <Button transparent
                                    onPress={() => {
                                        Linking.openURL(`tel:${'+966'}${detailsTrip.user_package && detailsTrip.user_package.user &&
                                            data.user_package.user.phone.startsWith("0") ?
                                            data.user_package.user.phone.slice("0".length)
                                            :
                                            data.user_package.user.phone}`)
                                    }}
                                    style={{ backgroundColor: WhiteGreen, width: '20%', flexDirection: 'row', marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                    <Image source={require('../images/call.png')} style={{ height: '60%', resizeMode: 'contain', width: '10%', marginHorizontal: '2%' }} />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, marginHorizontal: 2 }}>{strings('lang.Call')}</Text>
                                </Button>

                                <Button transparent
                                    onPress={() => {
                                        // Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${detailsTrip.user_package && detailsTrip.user_package.user &&
                                        //     data.user_package.user.phone.startsWith("0") ?
                                        //     data.user_package.user.phone.slice("0".length)
                                        //     :
                                        //     data.user_package.user.phone
                                        //     }`)
                                        setNewMessgae(false)
                                        props.navigation.push('Chat', { receiver_type: 'user', receiver_id: data.user_package.user.id, subject_id: data.user_package.id, subject_type: 'UserPackage', screen: 'MapDriver', chat: true, trip: data.user_package })
                                    }}
                                    style={{ backgroundColor: White, width: '20%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                    {newMessgae
                                        ?
                                        <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: Red, position: 'absolute', top: -2, end: 0 }}></View>
                                        :
                                        <></>
                                    }
                                    <Image source={require('../images/Group98722.png')} style={{ height: '60%', resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, marginHorizontal: 2 }}>{strings('lang.Chatt')}</Text>
                                </Button>
                            </View>
                            :
                            <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 16, marginTop: '5%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: .8, borderColor: MediumGrey }}>
                                <View style={{ width: screenWidth / 10, height: screenWidth / 10, marginEnd: '2%' }}>
                                    <Image source={detailsTrip.user && detailsTrip.user.image ? { uri: detailsTrip.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '100%' }} />
                                </View>

                                <View style={{ width: '36%', height: '90%', alignItems: 'flex-start', alignSelf: 'center', justifyContent: 'center', }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, }}>{detailsTrip.user && detailsTrip.user.name}</Text>
                                </View>

                                <Button transparent
                                    onPress={() => {
                                        Linking.openURL(`tel:${'+966'}${detailsTrip.user &&
                                            detailsTrip.user.phone.startsWith("0") ?
                                            detailsTrip.user.phone.slice("0".length)
                                            :
                                            detailsTrip.user.phone
                                            }`)
                                    }}
                                    style={{ backgroundColor: WhiteGreen, width: '20%', flexDirection: 'row', marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                    <Image source={require('../images/call.png')} style={{ height: '60%', resizeMode: 'contain', width: '10%', marginHorizontal: '2%' }} />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, marginHorizontal: 2 }}>{strings('lang.Call')}</Text>
                                </Button>

                                <Button transparent
                                    onPress={() => {
                                        // Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${detailsTrip.user &&
                                        //     detailsTrip.user.phone.startsWith("0") ?
                                        //     detailsTrip.user.phone.slice("0".length)
                                        //     :
                                        //     detailsTrip.user.phone
                                        //     }`)
                                        setNewMessgae(false)
                                        props.navigation.push('Chat', { receiver_type: 'user', receiver_id: detailsTrip.user.id, subject_id: detailsTrip.id, subject_type: request == 1 ? 'DailyTrip' : request == 3 ? 'VipTrip' : 'CityTrip', screen: 'MapDriver', chat: true, trip: detailsTrip })
                                    }}
                                    style={{ backgroundColor: White, width: '20%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                    {newMessgae
                                        ?
                                        <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: Red, position: 'absolute', top: -2, end: 0 }}></View>
                                        :
                                        <></>
                                    }
                                    <Image source={require('../images/Group98722.png')} style={{ height: '60%', resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, marginHorizontal: 2 }}>{strings('lang.Chatt')}</Text>
                                </Button>
                            </View>
                        }



                        <View style={styles.container}>
                            <View style={styles.imageContainer}>
                                <View style={styles.image}>
                                    <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                                </View>
                                <View style={{ height: '50%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: Green, zIndex: 0, }}></View>
                                <View style={styles.image}>
                                    <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                                </View>
                            </View>
                            {
                                // request == 4
                                //     ?
                                //     <View style={styles.addressContainer}>
                                //         <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.from_city_id ? detailsTrip.from_city.name : ''}</Text>
                                //         <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.to_city_id ? detailsTrip.to_city.name : ''}</Text>
                                //     </View>
                                //     :
                                request == 2
                                    ?
                                    <View style={styles.addressContainer}>
                                        <Text
                                            style={[styles.blackText,
                                            {
                                                fontSize: screenWidth / 35,
                                                lineHeight: screenHeight / 50,
                                                textAlign: I18nManager.isRTL ? 'left' : 'right'
                                            }]}>
                                            {detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].from_address_id
                                                ? detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].address_from.address
                                                : detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].from_address}</Text>
                                        <Text
                                            style={[styles.blackText,
                                            {
                                                fontSize: screenWidth / 35,
                                                lineHeight: screenHeight / 50,
                                                textAlign: I18nManager.isRTL ? 'left' : 'right'
                                            }]}>
                                            {detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].to_address_id
                                                ? detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].address_to.address
                                                : detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].to_address}</Text>
                                    </View>
                                    :
                                    <View style={styles.addressContainer}>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.from_address_id ? detailsTrip.address_from.address : detailsTrip.from_address}</Text>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.to_address_id ? detailsTrip.address_to.address : detailsTrip.to_address}</Text>
                                    </View>
                            }

                        </View>

                        {/* {request == 1 &&
                            <View style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '95%',
                                alignSelf: 'center',
                                // height: screenHeight / 22,
                                // backgroundColor: IsPaid ? DarkGreen : Red,
                                // borderRadius: 15
                            }}>
                                <Text style={{
                                    fontSize: screenWidth / 28,
                                    fontFamily: appFontBold,
                                    color: IsPaid && payment_methodId > 1 ? DarkGreen : Red
                                }}>{IsPaid && payment_methodId > 1 ? `${strings('lang.Trippaymenthasbeenconfirmed')} ` : strings('lang.Paymentforthetriphasnotbeenconfirmed')}</Text>

                            </View>

                        } */}

                        {timer ?
                            <View style={{
                                height: screenHeight / 10,
                                width: '95%',
                                alignSelf: 'center',
                                backgroundColor: minutes >= 2 ? WhiteRed : WhiteGery,
                                borderRadius: 10,
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}>
                                <Text style={{ fontSize: minutes >= 2 ? screenWidth / 12 : screenWidth / 10, fontFamily: appFontBold, color: Black }}>{minutes}:{seconds < 10 && 0}{seconds}</Text>
                                {minutes >= 2 &&
                                    <Text style={{ lineHeight: screenHeight / 50, fontSize: screenWidth / 30, fontFamily: appFontBold, color: Black }}>{'تخطي وقت انتظار العميل'}</Text>
                                }
                            </View>

                            :
                            <></>
                        }

                        <View
                            style={{
                                width: '95%',
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignSelf: 'center',
                                marginVertical: screenHeight / 40,
                                flexDirection: 'row'
                            }}
                        >
                            <Button
                                onPress={() => {
                                    if (props.route.params.request == 1) {
                                        if (timer == true) {
                                            confirmClientArrival()
                                        } else {
                                            confirmArrival()
                                        }
                                    } else if (props.route.params.request == 2) {
                                        if (timer == true) {
                                            confirmClientArrivalPackageTrip()
                                        } else {
                                            confirmArrivalPackage()
                                        }
                                    } else if (props.route.params.request == 3) {
                                        if (timer == true) {
                                            confirmClientArrivalVipTrip()
                                        } else {
                                            confirmArrivalVipTrip()
                                        }
                                    } else if (props.route.params.request == 4) {
                                        if (timer == true) {
                                            confirmClientArrivalCityTrip()
                                        } else {
                                            confirmArrivalCityTrip()
                                        }
                                    }
                                }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: timer == false ? '100%' : '65%',
                                    height: screenHeight / 22,
                                    backgroundColor: DarkBlue,
                                    borderRadius: 20,
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 32,
                                        color: White,
                                    }}
                                >
                                    {timer ? strings('lang.Confirmclientaccess') : strings('lang.Theclienthasbeenreached')}
                                </Text>
                            </Button>
                            {timer == false
                                ?
                                <></>
                                :
                                <Button
                                    disabled={minutes >= 2 ? false : true}
                                    onPress={() => {
                                        refRBSheetReasons.current.open(); setViewID(5)
                                    }}
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: '30%',
                                        height: screenHeight / 22,
                                        backgroundColor: appColor1,
                                        borderRadius: 20,
                                        opacity: minutes >= 2 ? 1 : .5
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontFamily: appFontBold,
                                            fontSize: screenWidth / 32,
                                            color: White,
                                        }}
                                    >
                                        {strings('lang.cancel')}
                                    </Text>
                                </Button>
                            }

                        </View>
                    </ScrollView>
                </View>
            }

            <>
                {/* <Modalize
                ref={modalizeRef3}
                contentRef={contentRef3}
                disableScrollIfPossible={true}
                modalHeight={timer ? screenHeight / 2.2 : screenHeight / 2.75}
                // modalTopOffset={screenHeight / 1}
                // alwaysOpen={request == 2 ? screenHeight / 3 : null}
                // snapPoint={100}
                handlePosition={'inside'}
                // adjustToContentHeight={true}
                // useNativeDriver={false}
                panGestureEnabled={false}
                keyboardAvoidingBehavior={'padding'}
                // dragToss={0.05}
                // threshold={150}
                // velocity={2800}
                // withReactModal={false}
                withOverlay={false}
                withHandle={true}
                // scrollViewProps={screenHeight}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
                closeOnOverlayTap={true}
                tapGestureEnabled={false}
                panGestureComponentEnabled={true}
                closeSnapPointStraightEnabled={false}
            >
                <ScrollView
                    style={{ width: '100%', alignSelf: 'center', paddingBottom: '3%' }}
                    showsVerticalScrollIndicator={false}
                >
                    {request == 2
                        ?
                        <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 16, marginTop: '5%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: .8, borderColor: MediumGrey }}>
                            <View style={{ width: '15%', height: '90%' }}>
                                <Image source={detailsTrip.user_package && detailsTrip.user_package.user && detailsTrip.user_package.user.image ? { uri: detailsTrip.user_package.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '90%', resizeMode: 'contain', width: '100%' }} />
                            </View>

                            <View style={{ width: '36%', height: '90%', alignItems: 'flex-start', alignSelf: 'center', justifyContent: 'center', }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, }}>{detailsTrip.user_package && detailsTrip.user_package.user && detailsTrip.user_package.user.name}</Text>
                            </View>

                            <Button transparent
                                onPress={() => { Linking.openURL(`tel:${'+966'}${detailsTrip.user_package && detailsTrip.user_package.user && data.user_package.user.phone}`) }}
                                style={{ backgroundColor: WhiteGreen, width: '20%', flexDirection: 'row', marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                <Image source={require('../images/call.png')} style={{ height: '60%', resizeMode: 'contain', width: '15%', marginTop: screenHeight / 200 }} />
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: 2 }}>{strings('lang.Call')}</Text>
                            </Button>

                            <Button transparent
                                onPress={() => { Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${detailsTrip.user_package && detailsTrip.user_package.user && data.user_package.user.phone}`) }}
                                style={{ backgroundColor: White, width: '20%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                <Image source={require('../images/whatssapp.png')} style={{ height: '60%', resizeMode: 'contain', width: '15%', marginTop: screenHeight / 200 }} />
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: 2 }}>{strings('lang.Chatt')}</Text>
                            </Button>
                        </View>
                        :
                        <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 16, marginTop: '5%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: .8, borderColor: MediumGrey }}>
                            <View style={{ width: '15%', height: '90%' }}>
                                <Image source={detailsTrip.user && detailsTrip.user.image ? { uri: detailsTrip.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '90%', resizeMode: 'contain', width: '100%' }} />
                            </View>

                            <View style={{ width: '36%', height: '90%', alignItems: 'flex-start', alignSelf: 'center', justifyContent: 'center', }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, }}>{detailsTrip.user && detailsTrip.user.name}</Text>
                            </View>

                            <Button transparent
                                onPress={() => { Linking.openURL(`tel:${'+966'}${detailsTrip.user && detailsTrip.user.phone}`) }}
                                style={{ backgroundColor: WhiteGreen, width: '20%', flexDirection: 'row', marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                <Image source={require('../images/call.png')} style={{ height: '60%', resizeMode: 'contain', width: '15%', marginTop: screenHeight / 200 }} />
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: 2 }}>{strings('lang.Call')}</Text>
                            </Button>

                            <Button transparent
                                onPress={() => { Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${detailsTrip.user && detailsTrip.user.phone}`) }}
                                style={{ backgroundColor: White, width: '20%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                <Image source={require('../images/whatssapp.png')} style={{ height: '60%', resizeMode: 'contain', width: '15%', marginTop: screenHeight / 200 }} />
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: 2 }}>{strings('lang.Chatt')}</Text>
                            </Button>
                        </View>
                    }



                    <View style={styles.container}>
                        <View style={styles.imageContainer}>
                            <View style={styles.image}>
                                <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                            </View>
                            <View style={{ height: '50%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: Green, zIndex: 0, }}></View>
                            <View style={styles.image}>
                                <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                            </View>
                        </View>
                        {request == 4
                            ?
                            <View style={styles.addressContainer}>
                                <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.from_city_id ? detailsTrip.from_city.name : ''}</Text>
                                <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.to_city_id ? detailsTrip.to_city.name : ''}</Text>
                            </View>
                            :
                            request == 2
                                ?
                                <View style={styles.addressContainer}>
                                    <Text
                                        style={[styles.blackText,
                                        {
                                            fontSize: screenWidth / 35,
                                            lineHeight: screenHeight / 50,
                                            textAlign: I18nManager.isRTL ? 'left' : 'right'
                                        }]}>
                                        {detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].from_address_id
                                            ? detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].address_from.address
                                            : detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].from_address}</Text>
                                    <Text
                                        style={[styles.blackText,
                                        {
                                            fontSize: screenWidth / 35,
                                            lineHeight: screenHeight / 50,
                                            textAlign: I18nManager.isRTL ? 'left' : 'right'
                                        }]}>
                                        {detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].to_address_id
                                            ? detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].address_to.address
                                            : detailsTrip.user_package && detailsTrip.user_package.destinations && detailsTrip.user_package.destinations[0] && detailsTrip.user_package.destinations[0].to_address}</Text>
                                </View>
                                :
                                <View style={styles.addressContainer}>
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.from_address_id ? detailsTrip.address_from.address : detailsTrip.from_address}</Text>
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.to_address_id ? detailsTrip.address_to.address : detailsTrip.to_address}</Text>
                                </View>
                        }

                    </View>

                    {request == 1 &&
                        <View style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '95%',
                            alignSelf: 'center',
                            // height: screenHeight / 22,
                            // backgroundColor: IsPaid ? DarkGreen : Red,
                            // borderRadius: 15
                        }}>
                            <Text style={{
                                fontSize: screenWidth / 28,
                                fontFamily: appFontBold,
                                color: IsPaid ? DarkGreen : Red
                            }}>{IsPaid ? `${strings('lang.Trippaymenthasbeenconfirmed')} ${detailsTrip.payment_method && detailsTrip.payment_method.name}` : strings('lang.Paymentforthetriphasnotbeenconfirmed')}</Text>

                        </View>

                    }

                    {timer ?
                        <View style={{
                            height: screenHeight / 10,
                            width: '95%',
                            alignSelf: 'center',
                            backgroundColor: minutes >= 2 ? WhiteRed : WhiteGery,
                            borderRadius: 10,
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            <Text style={{ fontSize: minutes >= 2 ? screenWidth / 12 : screenWidth / 10, fontFamily: appFontBold, color: Black }}>{minutes}:{seconds < 10 && 0}{seconds}</Text>
                            {minutes >= 2 &&
                                <Text style={{ lineHeight: screenHeight / 50, fontSize: screenWidth / 30, fontFamily: appFontBold, color: Black }}>{'تخطي وقت انتظار العميل'}</Text>
                            }
                        </View>

                        :
                        <></>
                    }

                    <View
                        style={{
                            width: '95%',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignSelf: 'center',
                            marginVertical: screenHeight / 40,
                            flexDirection: 'row'
                        }}
                    >
                        <Button
                            onPress={() => {
                                if (props.route.params.request == 1) {
                                    if (timer == true) {
                                        confirmClientArrival()
                                    } else {
                                        confirmArrival()
                                    }
                                } else if (props.route.params.request == 2) {
                                    if (timer == true) {
                                        confirmClientArrivalPackageTrip()
                                    } else {
                                        confirmArrivalPackage()
                                    }
                                } else if (props.route.params.request == 3) {
                                    if (timer == true) {
                                        confirmClientArrivalVipTrip()
                                    } else {
                                        confirmArrivalVipTrip()
                                    }
                                } else if (props.route.params.request == 4) {
                                    if (timer == true) {
                                        confirmClientArrivalCityTrip()
                                    } else {
                                        confirmArrivalCityTrip()
                                    }
                                }
                            }}
                            style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: timer == false ? '100%' : '65%',
                                height: screenHeight / 22,
                                backgroundColor: DarkBlue,
                                borderRadius: 20,
                            }}
                        >
                            <Text
                                style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 28,
                                    color: White,
                                }}
                            >
                                {timer ? strings('lang.Confirmclientaccess') : strings('lang.Theclienthasbeenreached')}
                            </Text>
                        </Button>
                        {timer == false
                            ?
                            <></>
                            :
                            <Button
                                disabled={minutes >= 2 ? false : true}
                                onPress={() => {
                                    refRBSheetReasons.current.open(); modalizeRef3.current.close()
                                }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '30%',
                                    height: screenHeight / 22,
                                    backgroundColor: appColor1,
                                    borderRadius: 20,
                                    opacity: minutes >= 2 ? 1 : .5
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 28,
                                        color: White,
                                    }}
                                >
                                    {strings('lang.cancel')}
                                </Text>
                            </Button>
                        }

                    </View>
                </ScrollView>
            </Modalize> */}
            </>

            <>
                {/* Vip trip */}
                {/* <Modalize
                ref={modalizeRef2}
                contentRef={contentRef2}
                disableScrollIfPossible={true}
                modalHeight={screenHeight / 2.3}
                // modalTopOffset={screenHeight / 1}
                // alwaysOpen={request == 3 ? screenHeight / 2.3 : 0}
                // snapPoint={100}
                handlePosition={'inside'}
                // adjustToContentHeight={true}
                // useNativeDriver={false}
                panGestureEnabled={false}
                keyboardAvoidingBehavior={'padding'}
                // dragToss={0.05}
                // threshold={150}
                // velocity={2800}
                // withReactModal={false}
                withOverlay={false}
                withHandle={true}
                // scrollViewProps={screenHeight}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
                closeOnOverlayTap={true}
                tapGestureEnabled={false}
                panGestureComponentEnabled={true}
                closeSnapPointStraightEnabled={false}
            >
                <ScrollView
                    style={{ width: '100%', alignSelf: 'center', paddingVertical: '3%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={{ width: '95%', height: screenHeight / 16, paddingHorizontal: '2.5%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', borderBottomColor: MediumGrey, borderBottomWidth: .8 }}>
                        <View style={{ flexDirection: 'row', width: '60%', alignItems: 'center', justifyContent: 'flex-start', height: '90%' }}>
                            <View style={{ width: '20%', height: '100%', }}>
                                <Image source={detailsTripVip.user && detailsTripVip.user.image ? { uri: detailsTripVip.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '100%' }} />
                            </View>

                            <View style={{ height: '100%', alignItems: 'center', alignSelf: 'center', justifyContent: 'center', }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '3%', }}>{detailsTripVip.user && detailsTripVip.user.name} </Text>
                            </View>
                        </View>


                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 18, color: Green2 }}>{detailsTripVip && detailsTripVip.price} {strings('lang.SR')}</Text>
                    </View>

                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '2.5%', alignSelf: 'flex-start', marginVertical: '2%' }}>{strings('lang.destination')} </Text>
                    <View style={[styles.container, { borderBottomColor: MediumGrey, borderBottomWidth: .8, marginTop: 0 }]}>
                        <View style={styles.imageContainer}>
                            <View style={styles.image}>
                                <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                            </View>
                            <View style={{ height: '50%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: Green, zIndex: 0, }}></View>
                            <View style={styles.image}>
                                <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                            </View>
                        </View>

                        <View style={styles.addressContainer}>
                            <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTripVip.from_address_id ? detailsTripVip.address_from.address : detailsTripVip.from_address}</Text>
                            <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTripVip.to_address_id ? detailsTripVip.address_to.address : detailsTripVip.to_address}</Text>

                        </View>
                    </View>

                    <View
                        style={{
                            borderBottomColor: MediumGrey, borderBottomWidth: .8, height: screenHeight / 20,
                            width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                        }}>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{strings('lang.timetomoveon')}</Text>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{detailsTripVip && detailsTripVip.time}</Text>
                    </View>

                    <View
                        style={{
                            height: screenHeight / 20,
                            width: '95%', alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'
                        }}>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, }]}>{strings('lang.Numberofindividuals')}</Text>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '5%', opacity: .7 }]}>{detailsTripVip && detailsTripVip.passengers_count}</Text>
                    </View>


                    <View
                        style={{
                            width: '95%',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignSelf: 'center',
                            marginVertical: '3%',
                            flexDirection: 'row'
                        }}
                    >
                        <Button
                            onPress={() => { confirmArrivalVipTrip() }}
                            style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '100%',
                                height: screenHeight / 22,
                                backgroundColor: DarkBlue,
                                borderRadius: 20,
                            }}
                        >
                            <Text
                                style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 28,
                                    color: White,
                                }}
                            >
                                {strings('lang.Confirm')}
                            </Text>
                        </Button>

                    </View>
                </ScrollView>

            </Modalize> */}
            </>


            {/* finish */}

            {ViewID == 4 &&
                <View style={[
                    styles.bottomView,
                    {
                        height: screenHeight / 2.75,
                        backgroundColor: WhiteGery,
                        width: screenWidth,
                        zIndex: 10000000
                    },
                ]}
                >
                    <ScrollView
                        style={{ width: '100%', alignSelf: 'center', paddingBottom: '3%' }}
                        showsVerticalScrollIndicator={false}
                    >
                        <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 16, marginTop: '5%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: .8, borderColor: MediumGrey }}>
                            <View style={{ width: screenWidth / 10, height: screenWidth / 10, marginEnd: '2%' }}>
                                <Image source={data.user && data.user.image ? { uri: data.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '100%' }} />
                            </View>

                            <View style={{ width: '36%', height: '90%', alignItems: 'flex-start', alignSelf: 'center', justifyContent: 'center', }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, }}>{data.user && data.user.name}</Text>
                            </View>

                            <Button transparent
                                onPress={() => {
                                    Linking.openURL(`tel:${'+966'}${data.user &&
                                        data.user.phone.startsWith("0") ?
                                        data.user.phone.slice("0".length)
                                        :
                                        data.user.phone
                                        }`)
                                }}
                                style={{ backgroundColor: WhiteGreen, width: '20%', flexDirection: 'row', marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                <Image source={require('../images/call.png')} style={{ height: '60%', resizeMode: 'contain', width: '12%', marginHorizontal: '2%' }} />
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, marginHorizontal: 2 }}>{strings('lang.Call')}</Text>
                            </Button>

                            <Button transparent
                                onPress={() => {
                                    // Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${data.user &&
                                    //     data.user.phone.startsWith("0") ?
                                    //     data.user.phone.slice("0".length)
                                    //     :
                                    //     data.user.phone
                                    //     }`)
                                    setNewMessgae(false)
                                    props.navigation.push('Chat', { receiver_type: 'user', receiver_id: data.user.id, subject_id: data.id, subject_type: request == 1 ? 'DailyTrip' : request == 2 ? 'UserPackage' : request == 3 ? 'VipTrip' : 'CityTrip', screen: 'MapDriver', chat: true, trip: data })
                                }}
                                style={{ backgroundColor: White, width: '20%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                {newMessgae
                                    ?
                                    <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: Red, position: 'absolute', top: -2, end: 0 }}></View>
                                    :
                                    <></>
                                }
                                <Image source={require('../images/Group98722.png')} style={{ height: '60%', resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, marginHorizontal: 2 }}>{strings('lang.Chatt')}</Text>
                            </Button>
                        </View>


                        <View style={styles.container}>
                            <View style={styles.imageContainer}>
                                <View style={styles.image}>
                                    <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                                </View>
                                <View style={{ height: '50%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: Green, zIndex: 0, }}></View>
                                <View style={styles.image}>
                                    <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                                </View>
                            </View>
                            {
                                // request == 4
                                //     ?
                                //     <View style={styles.addressContainer}>
                                //         <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.from_city_id ? detailsTrip.from_city.name : ''}</Text>
                                //         {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, marginBottom: '4%', color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}
                                //         <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.to_city_id ? detailsTrip.to_city.name : ''}</Text>
                                //         {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}

                                //     </View>
                                //     :
                                request == 2
                                    ?
                                    <View style={styles.addressContainer}>
                                        <Text
                                            style={[styles.blackText,
                                            {
                                                fontSize: screenWidth / 35,
                                                lineHeight: screenHeight / 50,
                                                textAlign: I18nManager.isRTL ? 'left' : 'right'
                                            }]}>
                                            {data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].from_address_id
                                                ? data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].address_from.address
                                                : data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].from_address}</Text>
                                        {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, marginBottom: '4%', color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}
                                        <Text
                                            style={[styles.blackText,
                                            {
                                                fontSize: screenWidth / 35,
                                                lineHeight: screenHeight / 50,
                                                textAlign: I18nManager.isRTL ? 'left' : 'right'
                                            }]}>
                                            {data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].to_address_id
                                                ? data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].address_to.address
                                                : data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].to_address}</Text>
                                        {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}
                                    </View>
                                    :
                                    <View style={styles.addressContainer}>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{data.from_address_id ? data.address_from.address : data.from_address}</Text>
                                        {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, marginBottom: '4%', color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{data.to_address_id ? data.address_to.address : data.to_address}</Text>
                                        {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, color: MediumGrey, lineHeight: screenHeight / 50 }]}>{'هذا النص هو مثال لنص يمكن أن يستبدل في نفس'}</Text> */}

                                    </View>
                            }


                        </View>
                        {request == 1 &&
                            <View style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '95%',
                                alignSelf: 'center',
                                // height: screenHeight / 22,
                                // backgroundColor: IsPaid ? DarkGreen : Red,
                                // borderRadius: 15
                            }}>
                                <Text style={{
                                    fontSize: screenWidth / 28,
                                    fontFamily: appFontBold,
                                    color: IsPaid && payment_methodId > 1 ? DarkGreen : Red
                                }}>{IsPaid && payment_methodId > 1 ? `${strings('lang.Trippaymenthasbeenconfirmed')} ` : strings('lang.Paymentforthetriphasnotbeenconfirmed')}</Text>

                            </View>

                        }


                        <View
                            style={{
                                width: '95%',
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignSelf: 'center',
                                marginVertical: screenHeight / 40,
                                flexDirection: 'row'
                            }}
                        >
                            <Button
                                onPress={() => {
                                    if (request == 1) {
                                        if (IsPaid == false || payment_methodId == 1) {
                                            refRBSheetIsPaid.current.open();
                                        }
                                        else {
                                            finishTrip()
                                        }
                                    } else if (request == 2) {
                                        finishTripPackages()
                                    }
                                    else if (request == 3) {
                                        // if (IsPaid == false || payment_methodId == 1) {
                                        //     refRBSheetIsPaid.current.open();
                                        // }
                                        // else {
                                        finishVipTrip()
                                        // }
                                    }
                                    else if (request == 4) {
                                        // if (IsPaid == false || payment_methodId == 1) {
                                        //     refRBSheetIsPaid.current.open();
                                        // }
                                        // else {
                                        finishCityTrip()
                                        // }
                                    }
                                }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '100%',
                                    height: screenHeight / 22,
                                    backgroundColor: DarkBlue,
                                    borderRadius: 20,
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 32,
                                        color: White,
                                    }}
                                >
                                    {strings('lang.Endthetrip')}
                                </Text>
                            </Button>

                        </View>
                    </ScrollView>
                </View>
            }

            <>
                {/* <Modalize
                ref={modalizeRef4}
                contentRef={contentRef4}
                disableScrollIfPossible={true}
                modalHeight={screenHeight / 2.75}
                // modalTopOffset={screenHeight / 1}
                // alwaysOpen={ screenHeight / 3 }
                // snapPoint={100}
                handlePosition={'inside'}
                // adjustToContentHeight={true}
                // useNativeDriver={false}
                panGestureEnabled={false}
                keyboardAvoidingBehavior={'padding'}
                // dragToss={0.05}
                // threshold={150}
                // velocity={2800}
                // withReactModal={false}
                withOverlay={false}
                withHandle={true}
                // scrollViewProps={screenHeight}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
                closeOnOverlayTap={true}
                tapGestureEnabled={false}
                panGestureComponentEnabled={true}
                closeSnapPointStraightEnabled={false}
            >
                <ScrollView
                    style={{ width: '100%', alignSelf: 'center', paddingBottom: '3%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 16, marginTop: '5%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: .8, borderColor: MediumGrey }}>
                        <View style={{ width: '15%', height: '90%' }}>
                            <Image source={data.user && data.user.image ? { uri: data.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '90%', resizeMode: 'contain', width: '100%' }} />
                        </View>

                        <View style={{ width: '36%', height: '90%', alignItems: 'flex-start', alignSelf: 'center', justifyContent: 'center', }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, }}>{data.user && data.user.name}</Text>
                        </View>

                        <Button transparent
                            onPress={() => { Linking.openURL(`tel:${'+966'}${data.user && data.user.phone}`) }}
                            style={{ backgroundColor: WhiteGreen, width: '20%', flexDirection: 'row', marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                            <Image source={require('../images/call.png')} style={{ height: '60%', resizeMode: 'contain', width: '15%', marginTop: screenHeight / 200 }} />
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: 2 }}>{strings('lang.Call')}</Text>
                        </Button>

                        <Button transparent
                            onPress={() => { Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${data.user && data.user.phone}`) }}
                            style={{ backgroundColor: White, width: '20%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, marginHorizontal: '1%', height: screenHeight / 25, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                            <Image source={require('../images/whatssapp.png')} style={{ height: '60%', resizeMode: 'contain', width: '15%', marginTop: screenHeight / 200 }} />
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: 2 }}>{strings('lang.Chatt')}</Text>
                        </Button>
                    </View>


                    <View style={styles.container}>
                        <View style={styles.imageContainer}>
                            <View style={styles.image}>
                                <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                            </View>
                            <View style={{ height: '50%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: Green, zIndex: 0, }}></View>
                            <View style={styles.image}>
                                <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                            </View>
                        </View>
                        {request == 4
                            ?
                            <View style={styles.addressContainer}>
                                <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.from_city_id ? detailsTrip.from_city.name : ''}</Text>
                                <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{detailsTrip.to_city_id ? detailsTrip.to_city.name : ''}</Text>

                            </View>
                            :
                            request == 2
                                ?
                                <View style={styles.addressContainer}>
                                    <Text
                                        style={[styles.blackText,
                                        {
                                            fontSize: screenWidth / 35,
                                            lineHeight: screenHeight / 50,
                                            textAlign: I18nManager.isRTL ? 'left' : 'right'
                                        }]}>
                                        {data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].from_address_id
                                            ? data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].address_from.address
                                            : data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].from_address}</Text>
                                    <Text
                                        style={[styles.blackText,
                                        {
                                            fontSize: screenWidth / 35,
                                            lineHeight: screenHeight / 50,
                                            textAlign: I18nManager.isRTL ? 'left' : 'right'
                                        }]}>
                                        {data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].to_address_id
                                            ? data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].address_to.address
                                            : data.user_package && data.user_package.destinations && data.user_package.destinations[0] && data.user_package.destinations[0].to_address}</Text>
                                </View>
                                :
                                <View style={styles.addressContainer}>
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{data.from_address_id ? data.address_from.address : data.from_address}</Text>
                                    <Text style={[styles.blackText, { fontSize: screenWidth / 35, lineHeight: screenHeight / 50, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{data.to_address_id ? data.address_to.address : data.to_address}</Text>

                                </View>
                        }


                    </View>
                    {request == 1 &&
                        <View style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '95%',
                            alignSelf: 'center',
                            // height: screenHeight / 22,
                            // backgroundColor: IsPaid ? DarkGreen : Red,
                            // borderRadius: 15
                        }}>
                            <Text style={{
                                fontSize: screenWidth / 28,
                                fontFamily: appFontBold,
                                color: IsPaid ? DarkGreen : Red
                            }}>{IsPaid ? `${strings('lang.Trippaymenthasbeenconfirmed')} ${data.payment_method && data.payment_method.name}` : strings('lang.Paymentforthetriphasnotbeenconfirmed')}</Text>

                        </View>

                    }


                    <View
                        style={{
                            width: '95%',
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignSelf: 'center',
                            marginVertical: screenHeight / 40,
                            flexDirection: 'row'
                        }}
                    >
                        <Button
                            onPress={() => {
                                if (request == 1) {
                                    if (IsPaid == false || payment_methodId == 1) {
                                        refRBSheetIsPaid.current.open();
                                    } else {
                                        finishTrip()
                                    }
                                } else if (request == 2) {
                                    finishTripPackages()
                                }
                                else if (request == 3) {
                                    finishVipTrip()
                                }
                                else if (request == 4) {
                                    finishCityTrip()
                                }
                            }}
                            style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '100%',
                                height: screenHeight / 22,
                                backgroundColor: DarkBlue,
                                borderRadius: 20,
                            }}
                        >
                            <Text
                                style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 28,
                                    color: White,
                                }}
                            >
                                {strings('lang.Endthetrip')}
                            </Text>
                        </Button>

                    </View>
                </ScrollView>
            </Modalize> */}
            </>

            {/* rating  */}
            <Modalize
                ref={modalizeRefRat}
                contentRef={contentRefRat}
                disableScrollIfPossible={true}
                // modalHeight={screenHeight / 2.2}
                // modalTopOffset={screenHeight / 1}
                // alwaysOpen={screenHeight / 2.2}
                // snapPoint={100}
                handlePosition={'inside'}
                adjustToContentHeight={true}
                useNativeDriver={false}
                panGestureEnabled={false}
                keyboardAvoidingBehavior={'padding'}
                // dragToss={0.05}
                // threshold={150}
                // velocity={2800}
                withReactModal={false}
                withOverlay={false}
                withHandle={true}
                // scrollViewProps={screenHeight}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
                closeOnOverlayTap={true}
                tapGestureEnabled={false}
                panGestureComponentEnabled={true}
                closeSnapPointStraightEnabled={false}
            >
                <ScrollView
                    style={{ width: '100%', alignSelf: 'center', paddingBottom: '3%' }}
                    showsVerticalScrollIndicator={false}
                >

                    <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 16, marginTop: '5%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: .8, borderColor: MediumGrey }}>
                        <View style={{ width: '15%', height: '90%' }}>
                            <Image source={data.user && data.user.image ? { uri: data.user.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '90%', resizeMode: 'contain', width: '100%' }} />
                        </View>

                        <View style={{ width: '85%', height: '90%', alignItems: 'flex-start', alignSelf: 'center', justifyContent: 'center', }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, }}>{data.user && data.user.name}</Text>
                        </View>
                    </View>

                    <Text style={{ fontFamily: appFont, fontSize: screenWidth / 25, color: Black, alignSelf: 'center' }}>{strings('lang.Giveyourratingtothecustomer')}</Text>
                    <View style={{ width: '95%', alignItems: 'center', justifyContent: 'center' }}>
                        <StarRating
                            disabled={false}
                            maxStars={5}
                            starSize={screenHeight / 25}
                            starStyle={{ marginEnd: 2, }}
                            rating={starCount}
                            containerStyle={{ alignContent: 'flex-start', }}
                            buttonStyle={{ flexDirection: 'row', left: 'auto' }}
                            // emptyStar={'ios-star-outline'}
                            // fullStar={'ios-star'}
                            // iconSet={'Ionicons'}
                            emptyStarColor={DarkYellow}
                            fullStarColor={DarkYellow}
                            selectedStar={(rating) => setStarCount(rating)}
                        />
                    </View>

                    <View style={styles.input1}>
                        <Textarea
                            onChangeText={text => setReview(text)}
                            value={review}
                            placeholder={strings('lang.Enternotesaboutthecustomer')}
                            placeholderTextColor={MediumGrey}
                            style={{
                                width: '100%',
                                alignSelf: 'center',
                                // borderRadius: 15,
                                paddingHorizontal: '5%',
                                borderColor: MediumGrey,
                                color: review ? Black : DarkGrey,
                                // borderWidth: 1,
                                fontFamily: appFontBold,
                                height: '100%',
                                textAlignVertical: 'top',
                                fontSize: screenWidth / 30,
                                textAlign: I18nManager.isRTL ? 'right' : 'left',
                            }}
                        />
                    </View>
                    <View
                        style={{
                            width: '95%',
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignSelf: 'center',
                            marginVertical: screenHeight / 40,
                            flexDirection: 'row'
                        }}
                    >
                        <Button
                            transparent
                            disabled={starCount == 0 ? true : false}
                            onPress={() => {
                                if (request == 1) {
                                    leaveReview()
                                } else if (request == 2) {
                                    leaveReviewPackages()
                                } else if (request == 3) {
                                    leaveReviewVip()
                                } else if (request == 4) {
                                    leaveReviewCity()
                                }
                            }}
                            style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '100%',
                                height: screenHeight / 22,
                                backgroundColor: DarkBlue,
                                borderRadius: 20,
                                opacity: starCount == 0 ? .5 : 1
                            }}
                        >
                            <Text
                                style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 28,
                                    color: White,
                                }}
                            >
                                {strings('lang.send')}
                            </Text>
                        </Button>

                    </View>
                </ScrollView>
            </Modalize>

            {/* isPaid rbsheet */}
            <RBSheet
                ref={refRBSheetIsPaid}
                height={data.discount ? screenHeight / 2.4 : screenHeight / 3}
                openDuration={280}
                customStyles={{
                    container: {
                        // justifyContent: "center",
                        alignItems: "center",
                        width: '100%',

                        alignSelf: 'center',
                        borderRadius: 15

                    },
                    wrapper: {

                    },
                    draggableIcon: {

                    }
                }}
            // onClose={() => { props.navigation.navigate('Home') }}
            // closeOnDragDown={true}
            >
                <View style={{ width: '90%', alignSelf: 'center', }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            width: '70%',
                            height: screenHeight / 20,
                            alignSelf: 'flex-end',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                        }}
                    >
                        <Text style={styles.labelModal}>
                            {strings('lang.Endthetrip')}
                        </Text>
                        <Button
                            onPress={() => {
                                refRBSheetIsPaid.current.close();
                                // modalizeRef4.current.open()
                            }}
                            style={{
                                width: screenWidth / 20,
                                height: screenWidth / 20,
                                alignItems: 'center',
                                alignSelf: 'center',
                                justifyContent: 'center',
                                backgroundColor: White,
                                elevation: 0,
                            }}
                        >
                            <Image source={require('../images/xx.png')} style={{ resizeMode: 'contain', width: '100%', height: '100%' }} />
                        </Button>
                    </View>

                    {IsPaid == true && payment_methodId > 1 ?
                        <Text style={{ ...styles.labelModal, ...{ alignSelf: 'center', marginStart: '0%', fontSize: screenWidth / 28, marginTop: '10%', color: DarkGreen } }}>
                            {strings('lang.Paid')}
                        </Text>
                        :
                        <>
                            <Text style={{ ...styles.labelModal, ...{ alignSelf: 'center', marginStart: '0%', fontSize: screenWidth / 20, marginTop: '10%', color: Red } }}>
                                {strings('lang.message17')}
                            </Text>
                            <Text style={{ ...styles.labelModal, ...{ alignSelf: 'center', marginStart: '0%', fontSize: screenWidth / 28, color: Red } }}>
                                {strings('lang.Doyouwanttoendthetrip')}
                            </Text>
                        </>

                    }
                    {data.discount
                        ?
                        <>
                            <View style={{ width: '90%', alignSelf: 'center', flexDirection: 'row', height: screenHeight / 28, alignItems: 'center', justifyContent: 'space-between', }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: DarkGrey, marginStart: '1%', alignSelf: 'center' }}>{strings('lang.Tripprice')}</Text>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: DarkGrey, marginStart: '1%', alignSelf: 'center' }}>{data.price} {strings('lang.SR')}</Text>
                            </View>
                            <View style={{ width: '90%', alignSelf: 'center', flexDirection: 'row', height: screenHeight / 28, alignItems: 'center', justifyContent: 'space-between', }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: DarkGrey, marginStart: '1%', alignSelf: 'center' }}>{strings('lang.Discountvalue')}</Text>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: DarkGrey, marginStart: '1%', alignSelf: 'center' }}>{data.discount} {strings('lang.SR')}</Text>
                            </View>
                            <View style={{ width: '90%', alignSelf: 'center', flexDirection: 'row', height: screenHeight / 28, alignItems: 'center', justifyContent: 'space-between', }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: MediumGreen, marginStart: '1%', alignSelf: 'center' }}>{strings('lang.Priceafterdiscount')}</Text>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: MediumGreen, marginStart: '1%', alignSelf: 'center' }}>{data.final_price} {strings('lang.SR')}</Text>
                            </View>
                            <Text style={{ ...styles.labelModal, ...{ alignSelf: 'center', marginStart: '0%', fontSize: screenWidth / 28, color: Red } }}>
                                {`${strings('lang.willbeadded')} ${data.discount} ${strings('lang.SR')} ${strings('lang.Inthewallet')}`}
                            </Text>
                        </>
                        :
                        <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 16, alignItems: 'center', justifyContent: 'space-between', }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 15, color: MediumGreen, marginStart: '1%', alignSelf: 'center' }}>{data.price} {strings('lang.SR')}</Text>
                        </View>

                    }

                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: '4%' }}>
                        <Button
                            style={{
                                width: '47%',
                                alignSelf: 'center',
                                height: screenHeight / 18,
                                backgroundColor: DarkBlue,
                                alignItems: 'center',
                                flexDirection: 'row',
                                justifyContent: 'center',
                                borderRadius: 10,
                                flexDirection: 'row',
                            }}
                            onPress={() => {
                                if (request == 3) {
                                    finishVipTrip()
                                }
                                else if (request == 4) {
                                    finishCityTrip()
                                }
                                else {
                                    finishTrip()
                                }

                            }}
                        >
                            <Text
                                style={{
                                    fontSize: screenWidth / 28,
                                    fontFamily: appFontBold,
                                    color: White,
                                }}
                            >
                                {strings('lang.Yes')}
                            </Text>
                        </Button>
                        <Button
                            style={{
                                width: '47%',
                                alignSelf: 'center',
                                height: screenHeight / 18,
                                backgroundColor: Red,
                                alignItems: 'center',
                                flexDirection: 'row',
                                justifyContent: 'center',
                                borderRadius: 10,
                                flexDirection: 'row',
                            }}
                            onPress={() => {
                                refRBSheetIsPaid.current.close();
                            }}
                        >
                            <Text
                                style={{
                                    fontSize: screenWidth / 28,
                                    fontFamily: appFontBold,
                                    color: White,
                                }}
                            >
                                {strings('lang.No')}
                            </Text>
                        </Button>
                    </View>


                </View>

            </RBSheet>

            {/* report rbsheet */}
            <RBSheet
                ref={refRBSheet}
                height={screenHeight / 1.5}
                openDuration={280}
                customStyles={{
                    container: {
                        // justifyContent: "center",
                        alignItems: "center",
                        width: '100%',

                        alignSelf: 'center',
                        borderRadius: 15

                    },
                    wrapper: {

                    },
                    draggableIcon: {

                    }
                }}
            // onClose={() => { props.navigation.navigate('Home') }}
            // closeOnDragDown={true}
            >
                <View style={{ alignItems: 'center', alignSelf: 'center', justifyContent: 'center', width: '100%', paddingVertical: '3%', }}>
                    {/* <Image source={require('../images/modrek/animal.png')} style={{ resizeMode: 'contain', width: '60%', height: '30%', alignItems: 'center', tintColor: Red, marginTop: '20%', marginBottom: '10%' }} /> */}
                    <View style={{ height: '10%', width: '60%', alignItems: 'center', alignSelf: 'center', justifyContent: 'center', }}>
                        <Text style={{ fontSize: screenWidth / 20, fontFamily: appFontBold, color: MediumGrey, }}>{strings('lang.Report')}</Text>
                    </View>

                    <View style={{ width: '90%', height: '60%', alignSelf: 'center', marginVertical: '2.5%' }}>
                        <Text style={{ fontSize: screenWidth / 26, fontFamily: appFont, color: Black, alignSelf: 'flex-start' }}>{strings('lang.Pleasewritethereport')}</Text>
                        <Textarea
                            onChangeText={text => setReport(text)}
                            value={report}
                            style={{
                                width: '100%',
                                alignSelf: 'center',
                                borderRadius: 15,
                                paddingHorizontal: '5%',
                                borderColor: MediumGrey,
                                color: DarkGrey,
                                borderWidth: 1,
                                fontFamily: appFontBold,
                                height: '85%',
                                textAlignVertical: 'top',
                                fontSize: screenWidth / 30,
                                textAlign: I18nManager.isRTL ? 'right' : 'left',
                            }}
                        />
                    </View>
                    <View style={{ alignItems: "center", justifyContent: 'space-between', width: '90%', alignSelf: "center", height: '25%', flexDirection: 'row' }}>
                        <Button onPress={() => {
                            if (request == 1) {

                            } else if (request == 2) {

                            } else if (request == 3) {
                                reportVipTrip();
                            } else if (request == 4) {
                                reportCityTrip();
                            }
                        }} style={{ width: '45%', alignSelf: "center", height: '45%', backgroundColor: DarkBlue, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.Confirm')}</Text>
                        </Button>
                        <Button onPress={() => { refRBSheet.current.close(); modalizeRef3.current.open() }} style={{ width: '45%', alignSelf: "center", height: '45%', backgroundColor: WhiteGery, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Black, }}>{strings('lang.back')}</Text>
                        </Button>
                    </View>
                </View>
            </RBSheet>

            {/* reasons rbsheet */}
            <RBSheet
                ref={refRBSheetReasons}
                height={screenHeight / 1.5}
                openDuration={280}
                customStyles={{
                    container: {
                        // justifyContent: "center",
                        alignItems: "center",
                        width: '100%',

                        alignSelf: 'center',
                        borderRadius: 15

                    },
                    wrapper: {

                    },
                    draggableIcon: {

                    }
                }}
                // onClose={() => { modalizeRef3.current.open() }}
                closeOnDragDown={true}
            >
                <ScrollView
                    style={{ alignSelf: 'center', width: '100%', paddingVertical: screenHeight / 50 }}
                    showsVerticalScrollIndicator={false}
                >
                    {/* <View style={{ alignItems: 'center', alignSelf: 'center', justifyContent: 'flex-start', width: '100%', paddingVertical: '3%', }}> */}
                    {/* <Image source={require('../images/modrek/animal.png')} style={{ resizeMode: 'contain', width: '60%', height: '30%', alignItems: 'center', tintColor: Red, marginTop: '20%', marginBottom: '10%' }} /> */}
                    <View style={{ height: screenHeight / 25, width: '60%', alignItems: 'center', alignSelf: 'center', justifyContent: 'center', }}>
                        <Text style={{ fontSize: screenWidth / 25, fontFamily: appFontBold, color: DarkGrey, }}>{strings('lang.Whatisthereasonforcancelingthetrip')}</Text>
                    </View>
                    {reasons.map((item, index) => {
                        return <Pressable
                            onPress={() => { setReasonId(item.id) }}
                            style={[styles.reasonContainer, {
                                backgroundColor: item.id == reasonsId ? DarkBlue : WhiteGery,
                                borderColor: item.id == reasonsId ? DarkBlue : MediumGrey,
                            }]}>
                            <Text style={{
                                fontSize: screenWidth / 30,
                                fontFamily: appFontBold,
                                color: item.id == reasonsId ? White : Black,
                            }}>{item.reason}</Text>
                        </Pressable>
                    })}


                    <View style={{ width: '95%', height: screenHeight / 7, alignSelf: 'center', marginVertical: '2.5%' }}>
                        {/* <Text style={{ fontSize: screenWidth / 26, fontFamily: appFont, color: Black, alignSelf: 'flex-start' }}>{strings('lang.Pleasewritethereason')}</Text> */}
                        <Textarea
                            placeholder={strings('lang.Pleasewritethereason')}
                            onChangeText={text => setReasonText(text)}
                            value={reasonText}
                            style={{
                                width: '100%',
                                alignSelf: 'center',
                                borderRadius: 10,
                                paddingHorizontal: '5%',
                                borderColor: MediumGrey,
                                color: DarkGrey,
                                borderWidth: 1,
                                fontFamily: appFontBold,
                                height: '100%',
                                textAlignVertical: 'top',
                                fontSize: screenWidth / 30,
                                textAlign: I18nManager.isRTL ? 'right' : 'left',
                            }}
                        />
                    </View>
                    {/* </View> */}
                </ScrollView>
                <View style={{ alignItems: "center", justifyContent: 'space-between', width: '95%', alignSelf: "center", height: screenHeight / 20, flexDirection: 'row', }}>
                    <Button onPress={() => {
                        if (request == 1) {
                            cancelOffer()
                        } else if (request == 2) {
                            cancelPackageTrip()
                        } else if (request == 3) {
                            cancelVipTrip();
                        } else if (request == 4) {
                            canceCityTrip();
                        }
                    }} style={{ width: '45%', alignSelf: "center", height: '100%', backgroundColor: DarkBlue, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                        <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.Confirm')}</Text>
                    </Button>
                    <Button onPress={() => { refRBSheetReasons.current.close(); setViewID(3) }}
                        style={{ width: '45%', alignSelf: "center", height: '100%', backgroundColor: WhiteGery, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                        <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Black, }}>{strings('lang.back')}</Text>
                    </Button>
                </View>
                <View style={{ height: screenHeight / 20 }}></View>

            </RBSheet>

            {/* <View style={{ height: screenHeight / 15 }}></View> */}
        </View>
    )
}


const styles = StyleSheet.create({
    container: {
        height: screenHeight / 9,
        width: '95%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row',
        marginTop: '5%',
    },
    reasonContainer: {
        height: screenHeight / 18,
        borderRadius: 20,
        width: '95%',
        backgroundColor: WhiteGery,
        borderWidth: .8,
        borderColor: MediumGrey,
        marginVertical: '2%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
    },
    input1: {
        height: screenHeight / 10,
        borderRadius: 20,
        width: '95%',
        backgroundColor: White,
        borderWidth: .8,
        flexDirection: 'row',
        paddingStart: '2%',
        borderColor: MediumGrey,
        marginVertical: '5%',
        alignSelf: 'center'
    },
    imageContainer: {
        height: '80%',
        width: '10%',
        alignSelf: 'flex-start',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    image: {
        height: '25%',
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
    },
    locationImage: {
        height: '100%',
        width: '90%',
        resizeMode: 'contain'
    },
    addressContainer: {
        height: '80%',
        width: '90%',
        alignSelf: 'flex-start',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
    },
    blackText: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
    },
    handle__shape: {
        alignSelf: 'center',
        width: screenWidth / 6,
        height: 6,
        borderRadius: 10,
        // backgroundColor: Black,
    },

    modalize__content: {
        marginTop: 'auto',
        borderTopLeftRadius: 30,
        borderTopRightRadius: 30,
        zIndex: 100, shadowColor: '#000000', shadowOffset: { width: 5, height: 5 }, shadowRadius: 8, shadowOpacity: 0.3,
        elevation: 5,
    },
    modal: {
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'space-between',
        width: '95%',
    },
    modal1: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1.2,
        height: screenHeight / 6.5,
        position: 'absolute',
        top: screenHeight / 3,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10,
        paddingVertical: '1%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },

    modalbuttonContainer: {
        height: 35,
        width: '60%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: MediumGreen,
        alignSelf: 'center',
        borderRadius: 5
    },
    modalbuttonContainer2: {
        height: 35,
        width: '35%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: appColor1,
        alignSelf: 'center',
        borderRadius: 5
    },
    modalbuttonText: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 28, color: White
    },
    contentContainer: {
        width: '95%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2%',
        height: screenHeight / 15,
        // borderRadius: 10,
        paddingHorizontal: 0,
        flexDirection: 'row'
    },
    textInput: {
        width: '100%',
        flexDirection: 'row',
        alignSelf: 'center',
        borderRadius: 20,
        borderColor: MediumGrey,
        height: screenHeight / 15,
        borderWidth: 1,
        paddingHorizontal: '3%',
        alignItems: 'center',
        justifyContent: 'space-between',
        textAlignVertical: 'center',
        fontFamily: appFont,
        fontSize: screenWidth / 32,
        textAlign: I18nManager.isRTL ? 'right' : 'left',
    },
    locationContainer: {
        width: '100%',
        // flexDirection: 'row',
        alignSelf: 'center',
        height: '100%',
        alignItems: 'flex-start',
        justifyContent: 'center',

    },
    priceContainer: {
        width: '100%',
        flexDirection: 'row',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 5,
        backgroundColor: MediumGrey,
        height: '100%',


    },
    carsContainer: {
        width: screenWidth / 5.25,
        height: '85%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: White,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: MediumGreen,
        marginHorizontal: '2%'
    },
    carsContainer1: {
        width: screenWidth / 5.25,
        height: '85%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: WhiteGery,
        borderRadius: 5,
        marginHorizontal: '2%'
    },
    carsImageContainer: {
        width: screenWidth / 6,
        height: screenHeight / 30,
        alignSelf: 'center',
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center'
    },
    carsImage: {
        width: '100%',
        height: '100%',
        resizeMode: 'contain',
    },
    carsImage1: {
        width: '70%',
        height: '70%',
        resizeMode: 'contain'
    },
    lightTextBlack: {
        color: Black,
        fontFamily: appFont,
        fontSize: screenWidth / 40,
    },
    locationImage: {
        resizeMode: 'contain',
        width: '100%',
        height: '100%'
    },
    profileModal: {
        flexDirection: 'row',
        height: screenHeight / 14,
        width: '95%',
        marginVertical: '5%',
        alignItems: 'center',
        alignSelf: 'center',
        marginBottom: screenHeight / 5
    },
    iconModal: {
        height: '100%',
        width: '80%',
        resizeMode: 'contain',
    },
    textModal: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        color: Black, marginHorizontal: '3%'
    },
    optionContainer: {
        alignSelf: 'center',
        width: '95%',
        height: 33,
        marginVertical: '.5%',
    },
    optionSubContainer: {
        alignSelf: 'center',
        width: '95%',
        height: '100%',
        flexDirection: 'row',
        alignItems: 'center',
    },
    iconContainer: {
        width: screenHeight / 30,
        height: screenHeight / 30,
        borderRadius: screenHeight / 60,
        marginHorizontal: '5%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: WhiteGery
    },
    iconModal1: {
        width: '65%',
        height: '65%',
        resizeMode: 'contain',
        tintColor: Black,
    },
    label: { color: DarkGrey, fontSize: screenWidth / 30, fontFamily: appFontBold, alignSelf: 'flex-start' },
    labelContainer: { width: '85%', height: '100%', justifyContent: 'center' },
    iconContainer1: { width: 50, height: 50, borderRadius: 10, alignItems: 'center', justifyContent: 'center', backgroundColor: WhiteGery },
    touchableText: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 28, marginStart: 10, alignSelf: 'flex-start' },
    touchableContainer: { borderBottomWidth: 1, borderTopWidth: 1, borderColor: MediumGrey, width: screenWidth / 1.1, height: 60, alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
    icon: { width: '4%', height: '80%', resizeMode: 'contain', tintColor: Black, transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] },
    icon1: { width: '60%', height: '60%', resizeMode: 'contain', tintColor: appColor1, },
    labelModal: { color: Black, fontSize: screenWidth / 26, fontFamily: appFontBold, alignSelf: 'center', marginStart: '13%' },
    bottomView: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        width: '100%',
        backgroundColor: 'white',
        // paddingVertical: '2%',
        borderTopWidth: 1,
        borderTopColor: MediumGrey,
    },
});

export default MapDriver;
