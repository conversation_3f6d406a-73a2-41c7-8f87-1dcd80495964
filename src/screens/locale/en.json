{"lang": {"Login_Successfuly": "<PERSON>gin <PERSON>y", "OrLoginWith": "Or Login With", "Register_Successfuly": "Registration Successfuly", "EmailOrPhone": "Email or Phone", "Logout": "Logout", "DeleteAccount": "Delete account", "Login": "<PERSON><PERSON>", "Login1": "Client", "Searchingforadriver": "Searching for a driver", "Login2": "Driver", "Youraccountistemporarilysuspended": "Your account is temporarily suspended", "mobile_number": "Mobile number", "No_Results": "No Results", "Contactsupport": "Contact support", "Deliverypartnerships": "Delivery partnerships", "Submit": "Submit", "Couponenteredsuccessfully": "Coupon entered successfully ", "orderFemale": "Now with the female STEP Captains service, you can request a female captain", "totalprofits": "Total profits", "Priceafterdiscount": "Price after discount", "youwin": "You win", "inwallet": "In wallet", "congratulations": "Congratulations", "Walletcharging": "Wallet charging", "Totalbalance": "Total balance", "willbeadded": "will be added", "Inthewallet": "to your wallet", "Thediscountwillbeaddedtothewallet": "The discount will be added to the wallet", "Receivablesdetails": "Receivables details", "Tripprice": "Trip price", "Choosethewalletchargingmethod": "Choose the wallet charging method", "Shippedsuccessfully": "Shipped successfully", "Shippingoperations": "Shipping operations", "Requestfordues": "Request for dues", "shipping": "shipping", "Companycommission": "Company commission", "thedemandprice": "The demand price", "Switchtoapassenger": "Switch to a passenger", "Switchtodriver": "Switch to driver", "mapdriver": "Map Driver", "minimumWallet": "The minimum value for charging the wallet is 50 riyals", "Intercityrequest": "Intercity request", "repeataction": "Repeat action", "reputation": "Reputation", "Experience": "Experience", "reviews": "Reviews", "audit": "Audit", "Trips": "Trips", "myearnings": "My earnings", "Triprequests": "Trip requests", "DriverRequests": "Driver Requests", "Mapp": "Mapp", "Thelist": "The list", "personalinformation": "Personal information", "Datamodification": "Data modification", "detailsofthetrip": "details of the trip", "Rateing": "Rateing", "Model": "Model", "Acceptanceinreturn": "Acceptance in return", "Showyourquote": "Enter your price", "Theclienthasbeenreached": "The client has been reached", "imageofthecarfromtheback": "Image of the car from the back", "imageofthecarfromthefront": "Image of the car from the front", "carplatenumber": "Car plate number", "carcolor": "Car color", "manufacturingyear": "Manufacturing year", "Therearecurrentlynonewrequestsfrompassengers": "There are currently no new requests from passengers", "carType": "Car Type", "cardata": "Car data", "driverdata": "Driver data", "Youmustbeconnectedtoreceiveordersfrompassengers": "You must be connected to receive orders from passengers", "connected": "Connected", "notConnected": "Not connected", "messageOrders": "There are currently no new requests from passengers", "Triplog": "Trip log", "Weeklysubscription": "Weekly subscription", "Monthlysubscription": "Monthly subscription", "3monthssubscription": "3 months subscription", "6monthssubscription": "6 months subscription", "yearsubscription": "year subscription", "Subscribe": "Subscribe", "vehicledata": "Vehicle data", "Criminalstatus": "Criminal status", "imageofthevehicleregistrationformfromthefront": "Image of the vehicle registration form from the front", "imageofthedriverslicensefromthefront": "Image of the driver's license from the front", "ImageoftheIDorIqamafromthefront": "Image of the ID or Iqama from the front", "nationaladdress": "National address", "driverregistration": "Driver registration", "cashMoney": "Cash money", "returnpath": "return path", "Cancellationoftheriderequest": "Cancellation of the ride request", "repeatrequest": "repeat request", "Orderhistory": "Order history", "Securityandsafety": "Security and safety", "Technicalsupport": "Technical support", "Switchtotraveler": "Switch to traveler", "Subscriptionconfirmedsuccessfully": "Subscription confirmed successfully", "Subscriptionpackages": "Subscription packages", "Please_insert_valid_phone": "Please insert valid phone", "Please_insert_phone": "Please insert phone", "Please_insert_image": "Profile image", "Please_insert_New_Password": "Please insert new password", "Change_Password": "Change Password", "Old_Password": "Old password", "Pleaseentertheverificationcodesenttoyouconsistingof4digits": "Please enter the verification code sent to youconsisting of 4 digits", "Resubmitafter": "Resubmit after", "New_Password": "New Password", "youwelcomein": "you welcome in", "verificationcode": "verification code", "Forget_Password": "Forget password", "Password": "Password", "ReType_New_Password": "Retype new Password", "Confirm": "Confirm", "Send": "Send", "Name": "Name", "Edit_Profile": "Edit Profile", "notes": "Notes", "Notes": "Notes", "Email": "E-mail", "Emailor?": "Email or phone", "Phone": "Phone", "Language": "Language", "Change": "Change", "forgot?": "Forgot your password?", "New_user": "New user? Please ", "Notifications": "Notifications", "No_Notifications": "No Notifications", "Create_Account": "Create Account", "Reset_Password": "Reset Password", "Search": "Search", "PleaseChooseCountryFirst": "Please Choose Country First", "Account": "Profile", "password": "Password", "quiz_account": "already have an account ?", "Forget_Password_?": "Forget Password ?", "back": "Back", "Date": "Date", "quiz_account_2": "Don't have account ? Please", "Please_insert_name": "Please insert name", "Please_insert_field": "Please insert field", "Please_insert_lastname": "Please insert lastname", "Please_insert_email": "Please insert email", "Please_insert_password": "Please insert password", "Phone_must_be_11_characters": "Phone must be 11 characters", "Password_must_be_more_6_characters": "Password must be more 6 characters", "Please_insert_password_confirmation": "Please insert password confirmation", "Password_confirmation_must_be_more_6_characters": "Password confirmation must be more 6 characters", "Please_insert_old_password": "Please insert old password", "old_password_must_be_more_6_characters": "old password must be more 6 characters", "password_must_be_matched": "Password must be matched", "login_continuou": "please login to continue", "something_wrong": "Something Went Wrong", "something_wrong1": "You cannot send complaint now", "setting": "Settings", "country": "Country", "City": "City", "Country": "Country", "Area": "Area", "addaD": "Add AD", "editaD": "Edit AD", "CoverPhoto": "Cover Photo", "next": "Next", "previous": "Previous", "paymentDetails": "Methods of payment and communication", "price": "price", "Installmentsavailable": "Installments available", "Delegated": "Delegated", "address": "Ad Address", "Details": "Details", "Type_of_property": "Type of property", "Choose_a_department": "Choose a department", "Overlooking": "Overlooking", "Space": "Space", "Where _is_your_property_located?": "Where is your property located?", "special_marque": "special mark", "Detailed_data_for_the_property": "Detailed data for the property", "the_rooms": "the rooms", "Floor": "Floor", "Baths": "Baths", "The_year_of_construction": "The year of construction", "Finishing_type": "Finishing type", "addnote": "Add Note", "note": "Note", "add": "Add", "cancel": "Cancel", "Customer_Service": "Customer Service", "message": "message", "osol_alalmya": "Osol Alalmya", "for_digital_investment_and_marketing": "For digital investment and marketing", "choose_language": "Choose Language", "choose_countrey": "Choose Country", "Like": "Like", "countrey": "Country", "Egypt": "Egypt", "SaudiArabia": "SaudiArabia", "Rent": "Rent", "Filter": "Filter", "Depart": "<PERSON><PERSON><PERSON>", "Call": "Call", "PoweredBy_Osol": "PoweredBy Osol", "sign_in_with": "Sign in with Social Media", "not_now": "Not now", "Registerion": "Registerion", "User_Type": "User Type", "Owner": "Owner", "Register": "Register", "I_agree_to_the_terms_and_conditions": "I agree to the terms and conditions", "terms_and_conditions": "Terms and conditions", "Do_you_want_Arabic_text_and_a_question": "Do you want Arabic text and a question?", "Home": "Home", "Share": "Share", "Deal_of_the_day": "Deal of the day", "News": "News", "Favourites": "Favourites", "Favourite": "Favourite", "Osolel3almya": "Osolel 3almya", "More": "More", "Messages": "Messages", "Reply": "Reply", "SendMessage": "SendMessage", "You": "You", "On": "On", "EGP": "EGP", "view": "view", "Off": "Off", "Contact_Us": "Contact Us", "Tell_us_what_you_think": "Tell us what you think", "About_osol_alalmya": "About osol <PERSON>", "About_osol": "About OSOL", "Profile": "Profile", "Save": "Save", "Add_Real_Estate": "Add Real Estate", "My_Real_Estates": "My Real Estates", "My_Notes": "My Notes", "Share_App": "Share App", "Exit": "Exit", "BathRooms": "BathRooms", "BedRooms": "Bed Rooms", "ADSAvailabletoday": "ADS Available today", "TodayDeals": "Today Deals", "Closed": "Closed", "MyAds": "My Ads", "MyNotes": "My Notes", "AdDetails": "Ad Details", "Location": "Location", "Rooms": "Rooms", "Edit": "Edit", "Finishing": "Finishing", "Buildingyear": "Building year", "Seller": "<PERSON><PERSON>", "SendAdvertiser": "Send Advertiser", "NewsDetails": "News Details", "CallAdvertiser": "Call Advertiser", "Intialdata": "Intial Data", "BuildingAddress": "Building Address", "data_details_for_build": "data details for build", "customertype": "Please choose customer type", "country_id": "Please choose country ", "Please_enter_note_first": "Please enter note first", "Please_enter_reason_first": "Please reason note first", "Please_update_note_first": "Please Update note first", "sign_up_with": "Sign up with", "Egp": "EGP", "Morenews": "More News", "Today": "Today", "Deal": "Deal", "My_Services": "My Services", "legal_services": "legal services", "Transfer_of_Ownership": "Transfer of Ownership", "Real_estate_tax": "Real estate tax", "Other_services": "Other services", "Real_estate_services": "Real estate services", "Selling_to_others": "Selling to others", "Real_estate_consulting": "Real estate consulting ", "Building": "Building", "loginMessage": "Please check username and password", "Done": "Done", "Reset": "Reset", "Filter_by": "Filter <PERSON>", "Ad_Date": "Ad Date", "View": "View", "Yes": "Yes", "No": "No", "News_Title": "News Title", "ViewON": "View ON", "adIsNegotiable": "Negotiable", "Description": "Description", "addressD": "Address", "mustLogin": "Please login to continue", "addtofav": "added to favourite successfully", "StausChangedSuccessfully": "Status Changed Successfully", "AdStatus": "Ad Status", "OpenLocationOnMap": "Open location on map", "Oldpasswordnotright": "Old password not right", "passwordchangedsuccessfully": "password changed successfully", "profileupdatedsuccessfully": "profile updated successfully", "Delete": "Delete", "Update": "Update", "newest": "Newest", "lowPrice": "Low Price", "highPrice": "High Price", "SAR": "SAR", "Insert_Phone": "Insert Phone", "activation_Code": "Activation Code", "Wrong_Code": "Wrong Code", "ad_Successfuly": "Your AD uploaded successfully", "Publish": "Publish", "addnotessuccess": "Added successfully", "Pleasewaitad": "Adding the advertisement and uploading Images, please wait", "yourphone": "Your Phone is ", "ChooseStatus": "Choose<PERSON><PERSON><PERSON>", "Activate": "Activate", "Enter_the_activation_code": "Enter the activation code", "Enter_a_phone_number": "Enter the activation code", "Activate_the_phone_number": "Activate the phone number", "Select_your_location": "Select your location", "Locate": "Locate", "LocateTheConnection": "Locate the connection", "Shopping": "Shopping", "ChooseARegion": "Choose a region", "AddressList": "Address list", "DeliveryToAnotherAddress": "Delivery to another address", "ChooseTheLocationOnTheMap": "Choose the location on the map", "SidiBisher": "<PERSON><PERSON>", "GamalAbdelNasserST": "<PERSON><PERSON><PERSON> Abd <PERSON> St.", "AddressDetails": "Address details", "AdditionalAddress": "Additional Address", "AddressType": "Address Type", "Street": "Street", "Department": "Department", "AddMoreDirections": "Add More Directions", "AdditionalPhone": "Additional Phone", "SpecialMark": "Special Mark", "Optional": "(Optional)", "Processing_Request": "Processing request", "Go_To_Home": "Go to home", "Adresses": "Adresses", "Requests": "Requests", "Previous_Requests": "Previous requests", "Favorite_Products": "Favorite products", "Credit_Card": "Credit card", "Cart": "<PERSON><PERSON>", "YourCart": "Your Cart", "Offers": "Offers", "Add_To_Cart": "Add to cart", "Call_On": "Call on", "Special_Offers": "Special offers", "The_Most_Prominent_Sections": "The most prominent sections", "CompleteRegistration": "Complete Registration", "Orders": "Requests", "Complaints": "<PERSON><PERSON><PERSON><PERSON>", "Complaint": "<PERSON><PERSON><PERSON><PERSON>", "WhoAreWe": "Whe are we", "CallUs": "Call us", "ContactUs": "Contact Us", "PrivacyPolicy": "Privacy policy", "YouDoNotHaveAnAccount": "You do not have an account", "RegisterNow": "Register now", "ActivateTheAccount": "Activate the account", "CompleteTheData": "Complete the data", "EnterTheActivationCode": "Enter the activation code", "Forename": "<PERSON><PERSON><PERSON>", "LastName": "Last name", "IDNumber": "ID number", "TheIDNumberIsWrongPleaseTryAgainWithAValidIDNumber": "The ID number is wrong please try again with a valid ID number", "IAgreeToTheTermsAndConditions": "I agree to the terms and conditions", "ChooseAWorker": "Choose a worker", "ChooseWorker": "Please choose worker first", "CustomRequest": "Custom request", "ReplacementRequest": "Replacement request", "FileAComplaint": "Send a complaint", "TrackEmployment": "Track employment", "ConnectiveEmployment": "Arrival workers", "ComplaintsBox": "Complaints box", "Replies": "Replies", "ComplaintDetails": "<PERSON><PERSON><PERSON><PERSON>", "YouCannotChooseFromDifferentCountries": "You cannot choose from different countries", "TheName": "The name", "Job": "Job", "Countrey": "<PERSON><PERSON>", "Age": "Age", "Order now": "OrderNow", "Religion": "Religion", "filtering": "Filtering", "Show": "Show", "ShowAll": "Show all", "CurriculumVitae": "Curriculum Vitae", "WorkerNumber": "Worker number", "MonthlySalary": "Monthly salary", "Nationality": "Nationality", "DateOfBirth": "Date of birth", "PlaceOfBirth": "Place of birth", "SocialStatus": "Social status", "NumberOfChildren": "Number of children", "MobileNumber": "Mobile", "AcademicLevel": "Academic level", "Weight": "Weight", "Length": "Length", "passport": "passport", "TheNumber": "The number", "ReleaseDate": "Release date", "ExpiryDate": "Expiry date", "placeOfIssue": "place of issue", "Duration": "Duration", "Languages": "Languages", "Arabic": "Arabic", "English": "English", "HomeWork": "HomeWork", "Cleaning": "Cleaning", "ChildRearing": "ChildRearing", "WashingAndIroning": "Washing and ironing", "TheCook": "The cook", "OtherSkills": "Other skills", "Pending": "Pending", "Processing": "Processing", "Expired_Canceled": "Expired/Canceled", "Canceledd": "Canceled", "Expired": "Expired", "TheSideOfTheComplainant": "The defendant", "Worker": "Worker", "Office": "Office", "TheNameOfTheEmployee": "The name of the employee", "AttachAFile": "Attach a file", "ItDoesNotPerformTheRequiredTasks": "It does not perform the required tasks", "UncooperativeInCarryingOutHisDuties": "Uncooperative in carrying out His duties", "Laziness": "Laziness", "TalkingOnTheMobilePhonePermanently": "Talking on the mobile phone permanently", "Naughty": "Naughty", "LackOfAttentionToOrders": "Lack of attention to orders", "Others": "Others", "ComplaintsBox1": "No title for this complaint", "AlternativeRequests": "Alternative requests", "ReasonForReporting": "Reason for reporting", "Report": "Report", "Visas": "Visa", "VisaNumber": "Visa number", "visaType": "visa type", "visaNumber": "visa number", "VisaDate": "Visa date", "Addition": "Addition", "TrackYourWorkers": "Track your workers", "MedicalExamination": "Medical examination", "Authorization": "Authorization", "Contract": "Contract", "School": "School", "LabourOffice": "Labour office", "ImplementationOf": "ImplementationOf", "Ticket": "Ticket", "PersonalData": "Personal data", "Status": "Status", "OrderNumber": "Order number", "Airline": "Airline", "VoucherInformation": "Voucher information", "DownloadTheContract": "Download the contract", "SpecialRequests": "Special requests", "Melady": "<PERSON><PERSON>", "Hijri": "<PERSON><PERSON><PERSON>", "Free": "Free", "WorkerAccessInformation": "Worker access information", "ArrivalTime": "Arrival time", "AnAirport": "An airport", "Destination": "Destination", "FutureName": "Future name", "FlightNumber": "Flight number", "CarrierLines": "Carrier lines", "Gender": "Gender", "Male": "Male", "Female": "Female", "Muslim": "Muslim", "Christian": "<PERSON>", "Average": "Average", "AboveAverage": "Above average", "High": "High", "M_A": "M.A.", "PhD": "PhD", "TheShape": "TheShape", "ArrivalStation": "Arrival station", "Condition1": "Condition 1", "Condition2": "Condition 2", "Condition3": "Condition 3", "Confirmation": "Confirmation", "ArrivingWorkers": "Arriving workers", "DateOfArrival": "Date of arrival", "AirportName": "Airport name", "ProfilePersonly": "Profile personly", "OldPassword": "Old password", "NewPassword": "New password", "ConfirmNewPassword": "Confirm New password", "PleaseEnterNewPassword": "Please enter new password", "TermsAndConditions": "Terms and conditions", "ApplicationPolicy": "Application policy", "ApplicationPermissions": "Application permissions", "ApplicationUpdates": "Application updates", "TheNameOfTheOffice": "The name of the office", "Address": "Address", "Region": "Region", "District": "District", "UnifiedNumber": "Unified number", "Topic": "Topic", "Map": "Map", "edit": "Edit", "track": "Track", "pleaseCkeckData": "Please ckeck all data", "pleaseEnterData": "Please enter all data", "pleaseEnterWorker": "Please choose worker", "wrongCode": "Wrong code", "addDone": "Added successfully", "ThereIsNoOrdes": "There is no ordes", "ThereIsNoComplaints": "There is no complaints", "ThereIsNoNotify": "There is no notifications", "ThereIsNoWorkers": "There is no workers", "ActivateAccount": "Activate account", "CompleteData": "Complete data", "termsConditions": "Please agree to terms and conditions", "DuplicatedIdentityNumber": "Duplicated identity number", "10Num": "Identity number must be 10 digit", "NO_DATA_FOR_THIS_CHOICES": "There are no workers", "NO_DATA_FOR_THIS_CHOICES2": "There are no contracts", "displayReplies": "<PERSON><PERSON><PERSON> replies", "downloaded": "Stored in files", "PermissionDenied": "Download permission denied", "chooseAgain": "<PERSON>sse again", "licenseNumber": "license number", "officeData": "office data", "Resendcode": "Resend code", "verification": "verification", "Ordersandcontracts": "Orders and contracts", "Householdlaborwithmonthlyrental": "Household labor with monthly rental", "laborsector": "labor sector", "Intermediateinrecruitment": "Intermediate in recruitment", "medicalsector": "medical sector", "Driverswithmonthlyrental": "Drivers with monthly rental", "Individualsector": "Individual sector", "Laborsectordemand": "Labor sector demand", "send": "send", "Rentalapplicationsandcontracts": "Rental applications and contracts", "paid": "paid", "Unpaid": "Unpaid", "payingoff": "paying off", "NumberofWorkers": "Number of Workers", "thedetails": "Details", "theamount": "the amount", "Package": "Package", "Thecontractperiod": "The contract period", "Paymentmethod": "Payment method", "serviceprovider": "service provider", "Orderdetails": "Order details", "Signature": "Signature", "Packages": "Packages", "Packageprice": "Package price", "totaltax": "total tax", "Contractstartdate": "Contract start date", "Skillsandhomework": "Skills and homework", "otherskills": "other skills", "Previousexperience": "Previous experience", "Recruit": "Recruit", "Packagedetails": "Package details", "taxprice": "tax price", "Total": "Total", "Transportingtheworkertotheresidence": "Transporting the worker to the residence", "SR": "SR", "detectlocation": "detect location", "Pleasereviewthetermsofthecontractbeforesigning": "Please review the terms of the contract before signing", "Iagree": "I agree to", "Termsofthecontract": "Terms of the contract", "Pay": "Pay", "mytickets": "my complaints", "newticket": "new complaint", "Closedtickets": "Closed complaintس", "Complaint'sname": "<PERSON><PERSON><PERSON><PERSON>'s name", "complaintnumber": "complaint number", "Servicename": "Service name", "change": "change", "fullname": "full name", "Accountdetails": "Account details", "Paymentdata": "Payment data", "paymentInfo": "Payment Info", "Bankaccount": "Bank account", "Intermediaterequests": "Intermediate requests", "Chooseemployment": "Choose employment", "Specialrequest": "Special request", "educationalqualification": "educational qualification", "Othercertificates": "Other certificates", "Scientificexpertise": "Scientific expertise", "Personalskills": "Personal skills", "Subjectname": "Subject name", "Subjectdetails": "Subject details", "testText": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.", "testDate": "10 minutes ago", "package": "subscription", "month": "Month", "from": "from", "noofproviders": "no. of providers", "contractnotavailable": "A contract is not available until the availability of labor", "familyNo": "number of family", "mediationinrecruitment": "Mediation inrecruitment", "choosePacFirst": "please choose package first", "chooseCouFirst": "please choose countrey first", "chooseWorrkerFirst": "please choose workers first", "chooseAddressFirst": "please choose address first", "acceptTerms": "please accept Terms and conditions first", "noWorkers": "There is no workers", "Paid": "<PERSON><PERSON> successfully", "Payment": "Payment", "PaymentStatus": "Payment status", "Username": "User name", "Forgotyourpassword": "Forgot your password?", "Donthaveanaccountpleasedo": "Don't have an account ? ", "Signup": "Sign up", "Skip": "<PERSON><PERSON>", "Confirmpassword": "Confirm password", "Iagreetothetermsandconditions": "I agree to the terms and conditions", "Pleaseenterthe4digitcodesenttoyou": "Please enter the 4-digit code sent to you", "Resendthecodewithin": "Resend the code within", "Seconds": "Seconds", "Deliverytimes": "Delivery times", "Choosedeliverytime": "Choose delivery time", "PM": "PM", "AM": "AM", "Choosetheday": "Choose the day", "Whatareyoulookingfor": "What are you looking for?", "Deliveryto": "Delivery to", "DeliveryLocation": "Delivery Location", "Sections": "Sections", "SpecialOffers": "Special Offers", "MyAccount": "My account", "All": "All", "SortBy": "Sort By", "Discount": "Discount", "limited": "for limited time", "Addtocart": "Add to cart", "Theproduct": "The product", "Thesize": "Size", "Evaluation": "Rates", "Quantity": "Quantity", "Addtocartforfixeddelivery": "Add to cart for fixed delivery", "Pillprice": "Pill price", "Cartonprice": "Carton price", "Add": "Add", "Constant": "Constant", "Basket": "Basket", "Subtotal": "Subtotal", "Productsweremindyouof": "Products we remind you of", "Continueshopping": "Continue shopping", "Nextone": "Next one", "Completetheapplication": "Complete order", "Titleanddate": "Title and date", "Addatitle": "Add a title", "Choosethecity": "Choose the city", "Specialmarque": "Special mark", "Totalproductsinclusiveofqqt": "Total products inclusive of qqt", "Delivery": "Delivery", "Discountvalue": "Discount value", "Finaltotal": "Final total", "Portfolio": "Wallet", "Itsinthewallet": "It's in the wallet", "Point": "Point", "Replacewith": "Replace with", "Redeempoints": "Redeem points", "Enterthediscountcode": "Enter the discount code", "Enter": "Enter", "Areyouaspecialcustomer": "Are you a special customer?", "Chooseyourpaymentmethod": "Choose your payment method", "Paymentbymada": "Payment by mada", "ApplepayPayby": "Apple pay Pay by", "PaybyVisa": "Pay by Visa", "Stcpayby": "Pay by stc", "Paiementwhenrecieving": "Payment when recieving", "Currentbalance": "Current balance", "Takeadvantageoftheexcellencediscount": "Take advantage of the excellence discount", "Pleaseenterthemobilenumberofthecustomer": "Please enter the mobile number of the customer", "Resendthecode": "Resend the code", "Deliverytime": "Delivery time", "Byweight": "By weight", "ByOption": "By option", "ExpectedProducts": "Expected products", "Youhave": "You have", "Breakevenpoint": "Breakeven point", "Walletbalance": "Wallet balance", "Ordertracking": "Order tracking", "reOrder": "Re-Order", "Orderconfirmed": "Order confirmed", "Buyingsucceeded": "Buying succeeded", "Onmyway": "On my way", "Sentdeliveredhanded": "<PERSON><PERSON> delivered handed", "Canceled": "Canceled", "Delivered": "Delivered", "Productrating": "Product rating", "Areyousuretocanceltheorder": "Are you sure to cancel the order", "Cancellingorder": "Cancelling order", "Reorder": "Reorder", "Areyousureaboutreconnecting": "Are you sure about reconnecting", "Baskets": "Baskets", "Totalnumberofproducts": "Total number of products", "Products": "Products", "Basketprice": "Basket price", "Onetimepurchase": "One time purchase", "Subscriptions": "Subscriptions", "Subscribenow": "Subscribe now", "SubscriptionBaskets": "Subscription Baskets", "Youdonthaveabasket": "You don't have a basket", "Createanewbasket": "Create a new basket", "Subscriptionvaliduntil": "Subscription valid until", "Newbasket": "New basket", "Basketname": "Basket name", "DeliveryFrequency": "Delivery Frequency", "Everyday": "Every day", "Everyweek": "Every week", "Permonth": "Per month", "Contact": "Contact", "Reviews": "Reviews", "pleaseputyourrating": "please put your rating", "whatssapp": "Whatsapp", "Filterbymodel": "Filter by model", "new": "new", "thefurthest": "the furthest", "lowestprice": " Lowest price", "themostexpensive": "Highest price", "leastrated": " least rated", "toprated": "top rated", "theclosest": "the closest", "Application": "Application", "old": "old", "Areyousuretodeletetheproduct": "Are you sure to delete the product", "OrdersTracking": "Order Tracking", "FixedConnectionProducts": "Fixed Connection Products", "Directconnection": "Direct connection", "Changepassword": "Change password", "Cancel": "Cancel", "Putyourrating": "Put your rating", "Searchbybarncode": "Search by barn code", "customersservice": "customers service", "knowmore": "know more", "Myorders": "My Orders", "Direct": "Direct", "farmdetails": "farm details", "Determiningthetimeofentrytotheslaughterhouse": "Determining the time of entry to the slaughterhouse", "Cuttingandprocessing": "Cutting and processing", "hi": "hi", "hardware": "hardware", "Advertisers": "Advertisers", "Services": "Services", "Cranes": "cranes", "cranes": "cranes", "Laurie": "<PERSON>", "Changethelanguage": "Change the language", "Transfer": "Transfer", "rate": "rate", "profiledetails": "ProfileDetails", "Attachments": "Attachments", "Sendorder": "Send order", "Unavailable": "Unavailable", "Advertiser": "Advertiser", "Moreabouttheadvertiser": "More about the advertiser", "writeyourmessage": "write your message", "writeyourOrder": "write your order", "Communicateviasocialmedia": "Communicate via social media", "snapchat": "snap chat", "Instagram": "Instagram", "Twitter": "Twitter", "Urdulanguage": "Urdu", "englishLanguage": "English", "arabicLanguage": "Arabic", "withoutaAramcolicense": "without a Aramco license", "Aramcolicense": "Aramco license", "withoutalicense": "without a license", "license": "License", "Averagerentpricefortheday": "Average rent price for the day", "Averagerentpriceforthemonth": "Average rent price for the month", "Priceisnotnegotiable": "Price is not negotiable", "Priceisnegotiable": "Price is negotiable", "thedriver": "the driver", "Adnumber": "Ad number", "Typeoflicense": "Type of license", "stomachmodel": "Equipment model", "stomachsize": "Equipment size", "Mycurrentsubscription": "My current subscription", "PreviousSubscriptions": "Previous Subscriptions", "Bill": "Bill", "numberofads": "number of ads", "Packagetype": "Package type", "Subscriptionnumber": "Subscription number", "typeofmassage": "Type of massage", "myreviews": "my reviews", "maintenancerequest": "maintenance request", "Holidaytype": "Holiday type", "ChooseHolidays": "Choose Holidays", "Anindividual": "An individual", "TaxNumber": "Tax Number", "CommercialRegistrationNo": "Commercial Registration No", "acompany": "a company", "Youdonothaveanyactiveads": "You do not have any active ads", "Addequipment": "Add equipment", "welcometoshawl": "welcome to shawl", "massage1": "You can start adding your own equipmentSubscribe to the package that best suits your needs", "massage2": "Please subscribe and activate your ads Favorites so that customers can view them", "communication": "communication", "stomachname": "Equipment name", "available": "Available", "other": "other", "Averagedailyrent": "Average daily rent", "Averagerent": "Average rent price", "Averagemonthlyrent": "Average monthly rent", "isnotnegotiable": "is not negotiable", "isnegotiable": "is negotiable", "pricediscount": "price discount", "Aramco": "Aramco", "stomachimage": "Equipment image", "massage3": "In the event that a picture is not uploaded, an avatar picture will be automatically placed for the stomach", "Choosethestomach": "Choose the stomach", "stomachcondition": "Equipment condition", "whatsappnumber": "whatsapp number", "Writethesizeofthestomach": "Write the size of the stomach", "Choosestomachsize": "Choose Equipment size", "Choosetheequipmentmodel": "Choose the equipment model", "Notavailableuntil": "Not available until", "until": "Until", "Subscribedsuccessfully": "Subscribed successfully", "Enabled": "Enabled", "themonth": "the month", "Notenabled": "Not enabled", "Fourthpackage": "Fourth package", "Thirdpackage": "Third package", "secondpackage": "second package", "firstpackage": "first package", "bank": "Payment by bank transfer", "bankTransfer": "Bank transfer", "socialist": "Subscription", "socialist1": "My Subscriptions", "PaymentOptions": "Payment Options", "Accessories": "Accessories", "currentPassowrd": "Current Password", "HolidayDetails": "Holiday Details", "DailyRent": "Daily rent", "Monthly": "Monthly", "FinishedAd": "Finished ad", "NotAvailableTill": "Not available till", "messageCode": "Please enter the 4-digit code that was sent to the number", "Sec": "Seconds", "Continue": "Continue", "freePackages": "Free Packages", "freePackage": "Free Package", "days": "days", "free2": "Free", "freePackageDesc1": "Allows you to advertise", "freePackageDesc2": "equipments for the duration of the bundle", "goldenPackages": "Golden Packages", "inMonth": "In Month ", "diamondPackages": "Diamond Packages", "save": "Save", "buy": "Buy", "model": "Model", "productNumber": "Product Number", "currentOrders": "Current Order", "RentFor": "rent for", "StatusOfRequest": "Status Of Request", "ConfirmPassword": "Confirm Password", "PleaseChooseUserType": "Please Choose UserType", "NotifyAdvertisor": "Notify advertisor", "Determinethenumberofrentaldays": "Determine the number of rental days", "Determinethequantity": "Determine the quantity", "Do_you_want_delete": "Do you want to delete address ?", "Do_you_want_delete2": "Do you want to delete advertisement ?", "deletedSucess": "Deleted successfully", "Incaseofchangingtheaddress": "In case of changing the address the products in the cart will be deleted", "Doyouwanttocontinue": "Do you want to continue ?", "Copy": "Copy", "IBANNumber": "IBAN number", "UploadTheTransferImage": "Upload the transfer image", "PleaseTransferAmount": "Please transfer an amount", "OnABankAccount": "on a bank account", "msgg": "We always strive to improve the quality of the application and service through your evaluation of the application services", "LevelOfService": "Level of service", "ServiceProvider": "Service provider", "Suggestions": "Suggestions", "addRateSuccessfully": "successfully Rated", "buyPrice": "Buy price ", "chooseValueDiscount": "Choose value discount", "category": "Category", "chooseCategory": "Choose category", "msggg": "In the event that your request is not available on the application .. please write to us", "pdf": "File", "img": "Image", "SubscribeTill": "subscribed until", "sendSuccessfully": "Send successfully ", "editSuccessfully": "Edit Successfully", "checkthediscountcode": "Check the discount code", "piece": "piece", "DeleteCart": "Delete cart", "Addedtocart": "Added to cart", "chooseLocation": "Please Choose Location", "And": "and", "ThereIsAnUpdate": "There is an update available in the store now", "ThereIsAnMaintananc": "There is an error please try later", "UpdateNow": "Update now", "CannotFindAddress": "Can't find address of this order", "validEmail": "Please enter valid email", "validationPhone": "Please enter phone number start with 05", "deleteAccount": "Delete account", "doYouWantDeleteAccount": "Do you want to delete account?", "Phone_must_be_10_characters": "phone number must be 10 digit", "copyCodeSuccessfully": "<PERSON><PERSON> Successfully", "editImageSuccessfully": "Edite image successfully", "doYouWantDeleteCart": "Do you want to delete cart?", "CodeSentSuccessfully": "Code sent Successfully", "CompletePayment": "Complete payment", "WaitingForConfirmation": "Waiting for confirmation", "PleaseSubscribeToAPackageFirst": "Please subscribe to a package first", "Brands": "Brands", "Discover": "Discover", "SeeMore": "SeeMore", "PromoCode": "Promo code", "CheckOut": "CheckOut", "AreYouSure": "Are you sure", "YouWantTo": "you want to", "Setthetime": "Set the time", "ThisItem": "this item", "Repeatthenumberoftimesaweek": "Repeat the number of times a week", "Timetogo": "Time to go", "returntime": "return time", "Selectthetypeofface": "Select the type of destination", "facetype": "destination type", "ThisOrder": "this order", "Shipping": "Shipping", "VAT": "VAT", "Tripcancellation": "Trip cancellation", "Deliverycompanies": "Delivery companies", "start": "start", "Whatisthereasonforcancelingthetrip": "What is the reason for canceling the trip", "Deliverydata": "Delivery data", "Acceptance": "Acceptance", "Searchforadriver": "Search for a driver", "Raisetheprice": "Raise the price", "Sendtheprice": "Send the price", "ConfirmAPay": "Confirm a pay", "TrackOrder": "Track order", "ThankYou": "ThanK you", "message1": "For buying from StepAll", "AddDeliveryLocation": "Add Delivery Location", "ChoosePaymentMethod": "Choose Payment Method", "SetAsDefaultDeliveryAddress": "Set as default delivery address", "color": "Color", "Refusal": "Refusal", "km": "km", "CancelOrder": "Cancel Order", "ConfirmedOrder": "Confirmed Order", "BuyingSucceeded": "Buying Succeeded", "ShippingOnTheWay": "Shipping On The Way", "comments": "comments", "Choosethelocationonthemap": "Choose the location on the map", "destination": "Destination", "Yourlocationis": "Your location is", "Close": "Close", "CloseApp": "Close app", "Recentlyaccessedsites": "Recently accessed sites", "Favoritesites": "Favorite sites", "Recommendedprice": "Recommended price", "Flightduration": "Flight duration", "minute": "minute", "Pleaseenterthemobilenumber": "Please enter the mobile number", "Ourservices": "Our services", "totalprice": "total price", "Deliveryprice": "Delivery price", "tax": "Tax", "packages": "Packages", "Mypackagedetails": "My package details", "timetomoveon": "time to move on", "Reportavacation": "Report a vacation", "Reportaproblem": "Report a problem", "Addanotherface": "Add another destination", "Tripvalue": "Trip value", "ordervip": "Order VIP", "VIPorderdetails": "VIP order details", "Postalcode": "Postal code", "Thetripwassuccessfullyrejected": "The trip was successfully rejected", "Enternotesaboutthecustomer": "Enter notes about the customer", "Enternotesaboutthedriver": "Enter notes about the driver", "Streetname": "Street name", "male": "Male", "female": "Female", "RequestAccept": "Request Accept", "Successfullyaccepted": "Successfully accepted", "Reportedsuccessfully": "Reported successfully", "socialstatus": "Social status", "bachelor": "Bachelor", "married": "Married", "Pleasewritethereason": "Please write the reason", "Doyousmoke": "Do you smoke", "sabbatical": "Sabbatical", "to": "To", "Tripdate": "Trip date", "Giveyourratingtothecustomer": "Give your rating to the customer", "free": "Free", "Thetripwascompletedsuccessfully": "The trip was completed successfully", "Endthetrip": "End the trip", "message2": "Picture of the car from the right side", "message3": "Picture of the car from the left side", "message6": "Picture of the car from the front", "message7": "Picture of the car from the back", "message8": "Picture of the car Interior", "message4": "Interior photo of the car", "Loginasaguest": "Guest", "hello": "hello", "Dailyrides": "Daily rides", "VIPrequests": "VIP requests", "betweencities": "Between cities", "Thebeginningofthevacation": "The beginning of the vacation", "Theendofthevacation": "The end of the vacation", "messageStep": "The vacation period is non-refundable from the subscription price", "Confirmclientaccess": "Confirm client access", "safety": "Safety", "Speed": "Speed", "commitment": "Commitment", "Driverandvehicledetails": "Driver and vehicle details", "Beginningoffreetime": "Beginning of free time", "Endoffreetime": "End of free time", "Dues": "Dues", "previousdues": "previous dues", "fromcity": "from city", "tocity": "to a city", "Numberofindividuals": "Number of individuals", "Findasite": "Find a site", "TheHome": "Home", "TheWork": "Work", "savedplaces": "Saved places", "Thenumberofseatsinthecar": "The number of seats in the car", "Notesaboutthetripandthecar": "Notes about the trip and the car", "Time": "Time", "message5": "The request has been confirmed successfully", "message10": "There are currently no new requests from passengers", "message11": "Leave for today", "message12": "Recommended pricing is subject to change", "Enterthearrivaltimeinminutes": "Enter the arrival time in minutes", "addressMessage": "Enter the name under which you want to save the address", "Intercityrequests": "Intercity requests", "Successfullycancelled": "Successfully cancelled", "Pleasechooseorwriteareasonforcancellation": "Please choose or write a reason for cancellation", "whatIsTheProblem": "What is the problem", "Giveyourratingtothedriver": "Give your rating to the driver", "endsIn": "Ends in", "IBAN": "IBAN number", "STC": "STC account number", "SerialNumber": "Serial Number", "pleaseCompleteData": "Please complete your data", "Therightletterofthepainting": "The right letter of the plate", "Theleftletterofthepainting": "The left letter of the plate", "Themiddleletterofthepainting": "The middle letter of the plate", "DailyRides": "Daily rides", "VIPRides": "VIP rides", "BetweenCities": "Between cities", "SavedAddresses": "Saved addresses", "MySubscriptions": "My subscriptions", "ReportVacation": "Report vacation", "TechnicalSupport": "technical support", "OtherServices": "Other services", "message13": "You can now reach your destination (work - school - home...) at the correct time with the delivery partnerships service", "Work": "Work", "Training": "Training", "Saturday": "Saturday", "Sunday": "Sunday", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Pleasecheckyourpersonalaccessories": "Please check your personal accessories before getting out of the car", "message14": "Are you sure you want to log out ?", "message20": "Are you sure you want to delete account ?", "message15": "The trip has not begun yet", "message16": "Lowest price is", "message17": "Not paid", "Doyouwanttoendthetrip": "Do you want to end the trip and collect the amount?", "Trippaymenthasbeenconfirmed": "Trip payment has been confirmed", "Paymentforthetriphasnotbeenconfirmed": "Payment for the trip has not been confirmed", "message18": "The price is less than the minimum", "message19": "The price is more than the minimum", "Totalrating": "Total rating", "Displaythedestinationtothecustomer": "Display the destination to the customer", "Showdestination": "Show destination", "CarBrand": "Car brand", "CarModel": "Car model", "Year": "Year", "CarColor": "Car color", "message21": "Wating for client to accept", "message22": "The application will be activated soon", "message23": "This area is outside the delivery range", "message24": "This trip is part of the 5 riyal offer", "totalProfit": "profit", "message25": "If you wish to add a return flight, please select Add a return flight and complete the data", "Chat": "Cha<PERSON>", "Chatt": "Cha<PERSON>", "Write": "Write", "oneWay": "One way", "roundTrip": "Round trip", "MultipleDestinations": "Multiple destinations", "GoingTrip": "Going trip", "ReturnTrip": "Return trip", "message26": "Package start date", "message27": "Package end date", "gifts": "gifts", "newOffer": "New offer", "Suitableofferswillbesentwithintwodays": "Suitable offers will be sent within two days.", "Therequesthasbeensentsuccessfully": "The request has been sent successfully", "messagePackage2": "The package price will be determined and a special offer will be sent according to your choices.", "NoAcceptedOfferYet": "No accepted offer yet", "bestValue": "Best value", "ThereIsNoOffersYet": "There are no offers yet"}}