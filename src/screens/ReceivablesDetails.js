import React, { Component, useEffect, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    ActivityIndicator,
    RefreshControl,
    FlatList,
    ScrollView
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    MediumGreen,
    appColor1,
    Grey,
    DarkYellow,
    DarkBlue,
} from '../components/Styles';
import { Button, DatePicker } from 'native-base';
import Header from '../components/Header';
import { strings } from './i18n';
import FooterDriver from '../components/FooterDriver';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import SelectDropdown from 'react-native-select-dropdown';
import DateTimePicker from 'react-native-modal-datetime-picker';
import * as Progress from 'react-native-progress';
import CircularProgress from 'react-native-circular-progress-indicator';
import { useRef } from 'react';
import StarRating from 'react-native-star-rating';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import * as walletActions from '../../Store/Actions/wallet';
import EarningContainer from '../components/EarningContainer';
import PortfolioContainer from '../components/PortfolioContainer';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RBSheet from 'react-native-raw-bottom-sheet';
import { TextInput } from 'react-native-gesture-handler';
import { Modalize } from 'react-native-modalize';
import ReceivablesDetailsContainer from '../components/ReceivablesDetailsContainer';



const ReceivablesDetails = props => {
    const dispatch = useDispatch();
    const [pendingPayouts, setPendingPayouts] = useState([{}]);
    const [paidPayouts, setpaidPayouts] = useState({});
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [loadingMore1, setLoadingMore1] = useState(false);
    const [page, setPage] = useState(1);
    const [lastPage, setLastPage] = useState(0);
    const [page1, setPage1] = useState(1);
    const [lastPage1, setLastPage1] = useState(0);
    const [total, setTotal] = useState(0);
    const [categories, setcategories] = useState([
        {
            id: 1,
            name: strings('lang.Shippingoperations')
        },
        {
            id: 2,
            name: strings('lang.Dues')
        },

    ]);
    const [categoryId, setCategoryId] = useState(1);
    const [noResault, setNoResault] = useState('');
    const [price, setPrice] = useState('');
    const [noResault1, setNoResault1] = useState('');
    const [Reload, setReload] = useState(false)
    const refRbSheet = useRef();
    const modalizeRef1 = useRef();
    const contentRef1 = useRef();

    console.log(I18nManager.isRTL);

    useEffect(() => {
        const getPayouts = async () => {
            setLoading(true)
            try {
                let response = await dispatch(walletActions.getPayouts(1));
                if (response.success == true) {
                    if (response.data.payouts.items == 0) {
                        setNoResault(strings('lang.No_Results'));
                        setPendingPayouts([])
                    }
                    else {
                        setNoResault('')
                        setPendingPayouts(response.data.payouts.items)
                        setLastPage(response.data.payouts.last_page)
                        setPage(1)
                    }
                    setLoading(false);

                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }


        };

        getPayouts();
    }, [Reload]);

    const onRefresh = async () => {
        setLoading(true)
        try {
            let response = await dispatch(walletActions.getPayouts(1));
            if (response.success == true) {
                if (response.data.payouts.items == 0) {
                    setNoResault(strings('lang.No_Results'));
                    setPendingPayouts([])
                }
                else {
                    setNoResault('')
                    setPendingPayouts(response.data.payouts.items)
                    setLastPage(response.data.payouts.last_page)
                    setPage(1)
                }
                setLoading(false);

            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const LoadMore = async () => {
        console.log('page', page);
        console.log('lastpage', lastPage);
        if (page < lastPage) {
            setLoadingMore(true)
            try {
                let response = await dispatch(walletActions.getPayouts(page + 1));
                if (response.success == true) {
                    setPendingPayouts([...pendingPayouts, ...response.data.payouts.items])
                    setPage(page + 1);
                    setNoResault('')
                    setLoadingMore(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingMore(false);
                }
            } catch (err) {
                setLoadingMore(false);
            }

        }
        else {
        }
    }

    if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Receivablesdetails')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />


                <View style={{ width: '100%', height: screenHeight / 1, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                    <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                        <View style={{ width: '100%', height: screenHeight / 1, backgroundColor: WhiteGery, }} />
                    </SkeletonPlaceholder>
                </View>

            </View>
        );

    } else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Receivablesdetails')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />
                {/* <ScrollView showsVerticalScrollIndicator={false} horizontal={false} > */}

                <View style={{
                    height: screenHeight / 6, width: '95%', alignSelf: 'center', flexDirection: 'row',
                    justifyContent: 'space-around', alignItems: 'center',
                }}>

                    <View style={styles.priceContainer}>
                        <Text
                            style={{
                                fontSize: screenWidth / 20,
                                fontFamily: appFontBold,
                                color: MediumGreen,
                            }}
                        >
                            {`${props.route.params.payouts.balance} ${strings('lang.SAR')}`}
                        </Text>
                        <Text
                            style={{
                                fontSize: screenWidth / 40,
                                fontFamily: appFontBold,
                                color: MediumGrey,

                            }}
                        >
                            {strings('lang.Totalbalance')}
                        </Text>
                    </View>

                    <View style={{ width: '.5%', height: '70%', backgroundColor: MediumGrey }}></View>


                    <View style={styles.priceContainer}>
                        <Text
                            style={{
                                fontSize: screenWidth / 20,
                                fontFamily: appFontBold,
                                color: Red,
                            }}
                        >
                            {`${props.route.params.payouts.commission} ${strings('lang.SAR')}`}
                        </Text>
                        <Text
                            style={{
                                fontSize: screenWidth / 40,
                                fontFamily: appFontBold,
                                color: MediumGrey,

                            }}
                        >
                            {strings('lang.Companycommission')}
                        </Text>
                    </View>

                </View>


                {/* <TouchableOpacity
                    onPress={() => { }}
                    style={{
                        width: '90%', marginVertical: 30,
                        height: screenHeight / 20, borderRadius: 25,
                        backgroundColor: DarkBlue, alignItems: 'center', alignSelf: 'center',
                        justifyContent: 'center'
                    }}
                >
                    <Text style={[styles.activeLabel, {
                        color: White, fontSize: screenWidth / 25
                    }]}>{strings('lang.Requestfordues')}</Text>
                </TouchableOpacity> */}


                {
                    noResault ? (
                        <Text
                            style={{
                                fontSize: screenWidth / 18,
                                fontFamily: appFontBold,
                                color: Red,
                                marginTop: screenHeight / 5,
                                alignSelf: 'center',
                            }}
                        >
                            {noResault}
                        </Text>
                    ) : (
                        <View></View>
                    )
                }

                <FlatList
                    data={pendingPayouts}
                    refreshControl={
                        <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                    }
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item, key }) =>
                        <ReceivablesDetailsContainer item={item} />
                    }
                    keyExtractor={item => item.id}
                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth / 1.05, height: screenHeight / 1.8 }}
                    onEndReachedThreshold={0.1}
                    onEndReached={() => LoadMore()}
                />
                {loadingMore ? <ActivityIndicator /> : <></>}


                {/* </ScrollView> */}





                <View style={{ height: screenHeight / 80 }}></View>
                <FooterDriver navigation={props.navigation} current={'myearnings'} />
            </View >
        );
    }
};

const styles = StyleSheet.create({

    container: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'space-evenly',
        width: '90%',
        height: screenHeight / 20,
        backgroundColor: WhiteGery,
        marginBottom: '5%'
    },
    rateContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'space-between',
        width: '90%',
        height: screenHeight / 7,
        marginBottom: '5%'
    },
    users: {
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'space-between',
        width: '90%',
        maxHeight: screenHeight / 7,
        marginVertical: '2%',
        borderWidth: 1,
        borderColor: MediumGrey,
        borderRadius: 5
    },
    priceContainer: {
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'center',
        width: '30%',

    },
    activeButton: { height: '95%', width: '40%', alignItems: "center", justifyContent: "center", alignSelf: 'center', borderBottomColor: DarkBlue, borderBottomWidth: 3, borderRadius: 2 },
    activeLabel: { fontFamily: appFontBold, fontSize: screenWidth / 32, color: DarkBlue, },
    Button: { height: '95%', width: '40%', alignItems: "center", justifyContent: "center", alignSelf: 'center', },
    label: { fontFamily: appFontBold, fontSize: screenWidth / 32, color: Black, },
});

export default ReceivablesDetails;
