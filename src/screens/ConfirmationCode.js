import React, { useState, useEffect, useRef } from 'react';
import { Image, View, Text, StyleSheet, I18nManager, BackHandler, ImageBackground } from "react-native";
import { screenHeight, DarkBlue, screenWidth, DarkGrey, White, appFont, appFontBold, DarkGreen, Red, Green, appColor2, appColor1, Red1, WhiteGery, Black, MediumGreen, MediumGrey, Gold, } from "../components/Styles";
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Input, Container, Toast, Button } from 'native-base';
import { ScrollView, TouchableOpacity, } from 'react-native-gesture-handler';
import { strings } from './i18n'
import I18n from 'react-native-i18n';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import CodeInput from 'react-native-confirmation-code-input';
import Header from '../components/Header';
import * as authActions from '../../Store/Actions/auth';
import Toaster from '../components/Toaster';
import Loading from '../components/Loading';
import messaging from '@react-native-firebase/messaging';
import * as generalActions from '../../Store/Actions/general';
import RNRestart from 'react-native-restart';

import {
    CodeField,
    Cursor,
    useBlurOnFulfill,
    useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import { useDispatch } from 'react-redux';
import { io } from "socket.io-client";
import { connectSocket } from '../../Store/Actions/socket';
const socket = io('http://65.108.33.237', { transports: ['websocket', 'polling'] });

const CELL_COUNT = 4;


const ConfirmationCode = (props) => {
    const [seconds, setSeconds] = useState(60)
    const [codeNum, setCode] = useState('')
    const [loading, setLoading] = useState(false)
    const [mins, setMins] = useState(2)
    const [secs, setSecs] = useState(0)
    const [Reload, setReload] = useState(false)
    const dispatch = useDispatch();

    const ref = useBlurOnFulfill({ codeNum, cellCount: CELL_COUNT });
    const [propss, getCellOnLayoutHandler] = useClearByFocusCell({
        codeNum,
        setCode,
    });
    const timerRef = useRef()

    function wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    useEffect(() => {


        const timerId = setInterval(() => {
            if (secs <= 0) {
                if (mins <= 0) {

                }
                else {
                    setMins(m => m - 1)
                    setSecs(59)
                }
            }
            else setSecs(s => s - 1)
        }, 1000)
        return () => clearInterval(timerId);
    }, [secs, mins, Reload])

    const verify = async (code) => {
        let data = props.route.params.data
        let verify = null;
        setLoading(true)
        if (props.route.params.userType == '2') {
            verify = await dispatch(authActions.verifyDriver(data.id, code));
        }
        else {
            verify = await dispatch(authActions.verify(data.id, code));
        }
        console.log('verify', verify);
        if (verify.success == true) {
            if (props.route.params.userType == '1' && !data.profile_completed) {
                setLoading(false)
                Toaster(
                    'top',
                    'success',
                    'green',
                    (strings('lang.Login_Successfuly')),
                    White,
                    1500,
                    screenHeight / 15,
                );
                props.navigation.push('Registration', { data: verify.data, userType: props.route.params.userType })
                await AsyncStorage.setItem("token", JSON.stringify(verify.data.token));
            }
            else {
                Toaster(
                    'top',
                    'success',
                    'green',
                    (strings('lang.Login_Successfuly')),
                    White,
                    1500,
                    screenHeight / 15,
                );
                await AsyncStorage.setItem("token", JSON.stringify(verify.data.token));
                await AsyncStorage.setItem("userType", props.route.params.userType);
                if (props.route.params.userType == '2') {
                    await AsyncStorage.setItem("id", JSON.stringify(verify.data.driver.id));
                    await AsyncStorage.setItem("name", verify.data.driver.name ? verify.data.driver.name : '');
                    await AsyncStorage.setItem("email", verify.data.driver.email ? verify.data.driver.email : '');
                    await AsyncStorage.setItem("image", verify.data.driver.image ? verify.data.driver.image : '');
                    await AsyncStorage.setItem("dial_code", verify.data.driver.dial_code ? verify.data.driver.dial_code : '');
                    await AsyncStorage.setItem("phone", verify.data.driver.phone ? verify.data.driver.phone : '');
                    await AsyncStorage.setItem("gender", verify.data.driver.gender ? verify.data.driver.gender : '');

                    await messaging().registerDeviceForRemoteMessages();
                    const FcmToken = await messaging().getToken();
                    console.log('getFcmToken', FcmToken);
                    await AsyncStorage.setItem("FcmToken", FcmToken ? FcmToken : '');
                    let addFcmToken = await dispatch(generalActions.addFcmToken(FcmToken));
                    console.log('addFcmToken', addFcmToken);
                    setLoading(false)
                    dispatch(connectSocket(JSON.stringify(verify.data.driver.id), '2'));

                    // RNRestart.Restart();

                    props.navigation.push('DriverRequests')

                }
                else {
                    await AsyncStorage.setItem("id", JSON.stringify(verify.data.user.id));
                    await AsyncStorage.setItem("name", verify.data.user.name ? verify.data.user.name : '');
                    await AsyncStorage.setItem("email", verify.data.user.email ? verify.data.user.email : '');
                    await AsyncStorage.setItem("image", verify.data.user.image ? verify.data.user.image : '');
                    await AsyncStorage.setItem("dial_code", verify.data.user.dial_code ? verify.data.user.dial_code : '');
                    await AsyncStorage.setItem("phone", verify.data.user.phone ? verify.data.user.phone : '');
                    await AsyncStorage.setItem("gender", verify.data.user.gender ? verify.data.user.gender : '');

                    await messaging().registerDeviceForRemoteMessages();
                    const FcmToken = await messaging().getToken();
                    console.log('getFcmToken', FcmToken);
                    await AsyncStorage.setItem("FcmToken", FcmToken ? FcmToken : '');
                    let addFcmToken = await dispatch(generalActions.addFcmToken(FcmToken));
                    console.log('addFcmToken', addFcmToken);
                    dispatch(connectSocket(JSON.stringify(verify.data.user.id), '1'));

                    // RNRestart.Restart();
                    setLoading(false)
                    props.navigation.push('Home')

                }
            };
        }
        else {
            setLoading(false)
            Toaster(
                'top',
                'danger',
                Red,
                verify.message,
                White,
                1500,
                screenHeight / 15,
            );
        };
    };

    const resendVerificationCode = async () => {
        let data = props.route.params.data

        setLoading(true)
        let resendVerificationCode = null;
        if (props.route.params.userType == '2') {
            resendVerificationCode = await dispatch(authActions.resendVerificationCodeDriver(data.id))
        }
        else {
            resendVerificationCode = await dispatch(authActions.resendVerificationCode(data.id))
        }
        console.log('resendVerificationCode', resendVerificationCode);
        if (resendVerificationCode.success == true) {
            setLoading(false)
            Toaster(
                'top',
                'success',
                'green',
                (strings('lang.CodeSentSuccessfully')),
                White,
                1500,
                screenHeight / 15,
            );
        }
        else {
            setLoading(false)
            Toaster(
                'top',
                'danger',
                Red,
                resendVerificationCode.message,
                White,
                1500,
                screenHeight / 15,
            );
        };
    };



    return (
        <Container>
            {loading ? <Loading /> : <></>}
            <Header title={strings('lang.verificationcode')} backPress={() => { props.navigation.goBack() }} background />

            <KeyboardAwareScrollView showsVerticalScrollIndicator={false} style={{}}>
                <View style={{ alignSelf: 'center', width: '100%', height: screenHeight / 1.08, borderRadius: 20, backgroundColor: WhiteGery, paddingVertical: '25%', justifyContent: 'space-between' }}>
                    <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 2.5, alignItems: 'center', justifyContent: 'space-between', zIndex: 100, backgroundColor: White, borderWidth: .5, borderColor: MediumGrey, borderRadius: 25, paddingVertical: '15%' }}>
                        <Text numberOfLines={2} style={styles.text1}>{strings('lang.Pleaseentertheverificationcodesenttoyouconsistingof4digits')}</Text>
                        {/* <CodeInput
                            placeholder=''
                            activeColor={Black}
                            inactiveColor={DarkGrey}
                            className={'border-box'}
                            autoFocus={true}
                            ignoreCase={true}
                            inputPosition='center'
                            onFulfill={(codeFulFil) => { verify(codeFulFil) }}
                            onChangeText={(text) => { setCode(text) }}
                            space={10}
                            codeLength={4}
                            size={screenWidth / 6}
                            textAlign="center"
                            containerStyle={{ flexDirection: I18nManager.isRTL == 'ar' ? 'row-reverse' : 'row', }}
                            codeInputStyle={{ borderRadius: 140, backgroundColor: White, borderColor: "#ddd" }}
                            // style={{ fontFamily: appFontBold, color: Black }}
                            keyboardType={'number-pad'}
                        /> */}

                        <CodeField
                            ref={ref}
                            // Use `caretHidden={false}` when users can't paste a text value, because context menu doesn't appear
                            value={codeNum}
                            onChangeText={setCode}
                            cellCount={CELL_COUNT}
                            rootStyle={styles.codeFieldRoot}
                            keyboardType="number-pad"
                            textContentType="oneTimeCode"
                            renderCell={({ index, symbol, isFocused }) => (
                                <Text
                                    key={index}
                                    style={[styles.cell, isFocused && styles.focusCell]}
                                    onLayout={getCellOnLayoutHandler(index)}>
                                    {symbol || (isFocused ? <Cursor /> : null)}
                                </Text>
                            )}
                        />

                        <Button onPress={() => { verify(codeNum) }} style={styles.buttonContainer}>
                            <Text style={styles.buttonText}>{strings('lang.verification')}</Text>
                        </Button>
                    </View>


                    <View style={{ width: '95%', alignItems: 'flex-start', justifyContent: 'center', alignSelf: 'center' }}>
                        <Text style={styles.text2}>{strings('lang.Resubmitafter')}</Text>
                        <Text style={{ color: Black, fontFamily: appFontBold, textAlign: "center", fontSize: screenWidth / 10, marginBottom: 10 }}>{mins}:{secs < 10 && 0}{secs}</Text>
                        {/* <TouchableOpacity style={{ marginBottom: 50 }} disabled={mins == 0 && secs == 0 ? false : true} onPress={() => { setMins(0); setSecs(3); setReload(true) }}>
                                <Text style={{ color: mins == 0 && secs == 0 ? Red1 : DarkGrey, textDecorationLine: mins == 0 && secs == 0 ? 'none' : 'line-through', fontFamily: appFont, textAlign: "center", marginHorizontal: 10 }}>{strings('lang.Resendcode')}</Text>
                            </TouchableOpacity> */}
                        <Button onPress={() => { setMins(2); setSecs(0); setReload(true), resendVerificationCode() }}
                            disabled={mins == 0 && secs == 0 ? false : true}
                            style={[styles.buttonContainer, { backgroundColor: Gold, opacity: mins == 0 && secs == 0 ? 1 : 0.5 }]}>
                            <Text style={styles.buttonText}>{strings('lang.Resendcode')}</Text>
                        </Button>
                    </View>
                </View>




            </KeyboardAwareScrollView>

        </Container >
    )
}

export default ConfirmationCode;
const styles = StyleSheet.create({
    text1: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 25,
        textAlign: 'center',
        alignSelf: "center",
        color: Black,
        width: '90%',
        // marginBottom: '10%'
    },
    text2: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 25,
        marginTop: '10%',
        alignSelf: "flex-start",
        color: Black,
    },

    buttonContainer: {
        backgroundColor: DarkBlue,
        width: '95%',
        height: screenHeight / 18,
        marginTop: screenHeight / 50,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 100,
    },
    codeFieldRoot: { marginTop: 20, width: '90%', flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center' },
    focusCell: {
        borderColor: DarkBlue,
    },
    cell: {
        width: screenWidth / 6,
        height: screenWidth / 6,
        borderRadius: screenWidth / 12, borderColor: "#ddd", fontSize: screenWidth / 16, textAlign: 'center',
        lineHeight: screenWidth / 6.5,
        borderWidth: 2,
        borderColor: '#00000030',
        textAlign: 'center', textAlignVertical: 'center', color: Black
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    buttonText1: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },

});