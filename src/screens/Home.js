import React, { useEffect, useRef, useState } from 'react';
import { BackHandler, I18nManager, Image, Platform, StyleSheet, Text, View } from "react-native";
import { TouchableOpacity } from 'react-native-gesture-handler';
import Footer from '../components/Footer';
import HomeHeader from '../components/HomeHeader';
import { appFont, appFontBold, Black, DarkBlue, DarkYellow, MediumGrey, Red, screenHeight, screenWidth, White, WhiteGery } from '../components/Styles';
import { strings } from './i18n';

import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useDispatch, useSelector } from 'react-redux';

import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import { Button } from 'native-base';
import { useCallback } from 'react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Carousel, { <PERSON><PERSON><PERSON>, ParallaxImage } from 'react-native-snap-carousel';
import * as authActions from '../../Store/Actions/auth';
import * as profileActions from '../../Store/Actions/profile';



const Home = props => {
    const [categories, setcategories] = useState([
        {
            id: 9,
            name: strings('lang.gifts'),
            uri: require('../images/giftt.png'),
            screen: 'Prizes'
        },
        {
            id: 0,
            name: strings('lang.DailyRides'),
            uri: require('../images/Group10582.png'),
            screen: 'MapAddress'
        },
        {
            id: 1,
            name: strings('lang.VIPRides'),
            uri: require('../images/Group10577.png'),
            screen: 'OrderVip'
        },
        {
            id: 2,
            name: strings('lang.BetweenCities'),
            uri: require('../images/Group10556.png'),
            screen: 'OrderCities'
        },
        {
            id: 3,
            name: strings('lang.Packages'),
            uri: require('../images/Group107.png'),
            screen: 'DeliveryCompanies'
        },
        {
            id: 4,
            name: strings('lang.SavedAddresses'),
            uri: require('../images/Group10554.png'),
            screen: 'Destenation'
        },
        {
            id: 5,
            name: strings('lang.MySubscriptions'),
            uri: require('../images/Group10640.png'),
            screen: 'Packages'
        },
        {
            id: 6,
            name: strings('lang.ReportVacation'),
            uri: require('../images/Group10600.png'),
            screen: 'Packages'
        },
        {
            id: 7,
            name: strings('lang.TechnicalSupport'),
            uri: require('../images/Group10579.png'),
            screen: 'ContactUs'
        },
        {
            id: 8,
            name: strings('lang.OtherServices'),
            uri: require('../images/Group10568.png'),
            screen: 'More'
        },

    ]);
    const [entries, setEntries] = useState([require('../images/Group10641.png'), require('../images/Group10641.png')]);
    const ref = useRef(null);
    const [activeSlide, setActiveSlide] = useState('');

    const [categoryId, setCategoryId] = useState(0);
    const [result, setResult] = useState('');
    const [name, setName] = useState('');
    const [gender, setGender] = useState('');
    const [image, setImage] = useState('');
    const [loading, setLoading] = useState(false);
    const [prezPress, setPrezPress] = useState(false);
    const [reload, setReload] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();
    const isFocused = useIsFocused();
    const slider_images = useSelector(state => state.general.slider_images);
    const is_live = useSelector(state => state.general.is_live);

    const renderItem = useCallback(({ item, index }, parallaxProps) => {
        return (
            <ParallaxImage
                // source={item}
                source={{ uri: item.image }}
                containerStyle={styles.imageContainer}
                style={styles.image}
                parallaxFactor={0}
                {...parallaxProps}
            />
        );
    }, []);

    const handleBackButton = () => {
        BackHandler.exitApp();
        return true;
    };

    useEffect(() => {
        BackHandler.addEventListener('hardwareBackPress', handleBackButton);

        return () => {
            BackHandler.removeEventListener('hardwareBackPress', handleBackButton);
        };
    }, []);

    useEffect(() => {

        const getData = async () => {
            setLoading(true);
            // let name = await AsyncStorage.getItem('name')
            // let image = await AsyncStorage.getItem('image')
            let gender = await AsyncStorage.getItem('gender')
            console.log('gender', gender);
            setGender(gender);
            // setName(name);
            // setImage(image);
            setLoading(false);
        }
        getData()
        const getProfile = async () => {
            setLoading(true)
            try {
                let response = await dispatch(authActions.getProfile());
                if (response.success == true) {
                    // if (response.data.user_prize == null) {
                    //     props.navigation.navigate('Prizes')
                    // } else {

                    // }
                }
                else {
                    if (response.message) {
                        // Toaster(
                        //     'top',
                        //     'danger',
                        //     Red,
                        //     response.message,
                        //     White,
                        //     1500,
                        //     screenHeight / 15,
                        // );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getNotPaidTripsCount = async () => {
            // setLoading(true)
            try {
                let response = await dispatch(profileActions.getNotPaidTripsCount());
                if (response.success == true) {

                }
                else {
                    if (response.message) {
                        // Toaster(
                        //     'top',
                        //     'danger',
                        //     Red,
                        //     response.message,
                        //     White,
                        //     1500,
                        //     screenHeight / 15,
                        // );
                    }
                }
                // setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                // setLoading(false);
            }
        };


        // getData()
        getProfile();
        getNotPaidTripsCount()

    }, [isFocused]);

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    useEffect(() => {
        const checkIfFirstLaunch = async () => {
            try {
                const hasLaunched = await AsyncStorage.getItem('hasLaunched');
                if (!hasLaunched) {
                    setPrezPress(true)
                    // props.navigation.navigate('Prizes')
                    await AsyncStorage.setItem('hasLaunched', 'true');
                }
            } catch (error) {
                console.error('Failed to check first launch status:', error);
            }
        };

        checkIfFirstLaunch();
    }, []);

    if (!is_live) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White, justifyContent: 'center' }}>
                <Text style={{ fontFamily: appFontBold, color: Red, fontSize: screenWidth / 16 }}>{strings('lang.message22')}</Text>
                <Image source={require('../images/soonn.gif')} style={{ height: screenHeight / 4, resizeMode: 'contain', marginTop: 10 }} />

                {Platform.OS != 'ios'
                    ?
                    <Button
                        onPress={() => {
                            if (Platform.OS == 'ios') {
                            }
                            else {
                                handleBackButton()

                            }
                        }}
                        transparent style={{ marginTop: 20, borderRadius: 10, alignSelf: 'center', width: '90%', height: screenHeight / 18, backgroundColor: DarkBlue, justifyContent: "center", alignItems: "center" }}>
                        <Text style={{ fontFamily: appFontBold, color: White, fontSize: screenWidth / 30 }}>{strings('lang.CloseApp')}</Text>
                    </Button>
                    :
                    <></>
                }

            </View>
        );
    }
    else if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <HomeHeader notificationPress={() => props.navigation.navigate('Notifications')} />

                <View style={{ width: '100%', height: screenHeight / 1, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                    <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                        <View style={{ width: '100%', height: screenHeight / 1, backgroundColor: WhiteGery, }} />
                    </SkeletonPlaceholder>
                </View>
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <HomeHeader
                    notificationPress={() => props.navigation.navigate('Notifications')}
                    name={name} image={image} />

                <View
                    style={{
                        alignSelf: 'center',
                        justifyContent: 'center',
                        height: screenHeight / 4.2,
                        width: '90%',
                        marginVertical: '2%',
                        // backgroundColor: Red,
                        borderRadius: 20,
                        borderWidth: 1,
                        borderColor: MediumGrey,
                        overflow: 'hidden'
                    }}
                >
                    <Carousel
                        autoplay={true}
                        loop={true}
                        layout={'default'}
                        containerStyle={{
                            justifyContent: 'center',
                            alignSelf: 'center',
                            overflow: 'hidden',
                            borderWidth: 1,

                        }}
                        ref={ref}
                        data={slider_images}
                        // data={entries}
                        renderItem={renderItem}
                        sliderWidth={screenWidth}
                        itemWidth={screenWidth * 1}
                        hasParallaxImages={true}
                        onSnapToItem={index => setActiveSlide(index)}
                        autoplayInterval={6000}
                    />
                    <Pagination
                        dotsLength={entries.length}
                        activeDotIndex={activeSlide}
                        containerStyle={{ position: 'absolute', bottom: 0, }}
                        dotStyle={{
                            width: 10,
                            height: 2,
                            borderRadius: 0,
                            marginHorizontal: 4,
                            backgroundColor: White
                        }}
                        inactiveDotStyle={{
                            width: 10,
                            height: 2,
                            borderRadius: 0,
                            marginHorizontal: 4,
                            backgroundColor: MediumGrey
                        }}
                        inactiveDotOpacity={0.4}
                        inactiveDotScale={0.6}
                    />
                </View>

                {gender == 'female' ?
                    <TouchableOpacity
                        onPress={() => { props.navigation.push('MapAddress', { cityTrip: false, screen: 'Home', pending: false, pendingTrip: null }) }}
                        style={{
                            width: '95%',
                            height: screenHeight / 14,
                            backgroundColor: DarkBlue,
                            paddingStart: 10,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            borderRadius: 25,
                            overflow: 'hidden',
                            alignSelf: 'center'
                        }}>
                        <View style={{
                            width: '80%',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            <Text numberOfLines={2} style={{
                                color: White,
                                fontFamily: appFontBold,
                                fontSize: screenWidth / 32,
                                lineHeight: screenHeight / 40,
                                textAlign: I18nManager.isRTL && 'left'
                            }}>{strings('lang.orderFemale')}</Text>
                        </View>

                        <View style={{
                            width: '20%',
                            alignItems: 'flex-end',
                            transform: [{ rotate: I18nManager.isRTL ? '180deg' : '0deg' }]
                        }}>
                            <Image source={require('../images/Back.png')} style={{ width: '100%', resizeMode: 'contain', tintColor: White }} />
                        </View>

                    </TouchableOpacity>
                    :
                    <></>
                }


                <Text style={styles.label}>{strings('lang.Ourservices')}</Text>
                <KeyboardAwareScrollView
                    style={{ width: '100%', }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.container}>
                        {categories.map((item) => {
                            return <TouchableOpacity style={styles.categoryContainer}
                                // onPress={() => { props.navigation.push('Prizes') }}
                                onPress={() => {
                                    // if (prezPress) {
                                    //     props.navigation.navigate('Prizes')
                                    // } else {
                                    props.navigation.push(item.screen, {
                                        cityTrip: item.id == 2 ? true : false,
                                        screen: 'Home', pending: false, pendingTrip: null,
                                    },);
                                    // }
                                }}
                            >
                                <View style={styles.iconContainer}>
                                    <Image source={item.uri} style={{ width: item.id == 9 ? '50%' : '75%', resizeMode: 'contain', tintColor: item.id == 9 && DarkYellow }} />
                                </View>
                                <Text style={styles.text}>{item.name}</Text>
                            </TouchableOpacity>
                        })}

                    </View>
                </KeyboardAwareScrollView>

                {/* <View style={{ height: screenHeight / 7 }}></View> */}
                <Footer current={'Home'} navigation={props.navigation} />
            </View>
        )
    }
};

export default Home;
const styles = StyleSheet.create({
    container: {
        width: '90%',
        alignSelf: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        overflow: 'hidden'
    },
    categoryContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        height: screenHeight / 7,
        width: screenWidth / 4,
        marginBottom: screenHeight / 80
    },
    iconContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        height: screenWidth / 4.6,
        width: screenWidth / 4.6,
        borderRadius: screenWidth / 8,
        backgroundColor: WhiteGery,
    },
    imageContainer: {
        width: '90%',
        height: screenHeight / 4.2,
        marginBottom: Platform.select({ ios: 0, android: 1 }), // Prevent a random Android rendering issue
        backgroundColor: White,
        borderRadius: 0,
        justifyContent: 'center',
        borderWidth: 0.8,
        borderColor: MediumGrey,
        overflow: 'hidden'
    },
    image: {
        width: '100%',
        height: '100%',
        resizeMode: 'cover'
    },
    label: { fontFamily: appFontBold, fontSize: screenWidth / 26, color: Black, alignSelf: 'flex-start', marginStart: '5%' },
    text: { fontFamily: appFont, fontSize: screenWidth / 44, color: Black, },
    icon: { width: '5%', height: '40%', tintColor: MediumGrey, resizeMode: 'contain', marginHorizontal: '7%' },
});