import {
    Image,
    View,
    Text,
    StyleSheet,
    I18nManager,
    BackHandler,
    TouchableOpacity,
    Keyboard,
    Platform,
    ImageBackground,
    Linking,
} from 'react-native';
import React, { Component, useEffect, useState } from 'react';
import {
    screenHeight,
    DarkBlue,
    screenWidth,
    DarkGrey,
    White,
    appFont,
    WhiteGery,
    appFontMedium,
    MediumGrey,
    appFontBold,
    DarkGreen,
    Red,
    Green,
    appColor2,
    appColor1,
    Black,
    WhiteGreen,
    DarkYellow,
    MediumGreen,
    Gold,
} from '../components/Styles';
import { Input, Container, Button, Toast } from 'native-base';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { TextInput } from 'react-native-gesture-handler';
import { strings } from './i18n';

import * as authActions from '../../Store/Actions/auth';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Loading from '../components/Loading';
import Header from '../components/Header';

const Login1 = props => {
    const [phone, setPhone] = useState('');
    const [code, setCode] = useState('+966');
    const [activeText, setActiveText] = useState(0);
    const [showPassword, setShowPassword] = useState(false);
    const [loading, setLoading] = useState(false);
    const [phoneError, setPhoneError] = useState('');
    const [passwordError, setPasswordError] = useState('');
    const [conditions, setconditions] = useState(false)

    const dispatch = useDispatch();

    useEffect(() => {

    }, [])

    const handleSubmit = async () => {
        if (!phone) {
            // if (!phone) {
            setPhoneError(strings('lang.Please_insert_field'));
            // }
            // else if (phone.length != 10) {
            //     setPhoneError(strings('lang.Phone_must_be_10_characters'));
            // }
            // else if (!phone.match(/^05/)) {
            //     setPhoneError(strings('lang.validationPhone'))
            // }
        }
        else if (conditions == false) {
            Toast.show({
                position: "top", type: "danger",
                text: (strings('lang.termsConditions')), textStyle: { color: "white", textAlign: 'center', fontFamily: appFont },
                style: { width: "75%", alignSelf: "center", opacity: .8, borderRadius: 10, backgroundColor: "red" },
                duration: 3000
            })
        }
        else {
            let inputString = phone;
            if (inputString.startsWith("0")) {
                inputString = inputString.slice("0".length);
            }
            console.log('inputString', inputString);
            setLoading(true)
            let Login = null;
            if (props.route.params.userType == '2') {
                Login = await dispatch(authActions.loginDriver(code, inputString));
            }
            else {
                Login = await dispatch(authActions.login(code, inputString));
            }
            console.log('LoginResponse', Login);
            if (Login.success == true) {
                setLoading(false)
                Toaster(
                    'top',
                    'success',
                    'green',
                    (strings('lang.Login_Successfuly')),
                    White,
                    1500,
                    screenHeight / 15,
                    props.navigation.push('ConfirmationCode', { data: Login.data, userType: props.route.params.userType })
                );
            }
            else {
                setLoading(false)
                Toaster(
                    'top',
                    'danger',
                    Red,
                    Login.message,
                    White,
                    1500,
                    screenHeight / 15,
                );
            }
        }
    };

    return (
        <Container>
            {loading ? <Loading /> : <View></View>}

            <ImageBackground
                source={require('../images/backgroundLogin.jpg')} // Replace with your image source
                style={styles.backgroundImage}
            >

                <Header title={strings('lang.Login')} backPress={() => { props.navigation.goBack() }} lohin />

                <KeyboardAwareScrollView showsVerticalScrollIndicator={false} style={{ width: screenWidth, height: screenHeight }}>

                    <View style={styles.containerv}>

                        <View style={styles.innerView}>

                            {/* <KeyboardAwareScrollView style={{}}> */}

                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 6,
                                    alignSelf: 'center',
                                    justifyContent: 'center',
                                    marginTop: '5%'
                                }}
                            >
                                <Image
                                    source={require('../images/logoStep.png')}
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        resizeMode: 'contain',
                                        alignSelf: 'center',
                                    }}
                                />
                            </View>
                            <Text style={{ color: DarkBlue, marginVertical: screenHeight / 15, fontFamily: appFontBold, alignSelf: 'center', fontSize: screenWidth / 18 }}>{strings('lang.Please_insert_phone')}</Text>
                            <View style={styles.input}>

                                <TextInput
                                    placeholderTextColor={'#707070'}
                                    placeholder={'5XXXXXXXX'}
                                    style={styles.inputText}
                                    keyboardType="number-pad"
                                    value={phone}
                                    onChangeText={text => {
                                        setPhone(text);
                                        setPhoneError('');
                                    }}
                                // onFocus={() => setActiveText(1)}
                                // onBlur={() => {
                                //     setActiveText(0);
                                // }}
                                />
                            </View>
                            {phoneError ?
                                <Text style={styles.loginError}>{phoneError}</Text>
                                :
                                <View style={{ height: 0 }}></View>
                            }

                            <View style={{ flexDirection: 'row', width: '90%', alignSelf: "center", alignItems: 'center', paddingStart: '0%', }}>
                                <View style={styles.checkboxcontainer}>
                                    {conditions == true ?
                                        <Button onPress={() => setconditions(false)} style={styles.activecheckbox}>
                                            <Image style={{ width: '150%', height: '150%', resizeMode: 'contain', tintColor: White }} source={require('../images/true.png')} />
                                        </Button>
                                        :
                                        <Button style={styles.checkbox} onPress={() => setconditions(true)}>
                                        </Button>
                                    }
                                </View>
                                <TouchableOpacity onPress={() => { props.navigation.navigate('TermsAndConditions') }} style={{ marginStart: '2%' }}>
                                    <Text style={{ fontFamily: appFontBold, color: DarkBlue, fontSize: screenWidth / 24, textDecorationLine: 'underline', alignSelf: 'flex-start' }}>
                                        {strings('lang.terms_and_conditions')}
                                    </Text>
                                </TouchableOpacity>
                            </View>


                            <Button onPress={() => { handleSubmit() }} style={styles.buttonContainer}>
                                <Text style={styles.buttonText}>{strings('lang.Login')}</Text>
                            </Button>

                            {props.route.params.userType == '2'
                                ?
                                <View style={styles.registerationContainer}>
                                    <Text style={styles.registerationText}>
                                        {strings('lang.Donthaveanaccountpleasedo')}
                                    </Text>
                                    <TouchableOpacity
                                        onPress={() => {
                                            Linking.openURL('https://step-db.com/driver/register').catch(err => console.error('An error occurred', err));
                                        }}
                                    >
                                        <Text style={styles.registerationText1}>
                                            {strings('lang.Register')}
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                                :
                                <></>
                            }

                            {/* </KeyboardAwareScrollView> */}


                        </View>
                    </View>
                    {/* <View style={{ height: screenHeight, width: '100%', alignItems: 'center', justifyContent: 'center', }}>

                    <Header title={strings('lang.Login')} backPress={() => { props.navigation.goBack() }} background />
                    <KeyboardAwareScrollView style={{}}>
                        {loading ? <Loading /> : <View></View>}

                        <View
                            style={{
                                width: '100%',
                                height: screenHeight / 4.5,
                                alignSelf: 'center',
                                justifyContent: 'center',
                                marginTop: screenHeight / 8,
                            }}
                        >
                            <Image
                                source={require('../images/Group10544.png')}
                                style={{
                                    width: '100%',
                                    height: '100%',
                                    resizeMode: 'contain',
                                    alignSelf: 'center',
                                }}
                            />
                        </View>
                        <Text style={{ color: Gold, marginTop: screenHeight / 10, marginBottom: screenHeight / 20, fontFamily: appFontBold, alignSelf: 'center', fontSize: screenWidth / 22 }}>{strings('lang.Please_insert_phone')}</Text>
                        <View style={styles.input}>
                           
                            <TextInput
                                placeholderTextColor={'#707070'}
                                placeholder={'5XXXXXXXX'}
                                style={styles.inputText}
                                keyboardType="number-pad"
                                value={phone}
                                onChangeText={text => {
                                    setPhone(text);
                                    setPhoneError('');
                                }}
                            // onFocus={() => setActiveText(1)}
                            // onBlur={() => {
                            //     setActiveText(0);
                            // }}
                            />
                        </View>
                        {phoneError ?
                            <Text style={styles.loginError}>{phoneError}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        }


                        <Button onPress={() => { handleSubmit() }} style={styles.buttonContainer}>
                            <Text style={styles.buttonText}>{strings('lang.Login')}</Text>
                        </Button>
                    </KeyboardAwareScrollView>


                    <View style={{ height: screenHeight / 15 }}></View>
                </View> */}
                </KeyboardAwareScrollView>
            </ImageBackground>
        </Container>
    );
};
const styles = StyleSheet.create({
    registerationContainer: {
        flexDirection: 'row',
        marginVertical: screenHeight / 50
    },
    registerationText: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 25,
        color: Black,
    },
    registerationText1: {
        color: DarkYellow,
        marginHorizontal: 5,
        fontFamily: appFontBold,
        fontSize: screenWidth / 25,
    },
    backgroundImage: {
        flex: 1,
        resizeMode: 'cover', // or 'stretch' or 'contain'
        justifyContent: 'center',
        alignItems: 'center'
    },
    containerv: {
        flex: 1,
        padding: 20,
        justifyContent: 'center',
        alignItems: 'center', marginTop: screenHeight / 12
    },
    innerView: {
        width: screenWidth / 1.1,
        height: screenHeight / 1.5,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'white', // Adjust the color of the inner view as needed
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },

    buttonContainer: {
        backgroundColor: DarkBlue,
        width: '90%',
        height: screenHeight / 18,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
        borderRadius: 25,
        marginTop: '3%',

    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
    },


    container: {
        flex: 1,
        alignItems: 'center',
        height: screenHeight,
    },
    input: {
        height: screenHeight / 18,
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '95%',
        paddingHorizontal: 10,

        alignSelf: 'center',
    },

    inputText: {
        color: Black,
        width: '100%',
        fontFamily: appFontBold,
        fontSize: screenWidth / 30,
        alignSelf: 'center',
        textAlign: Platform.OS ? I18nManager.isRTL ? 'right' : 'left' : I18nManager.isRTL ? 'left' : 'right',
        borderWidth: 1,
        borderColor: MediumGrey,
        // borderTopRightRadius: I18nManager.isRTL ? 0 : 100,
        // borderBottomRightRadius: I18nManager.isRTL ? 0 : 100,

        borderRadius: 20,
        height: '100%', paddingHorizontal: 10
    },
    loginError: {
        color: 'red', fontFamily: appFont, alignSelf: 'flex-start', fontSize: screenWidth / 35, marginStart: '10%'
    },
    loginOptionContainer: { width: screenWidth / 4, height: screenHeight / 18, backgroundColor: WhiteGery, borderRadius: 10, alignItems: 'center', justifyContent: 'center' },
    loginOptionImage: { width: '60%', height: '60%', resizeMode: 'contain' },
    activecheckbox: {
        backgroundColor: DarkBlue,
        width: 20,
        height: 20,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 0,
        alignSelf: 'center',
    },
    checkbox: {
        backgroundColor: White,
        width: 20,
        height: 20,
        borderRadius: 5,
        borderColor: MediumGrey,
        borderWidth: 1,
        elevation: 0,
        alignSelf: 'center'
    },
    checkboxcontainer: {
        flexDirection: 'row',
        alignSelf: 'center',
        alignItems: 'center',
        marginBottom: 5,
        height: 35,
    },
});

export default Login1;
