import React, { useEffect, useState } from 'react';
import { View, Image, Text, I18nManager, Platform, Linking } from "react-native";
import { appFont, screenWidth, DarkGreen, WhiteGreen, screenHeight, White, appFontBold, Red, MediumGrey, DarkBlue, } from '../components/Styles';
import { Button } from 'native-base';
import { strings } from '../screens/i18n';

const RequiredUpadte = props => {

  // const appStatus = props.route.params.appStatus;


  // const [applicationStatus, setApplicationStatus] = useState({});

  useEffect(() => {
    // setApplicationStatus(appStatus)
    // console.log(appStatus)
  }, []);


  const update = () => {
    if (Platform.OS == 'ios') {
      Linking.openURL('https://apps.apple.com/eg/app/step-%D8%B3%D8%AA%D8%A8/id6477336440')
    }
    else if (Platform.OS == 'android') {
      Linking.openURL('https://play.google.com/store/apps/details?id=com.StepAll&hl=en&gl=US')
    }
    else {
      // Linking.openURL('https://appgallery.huawei.com/app/C105619521')
    }
  }


  return (
    <View style={{ flex: 1, alignItems: 'center', backgroundColor: White, paddingTop: screenHeight / 10 }}>

      {/* {applicationStatus.IsUpdateRequired == true
        ? */}
      <Image source={require('../images/updatee.png')} style={{ width: '50%', height: screenHeight / 2.2, tintColor: DarkBlue, alignSelf: 'center', resizeMode: 'contain', marginTop: '0%' }} />
      {/* :
        <Image source={require('../images/maintance.png')} style={{ width: '40%', height: screenHeight / 2.2, tintColor: Red, alignSelf: 'center', resizeMode: 'contain', marginTop: '35%' }} />
      } */}

      {/* {applicationStatus.IsUpdateRequired == true
        ? */}
      <Text style={{ textAlign: "center", fontFamily: appFontBold, color: DarkBlue, fontSize: screenWidth / 20 }}>{strings('lang.ThereIsAnUpdate')}</Text>
      {/* :
        <Text style={{ textAlign: "center", fontFamily: appFontBold, color: Red, fontSize: screenWidth / 20 }}>{strings('lang.ThereIsAnMaintananc')}</Text>
      } */}


      {/* {applicationStatus.IsUpdateRequired == true
        ? */}
      <Button onPress={update} style={{ width: "90%", height: 50, backgroundColor: DarkBlue, borderRadius: 5, borderColor: DarkBlue, borderWidth: 1, alignSelf: "center", justifyContent: "center", marginTop: "25%" }}>
        <Text style={{ textAlign: "center", fontFamily: appFontBold, color: "#fff", fontSize: screenWidth / 25, }}>{(strings('lang.UpdateNow'))}</Text>
      </Button>
      {/* :
        <View></View>
      } */}


    </View>
  );
};


export default RequiredUpadte;


