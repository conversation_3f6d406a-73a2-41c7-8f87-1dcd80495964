import AsyncStorage from '@react-native-async-storage/async-storage';
import { useIsFocused } from '@react-navigation/native';
import moment from 'moment';
import { Button } from 'native-base';
import React, { useEffect, useState } from 'react';
import {
    I18nManager,
    Image,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import * as packagesActions from '../../Store/Actions/packages';
import { ScrollView } from 'react-native-gesture-handler';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import SelectDropdown from 'react-native-select-dropdown';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import { useDispatch } from 'react-redux';
import Header from '../components/Header';
import Loading from '../components/Loading';
import {
    appFont,
    appFontBold,
    Black,
    DarkBlue,
    DarkGrey,
    DarkYellow,
    Medium<PERSON>rey,
    Red,
    screenHeight,
    screenWidth,
    White,
    WhiteGery
} from '../components/Styles';
import Toaster from '../components/Toaster';
import { strings } from './i18n';



const DeliveryData = props => {

    const getDateTwoDaysAfterTomorrow = () => {
        return moment().add(2, 'days').format('L');
    };

    const dispatch = useDispatch();
    const [termsAndConditionData, setTermsAndConditionData] = useState('');
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [datePickerVisible, setDatePickerVisibility] = useState(false);
    const [date, setDate] = useState('');
    const [datePickerVisible1, setDatePickerVisibility1] = useState(false);
    const [date1, setDate1] = useState('');
    const [datePickerVisible2, setDatePickerVisibility2] = useState(false);
    const [date2, setDate2] = useState(getDateTwoDaysAfterTomorrow());
    const [tripTypes, setTripTypes] = useState([
        {
            id: 1,
            name: strings('lang.oneWay')
        },
        {
            id: 2,
            name: strings('lang.roundTrip')
        },
        {
            id: 3,
            name: strings('lang.MultipleDestinations')
        }
    ]);
    const [tripTypeId, setTripTypeId] = useState(1);
    const [destinations, setDestinations] = useState([
        {
            ID: 0,
            Name: strings('lang.School')
        },
        {
            ID: 1,
            Name: strings('lang.Work')
        }
        ,
        {
            ID: 2,
            Name: strings('lang.Training')
        }
        ,
        {
            ID: 3,
            Name: strings('lang.Others')
        }
    ]);
    const [days, setDays] = useState([
        {
            id: 'Saturday',
            Name: strings('lang.Saturday')
        },
        {
            id: 'Sunday',
            Name: strings('lang.Sunday')
        }
        ,
        {
            id: 'Monday',
            Name: strings('lang.Monday')
        },
        {
            id: 'Tuesday',
            Name: strings('lang.Tuesday')
        },
        {
            id: 'Wednesday',
            Name: strings('lang.Wednesday')
        },
        {
            id: 'Thursday',
            Name: strings('lang.Thursday')
        },
        {
            id: 'Friday',
            Name: strings('lang.Friday')
        },
    ]);
    const [userDays, setUserDays] = useState([

    ]);
    const [currentDay, setCurrentDay] = useState('');

    const [modalVisible, setModalVisible] = useState(false);
    const [source, setSource] = useState('');
    const [sourceId, setSourceId] = useState(0);
    const [sourceLabel, setSourceLabel] = useState('');
    const [fromLat, setFromLat] = useState('');
    const [fromLng, setFromLng] = useState('');
    const [destination, setDestination] = useState('');
    const [destinationId, setDestinationId] = useState(0);
    const [destinationType, setDestinationType] = useState('');
    const [destinationTypeId, setDestinationTypeId] = useState(null);
    const [destinationLabel, setDestinationLabel] = useState('');
    const [toLat, setToLat] = useState('');
    const [toLng, setTolng] = useState('');
    const [distance, setDistance] = useState(0);

    const [userDestinations, setUserDestinations] = useState([]);
    const [token, setToken] = useState('');
    const [minimumDate, setMinimumDate] = useState('');

    const IsFocused = useIsFocused();



    const loginfirst = async () => {
        Toaster(
            'top',
            'danger',
            Red,
            strings('lang.mustLogin'),
            White,
            1500,
            screenHeight / 15,
        );
    }

    useEffect(() => {
        async function GetInitalData() {
            let token = await AsyncStorage.getItem('token')
            setToken(token)
            var day = new Date().toLocaleString("en", { weekday: "long" });
            setCurrentDay(day)
            console.log('day', day);
            const currentDate = moment();
            const twoDaysAfter = currentDate.add(2, 'days');
            const formattedDate = twoDaysAfter.format('YYYY, M, DD');
            setMinimumDate(formattedDate);

        }
        GetInitalData()

        if (props.route.params.source == true) {
            if (props.route.params.addressDesription) {
                setSource(props.route.params.addressDesription);
                setSourceId(props.route.params.addressId);
                setSourceLabel(props.route.params.addressLabel);
                setFromLat(props.route.params.lat);
                setFromLng(props.route.params.lng);
            }
        }
        if (props.route.params.source == false) {
            if (props.route.params.addressDesription) {
                setDestination(props.route.params.addressDesription);
                setDestinationId(props.route.params.addressId);
                setDestinationLabel(props.route.params.addressLabel);
                setToLat(props.route.params.lat);
                setTolng(props.route.params.lng);
            }
        }
    }, [props, IsFocused]);

    const addUserDestination = async () => {
        if (fromLat == '' || fromLng == '' || source == '' || toLat == '' || toLng == '' || destination == '' || destinationTypeId == null || (date == '' && date1 == '')) {

        }
        else {
            let distance = 0;
            if (source && destination) {
                let GOOGLE_DISTANCES_API_KEY = 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'
                let ApiURL = "https://maps.googleapis.com/maps/api/distancematrix/json?";
                let mode = 'driving'
                let params = `origins=${source}&destinations=${destination}&mode=${mode}&key=${GOOGLE_DISTANCES_API_KEY}`;
                let finalApiURL = `${ApiURL}${encodeURI(params)}`;

                console.log("finalApiURL:\n");
                console.log(finalApiURL);

                // get duration/distance from base to each target
                try {
                    let response = await fetch(finalApiURL);
                    let responseJson = await response.json();
                    console.log("responseJson:\n");
                    console.log(responseJson);
                    distance = responseJson.rows[0].elements[0].distance.value
                    console.log('mmmmmmmmmm', responseJson.rows[0].elements[0].distance);
                } catch (error) {
                    console.error(error);
                }
            }
            let userDestination = {
                from_lat: fromLat,
                from_lng: fromLng,
                from_address: source,
                from_label: sourceLabel,
                to_lat: toLat,
                to_lng: toLng,
                to_address: destination,
                to_label: destinationLabel,
                type: destinationType,
                start_time: date,
                end_time: date1,
                distance: distance
            }
            setUserDestinations([...userDestinations, ...[userDestination]])
            // getDistance(source, destination)

            // setSource('');
            // setSourceId(0);
            // setSourceLabel('');
            // setFromLat('');
            // setFromLng('');
            setSource(destination);
            setSourceId(destinationId);
            setSourceLabel(destinationLabel);
            setFromLat(toLat);
            setFromLng(toLng);
            setDestination('');
            setDestinationId(0);
            setDestinationType('');
            setDestinationTypeId(0);
            setDestinationLabel('');
            setToLat('');
            setTolng('');
            setDestinationTypeId(null);
            setDestinationType('');
            setDate('');
            setDate1('');
        }
    }

    const delay = (delayInms) => {
        return new Promise(resolve => setTimeout(resolve, delayInms));
    };

    const getPackages = async () => {
        setLoadingMore(true)
        let distance = 0;
        let distanceReturn = 0;
        let destinations = [...userDestinations]

        if (fromLat != '' && fromLng != '' && source != '' && toLat != '' && toLng != '' && destination != '' && destinationTypeId != null && date2 != '') {

            if (source && destination) {
                let GOOGLE_DISTANCES_API_KEY = 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'
                let ApiURL = "https://maps.googleapis.com/maps/api/distancematrix/json?";
                let mode = 'driving'
                let params = `origins=${source}&destinations=${destination}&mode=${mode}&key=${GOOGLE_DISTANCES_API_KEY}`;
                let finalApiURL = `${ApiURL}${encodeURI(params)}`;

                console.log("finalApiURL:\n");
                console.log(finalApiURL);

                // get duration/distance from base to each target
                try {
                    let response = await fetch(finalApiURL);
                    let responseJson = await response.json();
                    console.log("responseJson:\n");
                    console.log(responseJson);
                    distance = responseJson.rows[0].elements[0].distance.value
                    console.log('mmmmmmmmmm', responseJson.rows[0].elements[0].distance);
                } catch (error) {
                    console.error(error);
                }
            }

            if (tripTypeId == 2 && source && destination) {
                let GOOGLE_DISTANCES_API_KEY = 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'
                let ApiURL = "https://maps.googleapis.com/maps/api/distancematrix/json?";
                let mode = 'driving'
                let params = `origins=${destination}&destinations=${source}&mode=${mode}&key=${GOOGLE_DISTANCES_API_KEY}`;
                let finalApiURL = `${ApiURL}${encodeURI(params)}`; source

                console.log("finalApiURL:\n");
                console.log(finalApiURL);

                // get duration/distance from base to each target
                try {
                    let response = await fetch(finalApiURL);
                    let responseJson = await response.json();
                    console.log("responseJson:\n");
                    console.log(responseJson);
                    distanceReturn = responseJson.rows[0].elements[0].distance.value
                    console.log('mmmmmmmmmm', responseJson.rows[0].elements[0].distance);
                } catch (error) {
                    console.error(error);
                }
            }



            if (tripTypeId == 2) {
                console.log('2');
                let userDestination = {
                    from_lat: fromLat,
                    from_lng: fromLng,
                    from_address: source,
                    from_label: sourceLabel,
                    to_lat: toLat,
                    to_lng: toLng,
                    to_address: destination,
                    to_label: destinationLabel,
                    type: destinationType,
                    start_time: date,
                    end_time: date1,
                    distance: distance
                }

                let userReturnDestination = {
                    from_lat: toLat,
                    from_lng: toLng,
                    from_address: destination,
                    from_label: destinationLabel,
                    to_lat: fromLat,
                    to_lng: fromLng,
                    to_address: source,
                    to_label: sourceLabel,
                    type: destinationType,
                    start_time: date,
                    end_time: date1,
                    distance: distanceReturn
                }

                destinations = [...destinations, ...[userDestination, userReturnDestination]]
                setUserDestinations([...userDestinations, ...[userDestination, userReturnDestination]])
            }
            else {
                console.log('1||3');

                let userDestination = {
                    from_lat: fromLat,
                    from_lng: fromLng,
                    from_address: source,
                    from_label: sourceLabel,
                    to_lat: toLat,
                    to_lng: toLng,
                    to_address: destination,
                    to_label: destinationLabel,
                    type: destinationType,
                    start_time: date,
                    end_time: date1,
                    distance: distance
                }
                destinations = [...destinations, ...[userDestination]]
                setUserDestinations([...userDestinations, ...[userDestination]])
            }


            console.log('destinations', destinations);

            setSource('');
            setSourceId(0);
            setSourceLabel('');
            setFromLat('');
            setFromLng('');
            setDestination('');
            setDestinationId(0);
            setDestinationType('');
            setDestinationTypeId(0);
            setDestinationLabel('');
            setToLat('');
            setTolng('');
            setDestinationTypeId(null);
            setDestinationType('');
            setDate('');
            setDate1('');
            setDate2('');
        }

        if (!token) {
            loginfirst()
            setLoadingMore(false)

        }
        // else if (date == '' || date1 == '' || userDays.length == 0 || destinations.length == 0) {
        else if (userDays.length == 0 || destinations.length == 0) {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.pleaseCompleteData'),
                White,
                1500,
                screenHeight / 50,
            );
            setLoadingMore(false)
        }
        else {
            // setModalVisible(true)
            try {
                setLoadingMore(true)
                let response = await dispatch(packagesActions.createPackagesRequest(
                    destinations,
                    tripTypeId == 1 ? date : tripTypeId == 2 ? date : destinations[0].start_time != '' ? moment(destinations[0].start_time, ["h:mm A"]).format("HH:mm") : date,
                    tripTypeId == 1 ? date : tripTypeId == 2 ? date1 : destinations[destinations.length - 1].end_time != '' ? moment(destinations[destinations.length - 1].end_time, ["h:mm A"]).format("HH:mm") : moment(destinations[0].start_time, ["h:mm A"]).format("HH:mm"),
                    date2,
                    userDays
                ));

                if (response.success == true) {
                    setLoadingMore(false)
                    setModalVisible(true)
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false)
                }
            } catch (err) {
                console.log(err);
                setLoadingMore(false)
            }
        }
    }

    const getDistance = async (source, destination) => {
        console.log('source', source);
        console.log('destination', destination);

        if (source && destination) {

            let GOOGLE_DISTANCES_API_KEY = 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'
            let ApiURL = "https://maps.googleapis.com/maps/api/distancematrix/json?";
            let mode = 'driving'
            let params = `origins=${source}&destinations=${destination}&mode=${mode}&key=${GOOGLE_DISTANCES_API_KEY}`;
            let finalApiURL = `${ApiURL}${encodeURI(params)}`;

            console.log("finalApiURL:\n");
            console.log(finalApiURL);

            // get duration/distance from base to each target
            try {
                let response = await fetch(finalApiURL);
                let responseJson = await response.json();
                console.log("responseJson:\n");
                console.log(responseJson);
                setDistance(distance + responseJson.rows[0].elements[0].distance.value)
                console.log('mmmmmmmmmm', responseJson.rows[0].elements[0].distance);
            } catch (error) {
                console.error(error);
            }
        }
    }

    if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Notifications')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Deliverydata')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                {loadingMore
                    ?
                    <Loading />
                    :
                    <></>
                }

                <View style={styles.changeData}>
                    {tripTypes.map((item, idex) => {
                        return (
                            <Pressable
                                onPress={() => { setTripTypeId(item.id); console.log('setTripTypeId', item.id); }}
                                style={[item.id == tripTypeId ? styles.active : styles.unActive]}>
                                <Text style={[styles.textData, { color: item.id == tripTypeId ? DarkBlue : Black }]}>{item.name}</Text>
                            </Pressable>
                        )
                    })}
                </View>

                <ScrollView
                    style={{ width: '100%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={{ width: '95%', alignItems: 'center', justifyContent: 'flex-start', alignSelf: 'center', }}>


                        <Text style={styles.label}>{tripTypeId == 2 ? strings('lang.GoingTrip') : strings('lang.destination')}</Text>

                        <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 7, justifyContent: 'space-between' }}>
                            <View style={[styles.iconConatiner2, { paddingVertical: '5%' }]}>
                                <Image source={require('../images/bluelocation.png')} style={styles.locationImageSmall} />

                                <View style={{ height: '60%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, zIndex: 0, }}></View>

                                <Image source={require('../images/yallowlocation.png')} style={styles.locationImageSmall} />
                            </View>
                            <View style={{ width: '90%' }}>
                                <Pressable
                                    onPress={() => { props.navigation.navigate('Search', { screen: 'DeliveryData', source: true }) }}
                                    style={styles.contentContainer2}>

                                    <View style={styles.textInput2}>
                                        <Text numberOfLines={1} style={{
                                            color: source ? Black : MediumGrey, textAlignVertical: 'center',
                                            fontFamily: appFont,
                                            fontSize: screenWidth / 32,
                                            textAlign: I18nManager.isRTL ? 'left' : 'left', width: '80%'
                                        }}>
                                            {
                                                sourceLabel
                                                    ? sourceLabel
                                                    :
                                                    source
                                                        ?
                                                        source
                                                        :
                                                        strings('lang.Yourlocationis')
                                            }
                                        </Text>
                                        <Pressable onPress={() => { props.navigation.navigate('Destenation', { screen: 'DeliveryData', source: true }) }}>
                                            <Text style={{
                                                color: DarkBlue, textAlignVertical: 'center',
                                                fontFamily: appFont,
                                                fontSize: screenWidth / 36,
                                                textAlign: I18nManager.isRTL ? 'right' : 'left',
                                                marginStart: '2%', textDecorationStyle: 'solid', textDecorationLine: 'underline'
                                            }}>
                                                {strings('lang.Locate')}
                                            </Text>
                                        </Pressable>
                                    </View>
                                </Pressable>

                                <Pressable
                                    onPress={() => { props.navigation.navigate('Search', { screen: 'DeliveryData', source: false }) }}
                                    style={styles.contentContainer2}>

                                    <View style={styles.textInput2}>
                                        <Text numberOfLines={1} style={{
                                            color: destination ? Black : MediumGrey, textAlignVertical: 'center',
                                            fontFamily: appFont,
                                            fontSize: screenWidth / 32,
                                            textAlign: I18nManager.isRTL ? 'left' : 'left',
                                            marginStart: '2%', width: '80%'
                                        }}>
                                            {
                                                destinationLabel ?
                                                    destinationLabel :
                                                    destination
                                                        ?
                                                        destination
                                                        :
                                                        strings('lang.destination')
                                            }
                                        </Text>
                                        <Pressable onPress={() => { props.navigation.navigate('Destenation', { screen: 'DeliveryData', source: false }) }}>
                                            <Text style={{
                                                color: DarkBlue, textAlignVertical: 'center',
                                                fontFamily: appFont,
                                                fontSize: screenWidth / 36,
                                                textAlign: I18nManager.isRTL ? 'right' : 'left',
                                                marginStart: '2%', textDecorationStyle: 'solid', textDecorationLine: 'underline'
                                            }}>
                                                {strings('lang.Locate')}
                                            </Text>
                                        </Pressable>
                                    </View>
                                </Pressable>
                            </View>
                        </View>

                        {tripTypeId == 2
                            ?
                            <>
                                <Text style={styles.label}>{strings('lang.ReturnTrip')}</Text>
                                <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 7, justifyContent: 'space-between' }}>
                                    <View style={[styles.iconConatiner2, { paddingVertical: '5%' }]}>
                                        <Image source={require('../images/bluelocation.png')} style={styles.locationImageSmall} />

                                        <View style={{ height: '60%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, zIndex: 0, }}></View>

                                        <Image source={require('../images/yallowlocation.png')} style={styles.locationImageSmall} />
                                    </View>
                                    <View style={{ width: '90%' }}>

                                        <Pressable
                                            style={styles.contentContainer2}>
                                            <View style={styles.textInput2}>
                                                <Text numberOfLines={1} style={{
                                                    color: destination ? Black : MediumGrey, textAlignVertical: 'center',
                                                    fontFamily: appFont,
                                                    fontSize: screenWidth / 32,
                                                    textAlign: I18nManager.isRTL ? 'left' : 'left',
                                                    marginStart: '2%', width: '80%'
                                                }}>
                                                    {
                                                        destinationLabel ?
                                                            destinationLabel :
                                                            destination
                                                                ?
                                                                destination
                                                                :
                                                                strings('lang.from')
                                                    }
                                                </Text>
                                            </View>
                                        </Pressable>
                                        <Pressable
                                            style={styles.contentContainer2}>

                                            <View style={styles.textInput2}>
                                                <Text numberOfLines={1} style={{
                                                    color: source ? Black : MediumGrey, textAlignVertical: 'center',
                                                    fontFamily: appFont,
                                                    fontSize: screenWidth / 32,
                                                    textAlign: I18nManager.isRTL ? 'left' : 'left', width: '80%'
                                                }}>
                                                    {
                                                        sourceLabel
                                                            ? sourceLabel
                                                            :
                                                            source
                                                                ?
                                                                source
                                                                :
                                                                strings('lang.to')
                                                    }
                                                </Text>

                                            </View>
                                        </Pressable>
                                    </View>
                                </View>
                            </>
                            :
                            <></>}

                        {(tripTypeId == 1 || tripTypeId == 2 || (tripTypeId == 3 && userDestinations.length == 0))
                            &&
                            <>
                                <Text style={styles.label}>{strings('lang.Timetogo')}</Text>
                                <TouchableOpacity
                                    onPress={() => { setDatePickerVisibility(!datePickerVisible) }}
                                    style={styles.contentContainer}>
                                    <View style={styles.textTime}>
                                        <Text style={{
                                            color: Black, textAlignVertical: 'center',
                                            fontFamily: appFont,
                                            fontSize: screenWidth / 32,
                                        }}>
                                            {date
                                                ?
                                                date
                                                :
                                                strings('lang.Setthetime')
                                            }
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </>
                        }
                        {(tripTypeId == 2 || (tripTypeId == 3 && userDestinations.length > 0))
                            &&
                            <>
                                <Text style={styles.label}>{strings('lang.returntime')}</Text>
                                <TouchableOpacity
                                    onPress={() => { setDatePickerVisibility1(!datePickerVisible1) }}
                                    style={styles.contentContainer}>
                                    <View style={styles.textTime}>
                                        <Text style={{
                                            color: Black, textAlignVertical: 'center',
                                            fontFamily: appFont,
                                            fontSize: screenWidth / 32,
                                        }}>
                                            {date1
                                                ?
                                                date1
                                                :
                                                strings('lang.Setthetime')
                                            }
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </>
                        }




                        <Text style={styles.label}>{strings('lang.facetype')}</Text>
                        <SelectDropdown
                            renderDropdownIcon={() => {
                                return (
                                    // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                                    <Image
                                        source={require('../images/Group9817.png')}
                                        style={styles.icon}
                                    />
                                );
                            }}
                            buttonTextAfterSelection={selectedItem => {
                                return destinationTypeId == null ? strings('lang.Selectthetypeofface') : selectedItem.Name;
                            }}
                            dropdownIconPosition={'left'}
                            dropdownStyle={{ borderRadius: 5 }}
                            defaultButtonText={strings('lang.Selectthetypeofface')}
                            buttonTextStyle={{
                                color: DarkGrey,
                                fontFamily: appFont,
                                fontSize: screenWidth / 28,
                                textAlign: 'left',
                            }}
                            rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                            buttonStyle={{
                                width: '95%',
                                alignSelf: 'center',
                                textAlign: 'left',
                                fontFamily: appFont,
                                backgroundColor: White,
                                borderRadius: 20,
                                height: screenHeight / 18,
                                marginVertical: '2%',
                                borderWidth: 1,
                                borderColor: MediumGrey,
                                flexDirection: 'row',
                            }}
                            data={destinations}
                            onSelect={selectedItem => {
                                setDestinationTypeId(selectedItem.ID);
                                setDestinationType(selectedItem.Name);
                                // setError_areaId('');
                            }}
                            rowTextForSelection={item => {
                                return item.Name;
                            }}
                        />

                        {/* {tripTypeId == 3
                            ?
                            <View style={{ width: '95%', borderRadius: 20, backgroundColor: Gold, marginTop: 5, opacity: 0.9, padding: 10 }}>
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 30,
                                        color: Black,
                                        alignSelf: 'center',
                                        textAlign: 'center'
                                    }}
                                >
                                    {strings('lang.message25')}
                                </Text>
                            </View>
                            :
                            <></>} */}

                        {tripTypeId == 3
                            ?
                            <View
                                style={{
                                    width: '100%',
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    alignSelf: 'center',
                                    alignItems: 'center',
                                    marginVertical: screenHeight / 60,
                                    height: screenHeight / 22
                                }}
                            >
                                <Button
                                    onPress={() => { addUserDestination() }}
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        alignSelf: 'center',
                                        width: '95%',
                                        height: screenHeight / 22,
                                        backgroundColor: DarkBlue,
                                        borderRadius: 20,
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontFamily: appFontBold,
                                            fontSize: screenWidth / 30,
                                            color: White,
                                        }}
                                    >
                                        {strings('lang.Addanotherface')}
                                    </Text>
                                </Button>
                            </View>
                            :
                            <></>}


                        {userDestinations.map((item, index) => {
                            return (
                                <View style={{ flexDirection: 'row', overflow: 'hidden', padding: 10, width: '100%', borderWidth: 1, alignItems: 'center', justifyContent: 'space-between', borderColor: MediumGrey, marginBottom: 10, borderRadius: 20 }}>
                                    <View
                                        style={{ width: '85%', alignItems: 'flex-start' }}>
                                        <Text style={styles.textblack1}>{strings('lang.facetype')} : {item.type}</Text>
                                        <Text style={styles.textblack1}>{strings('lang.from')} : {item.from_label ? item.from_label : item.from_address}</Text>
                                        <View style={{ height: 5 }}></View>
                                        <Text style={styles.textgrey}>{strings('lang.to')} : {item.to_label ? item.to_label : item.to_address}</Text>
                                        <Text style={styles.textgrey}>{item.start_time ? strings('lang.Timetogo') : strings('lang.returntime')} : {item.start_time ? item.start_time : item.end_time}</Text>
                                    </View>
                                    <View style={{ width: '10%', }}>
                                        <Button
                                            transparent
                                            onPress={() => {
                                                let userDests = [...userDestinations];
                                                let index = userDests.findIndex((userDestination) => userDestination.from_lat == item.from_lat);
                                                userDests.splice(index, 1)
                                                setUserDestinations(userDests);
                                            }}
                                            style={{ width: '80%', height: screenHeight / 10, alignItems: 'center', justifyContent: 'center', }}>
                                            <Image source={require('../images/xx.png')} style={{ width: '100%', resizeMode: 'contain', tintColor: Red, alignSelf: 'center' }} />
                                        </Button>
                                    </View>
                                </View>
                            )
                        })}

                        {/* <View style={{ width: '100%', backgroundColor: DarkYellow, borderRadius: 20, padding: 5, marginVertical: 10, alignItems: 'center', justifyContent: 'center' }}>
                            <Text style={{ ...styles.label, ...{ textAlign: 'center' } }}>{strings('lang.message25')}</Text>
                        </View> */}



                        <Text style={styles.label}>{strings('lang.message26')}</Text>
                        <TouchableOpacity
                            onPress={() => { setDatePickerVisibility2(!datePickerVisible2) }}
                            style={styles.contentContainer}>
                            <View style={styles.textTime}>
                                <Text style={{
                                    color: Black, textAlignVertical: 'center',
                                    fontFamily: appFont,
                                    fontSize: screenWidth / 32,
                                }}>
                                    {date2
                                        ?
                                        date2
                                        :
                                        strings('lang.Date')
                                    }
                                </Text>
                            </View>
                        </TouchableOpacity>

                        <Text style={[styles.label, { marginTop: '5%', marginBottom: '2%' }]}>{strings('lang.Repeatthenumberoftimesaweek')}</Text>
                        <View style={{ flexWrap: 'wrap', width: '95%', flexDirection: 'row', alignItems: 'center', alignSelf: 'center' }}>
                            {days.map((item, index) => {
                                return (
                                    <TouchableOpacity
                                        onPress={() => {
                                            let days = [...userDays];
                                            let element = userDays.find((element) => element.id == item.id);
                                            let index = userDays.findIndex((element) => element.id == item.id);
                                            console.log(element);
                                            if (element) {
                                                days.splice(index, 1)
                                                setUserDays(days);
                                            }
                                            else {
                                                setUserDays([...userDays, item])
                                            }


                                        }}
                                        style={{ backgroundColor: userDays.find(x => x.id === item.id) ? DarkYellow : null, marginVertical: '2%', overflow: 'hidden', marginHorizontal: '2%', width: '21%', height: 30, borderRadius: 20, borderColor: MediumGrey, borderWidth: .8, alignItems: 'center', justifyContent: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black }}>{item.Name}</Text>
                                    </TouchableOpacity>
                                )
                            })}


                        </View>



                        <DateTimePickerModal
                            isVisible={datePickerVisible}
                            mode="time"
                            onConfirm={
                                (selectedDate) => {
                                    console.log('selectedDate', selectedDate)
                                    const currentDate = selectedDate || date;
                                    // setDate(currentDate);
                                    console.log('currentDateTime', moment(currentDate).format())
                                    setDatePickerVisibility(false)
                                    setDate(moment(currentDate).format('LT'));
                                }
                            }
                            onCancel={() => { setDatePickerVisibility(false) }}
                        />

                        {/* <DateTimePicker
                            isVisible={datePickerVisible}
                            // timePickerModeAndroid={true}
                            mode="time"
                            // onDateChange={(time) => onDateChange(time)}
                            display={'spinner'}
                            onConfirm={
                                (selectedDate) => {
                                    console.log('selectedDate', selectedDate)
                                    const currentDate = selectedDate || date;
                                    // setDate(currentDate);
                                    console.log('currentDateTime', moment(currentDate).format())
                                    setDatePickerVisibility(false)
                                    setDate(moment(currentDate).format('LT'));
                                }
                            }
                            onCancel={() => { setDatePickerVisibility(false) }}
                            androidVariant='nativeAndroid'
                        /> */}
                        <DateTimePickerModal
                            isVisible={datePickerVisible1}
                            mode="time"
                            onConfirm={
                                (selectedDate) => {
                                    console.log('selectedDate', selectedDate)
                                    const currentDate = selectedDate || date1;
                                    // setDate1(currentDate);
                                    console.log('currentDateTime', moment(currentDate).format())
                                    setDatePickerVisibility1(false)
                                    setDate1(moment(currentDate).format('LT'));
                                }
                            }
                            onCancel={() => { setDatePickerVisibility1(false) }}
                        />

                        <DateTimePickerModal
                            isVisible={datePickerVisible2}
                            mode="date"
                            onConfirm={
                                (selectedDate) => {
                                    console.log('selectedDate', selectedDate)
                                    // let dayAfterTommorow = moment().add(2, 'days').format('L')
                                    const currentDate = moment(selectedDate).format('L') == moment().format('L') ? getDateTwoDaysAfterTomorrow() : moment(selectedDate).format('L');
                                    // setDate(currentDate);
                                    console.log('currentDateTime', moment(currentDate).format())
                                    setDatePickerVisibility2(false)
                                    setDate2(moment(currentDate).format('L'));
                                }
                            }
                            minimumDate={new Date(minimumDate)}
                            onCancel={() => { setDatePickerVisibility2(false) }}
                        />

                    </View>
                </ScrollView>

                <View style={{
                    flexDirection: 'row', width: '95%', alignItems: 'center',
                    justifyContent: 'center',
                    alignSelf: 'center',
                }}>
                    <Image source={require('../images/warning.png')} style={{
                        height: '100%', resizeMode: 'contain',
                        width: '10%',
                    }} />
                    <Text style={{ fontSize: screenWidth / 35, color: Red, fontFamily: appFontBold }}>{strings('lang.messagePackage2')}</Text>
                </View>

                <View
                    style={{
                        width: '100%',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignSelf: 'center',
                        alignItems: 'center',
                        marginVertical: screenHeight / 60,
                        height: screenHeight / 20
                    }}
                >
                    <Button
                        onPress={() => { getPackages() }}
                        style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            alignSelf: 'center',
                            width: '95%',
                            height: screenHeight / 20,
                            backgroundColor: DarkBlue,
                            borderRadius: 20,
                        }}
                    >

                        <Text
                            style={{
                                fontFamily: appFontBold,
                                fontSize: screenWidth / 28,
                                color: White,
                            }}
                        >
                            {strings('lang.Sendorder')}
                        </Text>
                    </Button>
                </View>


                <Modal
                    transparent={true}
                    animationType="fade"
                    visible={modalVisible}
                    propagateSwipe={true}
                >


                    {/* <View style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight / 14 + 2 * screenHeight / 5.5, minHeight: screenHeight }} > */}
                    <View style={{ backgroundColor: '#000000', opacity: 0.5, width: screenWidth, minHeight: screenHeight }} >
                    </View>

                    <View style={[styles.modal]}>
                        <Image source={require('../images/check.png')} style={{
                            height: '30%', resizeMode: 'contain',
                            width: '100%',
                        }} />
                        <View style={{ marginVertical: '5%', alignItems: 'center', justifyContent: 'center' }}>
                            <Text
                                style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 22,
                                    color: Black,
                                }}
                            >
                                {strings('lang.Therequesthasbeensentsuccessfully')}
                            </Text>
                            <Text
                                style={{
                                    fontFamily: appFont,
                                    fontSize: screenWidth / 30,
                                    color: DarkGrey,
                                }}
                            >
                                {strings('lang.Suitableofferswillbesentwithintwodays')}
                            </Text>
                        </View>

                        <View
                            style={{
                                width: '100%',
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignSelf: 'center',
                                alignItems: 'center',
                                marginTop: screenHeight / 60,
                                height: screenHeight / 20
                            }}
                        >
                            <Button
                                onPress={() => { props.navigation.navigate('Home'); setModalVisible(false) }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    alignSelf: 'center',
                                    width: '90%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 28,
                                        color: Black,
                                    }}
                                >
                                    {strings('lang.Home')}
                                </Text>
                            </Button>
                        </View>
                    </View>



                </Modal>
                <View style={{ height: screenHeight / 60 }}></View>
            </View>
        );
    }
};

const styles = StyleSheet.create({
    modal: {
        alignItems: 'center',
        width: screenWidth / 1.1,
        height: screenHeight / 2.5,
        position: 'absolute',
        top: screenHeight / 2.8,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 20,
        paddingVertical: '2%',
        justifyContent: 'center',
        paddingHorizontal: '5%',
    },
    contentContainer: {
        width: '95%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2%',
        height: screenHeight / 20,
        paddingHorizontal: 0,
        flexDirection: 'row'
    },
    locationImage: {
        resizeMode: 'contain',
        width: '100%',
        height: '100%'
    },
    textInput: {
        width: '100%',
        flexDirection: 'row',
        alignSelf: 'center',
        height: '100%',
        alignItems: 'center',
        // justifyContent: 'space-between',

    },
    textTime: {
        width: '100%',
        alignSelf: 'center',
        borderRadius: 20,
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: WhiteGery
    },
    icon: {
        width: '5%',
        height: '40%',
        tintColor: MediumGrey,
        resizeMode: 'contain',
        marginHorizontal: '7%',
    },
    label: {
        fontSize: screenWidth / 30,
        fontFamily: appFontBold,
        alignSelf: 'flex-start',
        marginHorizontal: '5%',
        color: Black,
    },
    container: {
        height: screenHeight / 9,
        width: '100%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row',
        marginVertical: '5%',
        // backgroundColor: Red
    },
    imageContainer: {
        height: '80%',
        width: '10%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    image: {
        height: '25%',
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
    },
    locationImage: {
        height: '100%',
        width: '90%',
        resizeMode: 'contain'
    },
    inputContainer: {
        height: '100%',
        width: '90%',
        // alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    input: {
        height: screenHeight / 20,
        borderRadius: 20,
        width: '100%',
        backgroundColor: White,
        borderWidth: .8,
        flexDirection: 'row',
        paddingStart: '2%',
        borderColor: MediumGrey,
    },
    contentContainer2: {
        width: '100%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2%',
        height: screenHeight / 18,
        borderRadius: 5,
        paddingHorizontal: 0,
        flexDirection: 'row',
    },
    locationImageSmall: {
        resizeMode: 'contain',
        width: '100%',
        height: '20%'
    },
    textInput2: {
        width: '100%',
        flexDirection: 'row',
        alignSelf: 'center',
        borderRadius: 100,
        borderColor: MediumGrey,
        borderWidth: 1,
        height: '100%',
        alignItems: 'center',
        justifyContent: 'space-between',
        textAlignVertical: 'center',
        fontFamily: appFont,
        fontSize: screenWidth / 32,
        textAlign: I18nManager.isRTL ? 'right' : 'left',
        paddingHorizontal: 10, overflow: 'hidden'
    },
    iconConatiner2: { height: '100%', width: '10%', alignItems: 'center', justifyContent: 'center', },
    textblack1: { fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'left' },
    textgrey: { fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'left' },
    changeData: {
        width: screenWidth,
        height: screenHeight / 18,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        marginBottom: 10,
        backgroundColor: WhiteGery,
        flexDirection: 'row',
        paddingHorizontal: '5%'
    },
    unActive: {
        width: '30%',
        height: '100%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center', flexDirection: 'row'
    },
    active: {
        width: '30%',
        height: '100%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        borderBottomWidth: 3,
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
        borderBottomColor: DarkBlue, flexDirection: 'row'
    },
    textData: {
        fontFamily: appFont,
        fontSize: screenWidth / 30
    },
});

export default DeliveryData;
