import React, { useEffect, useState } from 'react';
import { View, Image, Text, I18nManager, ImageBackground } from "react-native";
import { appFont, screenWidth, DarkGreen, WhiteGreen, screenHeight, White, appFontBold, Red, MediumGrey, Gold, Blue, appColor1, appColor2, DarkBlue, Black, DarkGrey, } from '../components/Styles';
import { strings } from '../screens/i18n';
import { TouchableOpacity } from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch, useSelector } from 'react-redux';
import Header from '../components/Header';



const Intro3 = props => {
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(false);
    const intro_screens = useSelector(state => state.general.intro_screens);

    useEffect(() => {


    }, []);

    const Finish = () => {
        AsyncStorage.setItem("intro", "Done")
        props.navigation.navigate('Login')
    }

    return (
        <View style={{ flex: 1, alignItems: 'center', backgroundColor: '#f9f9f9', justifyContent: 'space-between', paddingBottom: screenHeight / 15 }}>

            <ImageBackground source={require('../images/MaskGroup21.jpg')} style={{ width: '100%', height: '100%', alignSelf: 'center', resizeMode: 'cover', alignItems: 'center' }} >

                <View style={{ width: "90%", height: screenHeight / 4, alignItems: 'center', marginTop: screenHeight / 1.6 }}>
                    <Text numberOfLines={8} style={{ color: Black, fontFamily: appFont, fontSize: screenWidth / 22, textAlign: 'center', width: "95%", marginBottom: screenHeight / 100 }}>{intro_screens[2] && intro_screens[2].title}</Text>
                    <Text numberOfLines={8} style={{ color: DarkGrey, fontFamily: appFont, fontSize: screenWidth / 28, textAlign: 'center', width: "95%" }}>{intro_screens[2] && intro_screens[2].description}</Text>
                </View>

                <View style={{ height: screenHeight / 15, width: '90%', flexDirection: 'row', alignItems: 'center', marginTop: '0%', justifyContent: 'space-between', alignSelf: 'center' }}>
                    <View style={{ height: '80%', width: '30%', alignItems: 'center', backgroundColor: MediumGrey, borderRadius: 20 }}>
                        <TouchableOpacity style={{ flexDirection: 'row', width: '100%', height: '100%', alignItems: 'center', }} onPress={() => { Finish() }}>
                            <Text style={{ color: Black, fontFamily: appFont, fontSize: screenWidth / 26, }}>{strings('lang.Skip')}</Text>
                        </TouchableOpacity>
                    </View>
                    <View style={{ height: '80%', width: 60, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-around' }}>
                        <View style={{ width: 10, height: 10, borderRadius: 5, backgroundColor: MediumGrey, }}></View>
                        <View style={{ width: 10, height: 10, borderRadius: 5, backgroundColor: MediumGrey, }}></View>
                        <View style={{ width: 10, height: 10, borderRadius: 5, backgroundColor: Gold, }}></View>
                    </View>
                    <View style={{ height: '80%', width: '30%', alignItems: 'center', backgroundColor: DarkBlue, borderRadius: 20 }}>
                        <TouchableOpacity style={{ flexDirection: 'row', width: '100%', height: '100%', alignItems: 'center', }} onPress={() => { Finish() }}>
                            <Text style={{ color: White, fontFamily: appFont, fontSize: screenWidth / 26, }}>{strings('lang.next')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>

            </ImageBackground>

        </View>
    );
};

export default Intro3;
