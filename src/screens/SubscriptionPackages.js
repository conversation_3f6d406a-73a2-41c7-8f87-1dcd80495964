import React, { useEffect, useState } from 'react';
import {
    I18nManager,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useDispatch } from 'react-redux';
import * as packagesActions from '../../Store/Actions/packages';
import Header from '../components/Header';
import Loading from '../components/Loading';
import {
    appFontBold,
    Black,
    DarkBlue,
    DarkGrey,
    DarkYellow,
    Grey,
    MediumGrey,
    MediumYellow,
    Red,
    screenHeight,
    screenWidth,
    White,
    WhiteGery
} from '../components/Styles';
import Toaster from '../components/Toaster';
import { strings } from './i18n';



const SubscriptionPackages = props => {
    const dispatch = useDispatch();
    const [termsAndConditionData, setTermsAndConditionData] = useState('');
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [subscribeId, setSubscribeId] = useState(false)
    const [packages, setPackages] = useState(props.route.params.packages);


    useEffect(() => {

    }, []);

    const createPackages = async (id) => {
        // setLoadingMore(true)
        // if (paymentMethodId == 0) {
        //     Toaster(
        //         'top',
        //         'danger',
        //         Red,
        //         strings('lang.ChoosePaymentMethod'),
        //         White,
        //         1500,
        //         screenHeight / 50,
        //     );
        //     setLoadingMore(false)
        // }
        // else {
        try {
            setLoadingMore(true)
            let response = await dispatch(packagesActions.createPackages(
                props.route.params.userDestinations,
                props.route.params.date,
                props.route.params.date1,
                props.route.params.date2,
                props.route.params.userDays,
                id,
                //   paymentMethodId,
                // props.route.params.distance / 1000,
            ));

            if (response.success == true) {
                props.navigation.navigate('PaymentMethods', { subscription: true, item: response.data })
                setLoadingMore(false)
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
        // }
    }


    if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Notifications')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    } else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Packages')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                {loadingMore
                    ?
                    <Loading />
                    :
                    <></>
                }

                <ScrollView
                    style={{ width: '100%', }}
                    showsVerticalScrollIndicator={false}
                >
                    {/* <View style={{ height: screenHeight / 6, width: '95%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center' }}>
                        <Image source={require('../images/Group10549.png')} style={{ resizeMode: 'contain', width: '50%', height: '70%' }} />
                    </View> */}
                    {packages.map((item, index) => {
                        return (
                            <View style={{ width: '95%', alignItems: 'center', justifyContent: 'center', alignSelf: 'center', marginVertical: screenHeight / 80, borderWidth: 1, borderColor: item.tag ? MediumYellow : Grey, borderRadius: 20, paddingVertical: screenHeight / 60 }}>
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '90%', alignSelf: 'center', marginBottom: '2%' }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 22, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}> {item.name} </Text>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 22, color: DarkYellow, textAlign: I18nManager.isRTL ? 'left' : 'right', }}>{item.final_price} {strings('lang.SR')} </Text>
                                </View>
                                {item.discount
                                    ?
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '90%', alignSelf: 'center', marginBottom: '2%', backgroundColor: WhiteGery, padding: 5 }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Red, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{strings('lang.Discount')} {item.discount}% {strings('lang.limited')}</Text>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: DarkGrey, textAlign: I18nManager.isRTL ? 'left' : 'right', textDecorationLine: 'line-through', }}>{item.price} {strings('lang.SR')}</Text>
                                    </View>
                                    :
                                    <></>
                                }

                                <TouchableOpacity
                                    onPress={() => { createPackages(item.id) }}
                                    style={{ height: screenHeight / 25, width: '90%', alignItems: 'center', justifyContent: 'center', backgroundColor: DarkBlue, borderRadius: 20 }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: White, }}> {strings('lang.Confirm')} </Text>
                                </TouchableOpacity>

                                {item.tag
                                    ?
                                    <View style={{ width: screenWidth / 2, height: screenHeight / 30, backgroundColor: MediumYellow, position: 'absolute', top: -screenHeight / 55, alignItems: 'center', justifyContent: 'center', borderRadius: 5 }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}> {item.tag} </Text>
                                    </View>
                                    :
                                    <></>
                                }

                            </View>
                        )
                    })}

                </ScrollView>


                <View style={{ height: screenHeight / 20 }}></View>
            </View>
        );
    }
};

const styles = StyleSheet.create({

});

export default SubscriptionPackages;
