import { useFocusEffect } from '@react-navigation/native';
import { Button } from 'native-base';
import React, { useEffect, useState } from 'react';
import {
    BackHandler,
    I18nManager,
    Image,
    StyleSheet,
    Text,
    View
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useDispatch } from 'react-redux';
import Header from '../components/Header';
import {
    appFont,
    appFontBold,
    Black,
    DarkBlue,
    DarkGrey,
    MediumGrey,
    screenHeight,
    screenWidth,
    White,
    WhiteGery
} from '../components/Styles';
import { strings } from './i18n';


const DeliveryCompanies = props => {
    const dispatch = useDispatch();
    const [termsAndConditionData, setTermsAndConditionData] = useState('');
    const [loading, setLoading] = useState(false);

    console.log(I18nManager.isRTL);
    useEffect(() => {

    }, []);

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                props.navigation.goBack()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Notifications')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    } else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Packages')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={{ width: '90%', alignItems: 'center', justifyContent: 'flex-start', alignSelf: 'center', height: screenHeight }}>

                        <Image source={require('../images/Group10500.png')} style={{ height: '20%', resizeMode: 'contain', width: '70%', marginTop: '40%' }} />
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: DarkGrey, alignSelf: 'flex-start', marginVertical: '5%', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>
                            {strings('lang.message13')}
                        </Text>

                        <View
                            style={{
                                width: '100%',
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignSelf: 'center',
                                alignItems: 'center',
                                marginTop: screenHeight / 80,
                                height: screenHeight / 10
                            }}
                        >
                            <Button
                                onPress={() => { props.navigation.push('DeliveryData', {}) }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    alignSelf: 'center',
                                    width: '95%',
                                    height: screenHeight / 18,
                                    backgroundColor: DarkBlue,
                                    borderRadius: 100,
                                }}
                            >

                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 28,
                                        color: White,
                                    }}
                                >
                                    {strings('lang.start')}
                                </Text>
                            </Button>
                        </View>

                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >

            </View >
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white',
    },
    section: { width: '90%', alignSelf: 'center' },
    labelContanier: {
        width: '100%',
        marginVertical: '3%',
        alignItems: 'flex-start',
    },
    textContanier: {
        width: '100%',
        marginVertical: '2%',
        alignItems: 'flex-start',
    },
    label: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 20, marginTop: screenHeight / 50 },
    body: {
        fontFamily: appFontBold,
        color: DarkGrey,
        fontSize: screenWidth / 30,
        alignSelf: I18nManager.isRTL ? 'flex-start' : 'flex-end',
    },
});

export default DeliveryCompanies;
