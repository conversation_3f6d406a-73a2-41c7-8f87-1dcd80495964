import React, { Component, useEffect, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    ActivityIndicator,
    RefreshControl,
    FlatList
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    MediumGreen,
    appColor1,
    Grey,
    DarkYellow,
    DarkBlue,
} from '../components/Styles';
import { Button, DatePicker } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import FooterDriver from '../components/FooterDriver';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import SelectDropdown from 'react-native-select-dropdown';
import DateTimePicker from 'react-native-modal-datetime-picker';
import * as Progress from 'react-native-progress';
import CircularProgress from 'react-native-circular-progress-indicator';
import { useRef } from 'react';
import StarRating from 'react-native-star-rating';
import * as driverProfileActions from '../../Store/Actions/driverProfile';



const Rateing = props => {

    const dispatch = useDispatch();

    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);

    const [noResault, setNoResault] = useState('');
    const [review, setReview] = useState({});
    const [reviews, setReviews] = useState({});
    const [reviewsItems, setReviewsItems] = useState({});
    const [page, setPage] = useState(1);
    const [lastPage, setLastPage] = useState(0);
    const [Reload, setReload] = useState(false)



    useEffect(() => {

        const getReviews = async () => {
            setLoading(true)
            try {
                let response = await dispatch(driverProfileActions.getReviews(1));
                if (response.success == true) {
                    setReview(response.data)
                    setReviews(response.data.reviews)
                    if (response.data.reviews.items.length == 0) {
                        setNoResault(strings('lang.No_Results'));
                        setReviewsItems([])
                    }
                    else {
                        setNoResault('')
                        setReviewsItems(response.data.reviews.items)
                        setLastPage(response.data.reviews.last_page)
                    }
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getReviews();

    }, [Reload]);

    const onRefresh = async () => {
        setLoading(true)
        try {
            let response = await dispatch(driverProfileActions.getReviews(1));
            if (response.success == true) {
                setReview(response.data)
                setReviews(response.data.reviews)
                if (response.data.reviews.items.length == 0) {
                    setNoResault(strings('lang.No_Results'));
                    setReviewsItems([])
                }
                else {
                    setNoResault('')
                    setReviewsItems(response.data.reviews.items)
                    setLastPage(response.data.reviews.last_page)
                    setPage(1)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    }

    const LoadMore = async () => {
        console.log('page', page);
        console.log('lastpage', lastPage);
        if (page < lastPage) {
            setLoadingMore(true)
            try {
                let response = await dispatch(driverProfileActions.getReviews(page + 1));
                if (response.success == true) {
                    setReviewsItems([...reviewsItems, ...response.data.reviews.items])
                    setPage(page + 1);
                    setNoResault('')
                    setLoadingMore(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingMore(false);
                }
            } catch (err) {
                setLoadingMore(false);
            }

        }
        else {
        }
    }


    if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Rateing')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <View style={{ width: '100%', height: screenHeight / 1, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                    <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                        <View style={{ width: '100%', height: screenHeight / 1, backgroundColor: WhiteGery, }} />
                    </SkeletonPlaceholder>
                </View>

            </View>
        );

    } else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Rateing')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                {/* <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                > */}
                <View style={styles.container}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-evenly', width: '25%', }}>
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: MediumGreen }}>{review.trips_count}</Text>
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: Black }}>{strings('lang.Trips')}</Text>
                    </View>

                    <View style={{ width: '1%', height: '70%', backgroundColor: MediumGrey, alignSelf: 'center' }}></View>

                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-evenly', width: '25%', }}>
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: MediumGreen }}>{review.reviews_count}</Text>
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: Black }}>{strings('lang.audit')}</Text>
                    </View>
                </View>

                <View style={styles.rateContainer}>
                    {/* <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                        <Progress.Circle
                            allowFontScaling={true}
                            unfilledColor={MediumGrey}
                            color={DarkYellow}
                            thickness={screenWidth / 40}
                            borderColor={'#c6cdd9'}
                            borderWidth={0}
                            animated={true}
                            indeterminate={false}
                            size={screenWidth / 5}
                            showsText={true}
                            progress={0.75}
                            textStyle={{ fontSize: screenWidth / 28, color: Black, fontFamily: appFontBold }}
                            formatText={() => {
                                return 'متوسط'
                            }}
                        />
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 33, color: Black, marginTop: 5 }}>{strings('lang.reviews')}</Text>
                    </View>

                    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                        <Progress.Circle
                            allowFontScaling={true}
                            unfilledColor={MediumGrey}
                            color={DarkYellow}
                            thickness={screenWidth / 40}
                            borderColor={'#c6cdd9'}
                            borderWidth={0}
                            animated={true}
                            indeterminate={false}
                            size={screenWidth / 5}
                            showsText={true}
                            progress={0.75}
                            textStyle={{ fontSize: screenWidth / 28, color: Black, fontFamily: appFontBold }}
                            formatText={() => {
                                return 'جديد'
                            }}
                        />
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 33, color: Black, marginTop: 5 }}>{strings('lang.Experience')}</Text>
                    </View>

                    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                        <Progress.Circle
                            allowFontScaling={true}
                            unfilledColor={MediumGrey}
                            color={DarkYellow}
                            thickness={screenWidth / 40}
                            borderColor={'#c6cdd9'}
                            borderWidth={0}
                            animated={true}
                            indeterminate={false}
                            size={screenWidth / 5}
                            showsText={true}
                            progress={0.75}
                            textStyle={{ fontSize: screenWidth / 28, color: Black, fontFamily: appFontBold }}
                            formatText={() => {
                                return 'منخفض'
                            }}
                        />
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 33, color: Black, marginTop: 5 }}>{strings('lang.repeataction')}</Text>
                    </View> */}
                    <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: Black }}>{strings('lang.Totalrating')}</Text>
                    <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: Black }}>{review.average_rating}</Text>
                    <StarRating
                        disabled={true}
                        maxStars={5}
                        starSize={screenHeight / 50}
                        starStyle={{ marginRight: 5, transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }}
                        rating={review.average_rating}
                        fullStarColor={DarkYellow}
                        containerStyle={{ alignSelf: 'flex-start' }}

                    />

                </View>

                {
                    noResault ? (
                        <Text
                            style={{
                                fontSize: screenWidth / 18,
                                fontFamily: appFontBold,
                                color: Red,
                                marginTop: screenHeight / 5,
                                alignSelf: 'center',
                            }}
                        >
                            {noResault}
                        </Text>
                    ) : (
                        <View></View>
                    )
                }

                <FlatList
                    data={reviewsItems}
                    refreshControl={
                        <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                    }
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item, key }) =>
                        <View style={styles.users}>
                            <View style={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', width: '100%', }}>
                                <View style={{ width: '15%', alignItems: 'center', justifyContent: 'center' }}>
                                    <Image source={require('../images/Group-39.png')} style={{ resizeMode: 'contain', height: screenWidth / 10, width: screenWidth / 10 }} />
                                </View>
                                <View style={{ width: '100%', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '85%', paddingEnd: '2.5%' }}>
                                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: Black, alignSelf: 'flex-start' }}>{item.reviewer.name}</Text>
                                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 33, color: Black, alignSelf: 'flex-start', }}>{item.reviewer.created_at.slice(0, 10)}</Text>
                                    </View>
                                    <View style={{ width: '85%', paddingEnd: '2.5%' }}>
                                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: Black, alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{item.comment}</Text>
                                    </View>
                                    <StarRating
                                        disabled={true}
                                        maxStars={5}
                                        starSize={screenHeight / 50}
                                        starStyle={{ marginEnd: 2, }}
                                        rating={item.rating}
                                        fullStarColor={DarkYellow}
                                    />
                                </View>
                            </View>
                        </View>
                    }
                    // keyExtractor={item => item.id}
                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth / 1.05, height: screenHeight / 1.8 }}
                    // onEndReachedThreshold={0.1}
                    onEndReached={() => LoadMore()}
                />
                {loadingMore ? <ActivityIndicator size={Platform.OS == 'ios' ? 80 : 40} color={DarkBlue} /> : <></>}



                {/* </ScrollView> */}


                {/* <View style={{ height: screenHeight / 8 }}></View> */}
                <FooterDriver navigation={props.navigation} current={'Rateing'} />
            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'space-evenly',
        width: '90%',
        height: screenHeight / 20,
        backgroundColor: WhiteGery,
        marginBottom: '5%'
    },
    rateContainer: {
        alignItems: 'flex-start',
        alignSelf: 'center',
        justifyContent: 'center',
        width: '85%',
        height: screenHeight / 10,
        marginBottom: '5%',
    },
    users: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        alignSelf: 'center',
        justifyContent: 'flex-start',
        width: '90%',
        marginVertical: screenHeight / 150,
        // minHeight: screenHeight / 10,
        borderWidth: 1,
        borderColor: MediumGrey,
        borderRadius: 10,
        paddingVertical: 5,
        overflow: 'hidden'
    },
});

export default Rateing;
