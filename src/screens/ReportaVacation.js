import React, { Component, useEffect, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    MediumGreen,
    appColor1,
    Grey,
    DarkYellow,
    DarkBlue,
    WhiteRed,
    RedDark,
    MediumRed,
} from '../components/Styles';
import { Button, DatePicker, Input } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import moment from 'moment';
import * as vacationsActions from '../../Store/Actions/vacations';

const ReportaVacation = props => {
    const dispatch = useDispatch();
    const [termsAndConditionData, setTermsAndConditionData] = useState('');
    const [loading, setLoading] = useState(false);
    const [code, setCode] = useState('');
    const [activeText, setActiveText] = useState(0);
    const [date, setDate] = useState('');
    const [showDate, setShowDate] = useState('');
    const [dateTo, setDateTo] = useState('');
    const [showDateTo, setShowDateTo] = useState('');




    const createVacation = async () => {
        try {
            setLoading(true)
            let response = await dispatch(vacationsActions.createVacation(
                props.route.params.item.id,
                date.slice(0, 10),
                dateTo.slice(0, 10)
            ));

            if (response.success == true) {
                setLoading(false)
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.StausChangedSuccessfully'),
                    White,
                    1500,
                    screenHeight / 15,
                );
                props.navigation.navigate('Home');
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoading(false)
            }
        } catch (err) {
            console.log(err);
            setLoading(false)
        }
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Notifications')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    } else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Reportavacation')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <View style={{ height: screenHeight / 6, width: '95%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', marginVertical: '15%' }}>
                    <Image source={require('../images/Group10600.png')} style={{ resizeMode: 'contain', width: '50%', height: '70%', }} />
                </View>
                <View style={{ width: '90%', alignSelf: 'center' }}>
                    <Text
                        style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 30, alignSelf: 'flex-start' }}>
                        {strings('lang.Thebeginningofthevacation')}
                    </Text>
                    <View style={styles.input}>
                        <View style={{ width: '80%', height: '100%', alignItems: 'center', flexDirection: 'row' }}>
                            <Text style={{ fontFamily: appFontBold, color: MediumGrey, fontSize: screenWidth / 33, marginEnd: 5 }}>{strings('lang.Date')}</Text>
                            <Text style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 33, marginEnd: 5 }}>{date.slice(0, 10)}</Text>
                            <DateTimePickerModal
                                isVisible={showDate}
                                mode="date"
                                onConfirm={
                                    (selectedDate) => {
                                        console.log('selectedDate', selectedDate)
                                        const currentDate = selectedDate || date;
                                        setDate(currentDate);
                                        console.log('currentDateTime', moment(currentDate).format())
                                        setShowDate(false)
                                        setDate(moment(currentDate).format());
                                    }
                                }
                                onCancel={() => { setShowDate(false) }}
                            />
                        </View>
                        <Pressable onPress={() => { setShowDate(true) }} style={styles.selectContainer}>
                            <Text style={styles.text}>
                                تحديد
                            </Text>
                        </Pressable>
                    </View>

                    <Text
                        style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 30, alignSelf: 'flex-start', marginTop: '5%' }}>
                        {strings('lang.Theendofthevacation')}
                    </Text>
                    <View style={styles.input}>
                        <View style={{ width: '80%', height: '100%', alignItems: 'center', flexDirection: 'row' }}>
                            <Text style={{ fontFamily: appFontBold, color: MediumGrey, fontSize: screenWidth / 33, marginEnd: 5 }}>{strings('lang.Date')}</Text>
                            <Text style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 33, marginEnd: 5 }}>{dateTo.slice(0, 10)}</Text>
                            <DateTimePickerModal
                                isVisible={showDateTo}
                                mode="date"
                                onConfirm={
                                    (selectedDate) => {
                                        console.log('selectedDate', selectedDate)
                                        const currentDate = selectedDate || dateTo;
                                        setDateTo(currentDate);
                                        console.log('currentDateTime', moment(currentDate).format())
                                        setShowDateTo(false)
                                        setDateTo(moment(currentDate).format());
                                    }
                                }
                                onCancel={() => { setShowDate(false) }}
                            />
                        </View>
                        <Pressable onPress={() => { setShowDateTo(true) }} style={styles.selectContainer}>
                            <Text style={styles.text}>
                                تحديد
                            </Text>
                        </Pressable>
                    </View>

                    <View style={{
                        width: '100%',
                        height: screenHeight / 25,
                        backgroundColor: WhiteRed,
                        marginVertical: screenHeight / 20,
                        borderRadius: 20,
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}>
                        <Text style={{ color: MediumRed, fontFamily: appFont, fontSize: screenWidth / 35 }}>
                            {strings('lang.messageStep')}
                        </Text>
                    </View>

                    <Button onPress={() => { createVacation() }} transparent
                        style={{
                            width: '100%', height: screenHeight / 18,
                            alignSelf: 'center', justifyContent: 'center', alignItems: 'center',
                            backgroundColor: DarkBlue, borderRadius: 25, marginTop: screenHeight / 20
                        }}>
                        <Text style={{ fontFamily: appFontBold, color: White, fontSize: screenWidth / 25, }}>{strings('lang.Report').toUpperCase()}</Text>
                    </Button>

                </View>



                <View style={{ height: screenHeight / 20 }}></View>
            </View>
        );
    }
};

const styles = StyleSheet.create({
    input: {
        height: screenHeight / 20,
        borderRadius: 20,
        width: '100%',
        backgroundColor: White,
        borderWidth: .8,
        flexDirection: 'row',
        paddingStart: '2%',
        borderColor: MediumGrey,
    },
    activeInput: {
        borderColor: DarkBlue,
        borderWidth: .8,
        height: screenHeight / 20,
        borderRadius: 20,
        width: '100%',
        flexDirection: 'row',
        paddingStart: '2%',
        backgroundColor: White,
    },
    inputText: {
        color: Black,
        width: '100%',
        fontFamily: appFontBold,
        fontSize: screenWidth / 33,
        alignSelf: 'center',
        textAlign: I18nManager.isRTL ? 'right' : 'left',
    },
    selectContainer: {
        // backgroundColor: White,
        width: screenWidth / 8,
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        end: 0
    },
    text: { fontFamily: appFontBold, color: DarkBlue, fontSize: screenWidth / 40, textDecorationLine: 'underline' },

});

export default ReportaVacation;
