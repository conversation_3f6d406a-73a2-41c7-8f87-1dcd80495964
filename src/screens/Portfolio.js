import React, { Component, useEffect, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    ActivityIndicator,
    RefreshControl,
    FlatList,
    ScrollView
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    MediumGreen,
    appColor1,
    Grey,
    DarkYellow,
    DarkBlue,
} from '../components/Styles';
import { Button, Container, DatePicker } from 'native-base';
import Header from '../components/Header';
import { strings } from './i18n';
import FooterDriver from '../components/FooterDriver';
import Toaster from '../components/Toaster';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import SelectDropdown from 'react-native-select-dropdown';
import DateTimePicker from 'react-native-modal-datetime-picker';
import * as Progress from 'react-native-progress';
import CircularProgress from 'react-native-circular-progress-indicator';
import { useRef } from 'react';
import StarRating from 'react-native-star-rating';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import * as walletActions from '../../Store/Actions/wallet';
import EarningContainer from '../components/EarningContainer';
import PortfolioContainer from '../components/PortfolioContainer';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RBSheet from 'react-native-raw-bottom-sheet';
import { TextInput } from 'react-native-gesture-handler';
import { Modalize } from 'react-native-modalize';



const Portfolio = props => {
    const dispatch = useDispatch();
    const [WalletTransactions, setWalletTransactions] = useState([]);
    const [wallet, setWallet] = useState({});
    const [payouts, setPayouts] = useState({});
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [loadingMore1, setLoadingMore1] = useState(false);
    const [page, setPage] = useState(1);
    const [lastPage, setLastPage] = useState(0);
    const [page1, setPage1] = useState(1);
    const [lastPage1, setLastPage1] = useState(0);
    const [total, setTotal] = useState(0);
    const [categories, setcategories] = useState([
        {
            id: 1,
            name: strings('lang.Shippingoperations')
        },
        {
            id: 2,
            name: strings('lang.Dues')
        },

    ]);
    const [categoryId, setCategoryId] = useState(1);
    const [noResault, setNoResault] = useState('');
    const [price, setPrice] = useState('');
    const [noResault1, setNoResault1] = useState('');
    const [Reload, setReload] = useState(false)
    const refRbSheet = useRef();
    const modalizeRef1 = useRef();
    const contentRef1 = useRef();
    const settings = useSelector(state => state.general.settings);

    console.log(I18nManager.isRTL);

    useEffect(() => {
        console.log('settings', settings);
        const getWallet = async () => {
            setLoading(true)
            try {
                let response = await dispatch(walletActions.getWallet());
                if (response.success == true) {
                    setWallet(response.data.wallet)
                    setPayouts(response.data.payouts)
                    setLoading(false);

                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoading(false);
                }
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }


        };
        const getWalletTransactions = async () => {
            setLoading(true)
            try {
                let response = await dispatch(walletActions.getWalletTransactions(1));
                if (response.success == true) {
                    if (response.data.items == 0) {
                        setNoResault(strings('lang.No_Results'));
                        setWalletTransactions([])
                    }
                    else {
                        setNoResault('')
                        setWalletTransactions(response.data.items)
                        setLastPage(response.data.last_page)
                        setPage(1)
                    }
                    setLoading(false);

                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }


        };

        getWallet();
        getWalletTransactions();
    }, [Reload]);

    const onRefresh = async () => {
        setLoading(true)
        try {
            let response = await dispatch(walletActions.getWalletTransactions(1));
            if (response.success == true) {
                if (response.data.items == 0) {
                    setNoResault(strings('lang.No_Results'));
                    setWalletTransactions([])
                }
                else {
                    setNoResault('')
                    setWalletTransactions(response.data.items)
                    setLastPage(response.data.last_page)
                    setPage(1)
                }
                setLoading(false);

            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const LoadMore = async () => {
        console.log('page', page);
        console.log('lastpage', lastPage);
        if (page < lastPage) {
            setLoadingMore(true)
            try {
                let response = await dispatch(walletActions.getWalletTransactions(page + 1));
                if (response.success == true) {
                    setWalletTransactions([...WalletTransactions, ...response.data.items])
                    setPage(page + 1);
                    setNoResault('')
                    setLoadingMore(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingMore(false);
                }
            } catch (err) {
                setLoadingMore(false);
            }

        }
        else {
        }
    }

    const minimum_wallet_charge = async (price) => {
        setLoadingMore(true)
        if (price >= settings.minimum_wallet_charge) {
            props.navigation.navigate('PaymentMethodsWallet', { price: price, walletId: wallet.id });
            setPrice('');
            modalizeRef1.current.close()
            setLoadingMore(false);

        }
        else {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.minimumWallet'),
                White,
                1500,
                screenHeight / 15,
            );
            setLoadingMore(false);
        }

    };

    if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Portfolio')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />


                <View style={{ width: '100%', height: screenHeight / 1, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                    <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                        <View style={{ width: '100%', height: screenHeight / 1, backgroundColor: WhiteGery, }} />
                    </SkeletonPlaceholder>
                </View>

            </View>
        );

    } else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Portfolio')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />
                <Container  >

                    <View  >

                        <View style={{
                            height: screenHeight / 8, width: '90%', alignSelf: 'center',
                            flexDirection: 'row', justifyContent: 'space-between',
                            marginVertical: '5%'
                        }}>
                            <View style={{
                                height: '100%', width: '40%', alignItems: 'flex-start',
                                justifyContent: 'center',

                            }}>
                                <Text style={styles.activeLabel}>{strings('lang.Walletbalance')}</Text>
                                <Text style={[styles.activeLabel, {
                                    color: DarkYellow, fontSize: screenWidth / 10
                                }]}>{`${wallet.balance} ${strings('lang.SAR')}`}</Text>
                            </View>

                            <View style={{
                                height: '100%', width: '50%', alignItems: 'center',
                                justifyContent: 'flex-end',
                                paddingBottom: '4%'
                            }}>
                                <TouchableOpacity
                                    onPress={() => { modalizeRef1.current.open() }}
                                    style={{
                                        width: '100%',
                                        height: screenHeight / 20, borderRadius: 25,
                                        backgroundColor: DarkBlue, alignItems: 'center', justifyContent: 'center'
                                    }}
                                >
                                    <Text style={[styles.activeLabel, {
                                        color: White, fontSize: screenWidth / 25
                                    }]}>{strings('lang.Walletcharging')}</Text>
                                </TouchableOpacity>
                            </View>

                        </View>



                        <View style={{ height: screenHeight / 20, width: '100%', flexDirection: 'row', justifyContent: 'space-evenly', backgroundColor: WhiteGery, marginBottom: '2%' }}>
                            {categories.map((item, index) => {
                                return (
                                    <Pressable
                                        onPress={() => { setCategoryId(item.id) }}
                                        style={item.id == categoryId ? styles.activeButton : styles.Button}
                                    >
                                        <Text style={item.id == categoryId ? styles.activeLabel : styles.label}>{item.name}</Text>
                                    </Pressable>
                                )
                            })
                            }
                        </View>

                        {categoryId == 1
                            &&
                            <>
                                {
                                    noResault ? (
                                        <Text
                                            style={{
                                                fontSize: screenWidth / 18,
                                                fontFamily: appFontBold,
                                                color: Red,
                                                marginTop: screenHeight / 5,
                                                alignSelf: 'center',
                                            }}
                                        >
                                            {noResault}
                                        </Text>
                                    ) : (
                                        <View></View>
                                    )
                                }

                                <FlatList
                                    data={WalletTransactions}
                                    refreshControl={
                                        <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                                    }
                                    showsVerticalScrollIndicator={false}
                                    renderItem={({ item, key }) =>
                                        <PortfolioContainer item={item} />
                                    }
                                    keyExtractor={item => item.id}
                                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth / 1.05, height: screenHeight / 1.8 }}
                                    onEndReachedThreshold={0.1}
                                    onEndReached={() => LoadMore()}
                                />
                                {loadingMore ? <ActivityIndicator /> : <></>}

                            </>
                        }
                        {categoryId == 2
                            &&
                            <>

                                <View style={{
                                    height: screenHeight / 6, width: '95%', alignSelf: 'center', flexDirection: 'row',
                                    justifyContent: 'space-around', alignItems: 'center',
                                }}>

                                    <View style={styles.priceContainer}>
                                        <Text
                                            style={{
                                                fontSize: screenWidth / 20,
                                                fontFamily: appFontBold,
                                                color: MediumGreen,
                                            }}
                                        >
                                            {`${payouts.balance} ${strings('lang.SAR')}`}
                                        </Text>
                                        <Text
                                            style={{
                                                fontSize: screenWidth / 40,
                                                fontFamily: appFontBold,
                                                color: MediumGrey,

                                            }}
                                        >
                                            {strings('lang.Totalbalance')}
                                        </Text>
                                    </View>

                                    <View style={{ width: '.5%', height: '70%', backgroundColor: MediumGrey }}></View>


                                    <View style={styles.priceContainer}>
                                        <Text
                                            style={{
                                                fontSize: screenWidth / 20,
                                                fontFamily: appFontBold,
                                                color: Red,
                                            }}
                                        >
                                            {`${payouts.commission} ${strings('lang.SAR')}`}
                                        </Text>
                                        <Text
                                            style={{
                                                fontSize: screenWidth / 40,
                                                fontFamily: appFontBold,
                                                color: MediumGrey,

                                            }}
                                        >
                                            {strings('lang.Companycommission')}
                                        </Text>
                                    </View>

                                </View>


                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('ReceivablesDetails', { payouts: payouts }) }}
                                    style={{
                                        width: '90%', marginTop: 20,
                                        height: screenHeight / 20, borderRadius: 25,
                                        backgroundColor: DarkYellow, alignItems: 'center', alignSelf: 'center',
                                        justifyContent: 'center'
                                    }}
                                >
                                    <Text style={[styles.activeLabel, {
                                        color: White, fontSize: screenWidth / 25
                                    }]}>{strings('lang.Receivablesdetails')}</Text>
                                </TouchableOpacity>
                            </>
                        }



                    </View>
                </Container>


                <Modalize
                    ref={modalizeRef1}
                    contentRef={contentRef1}
                    disableScrollIfPossible={true}
                    modalHeight={screenHeight / 1.8}
                    // modalTopOffset={screenHeight / 1}
                    // alwaysOpen={screenHeight / 2}
                    // snapPoint={100}
                    handlePosition={'inside'}
                    // adjustToContentHeight={true}
                    // useNativeDriver={false}
                    panGestureEnabled={false}
                    keyboardAvoidingBehavior={'padding'}
                    // dragToss={0.05}
                    // threshold={150}
                    // velocity={2800}
                    // withReactModal={false}
                    withOverlay={false}
                    withHandle={true}
                    scrollViewProps={screenHeight}
                    modalStyle={styles.modalize__content}
                    handleStyle={styles.handle__shape}
                // closeOnOverlayTap={true}
                // tapGestureEnabled={false}
                // panGestureComponentEnabled={true}
                // closeSnapPointStraightEnabled={false}
                >
                    <ScrollView
                        style={{ width: '100%', alignSelf: 'center', paddingVertical: '3%' }}
                        showsVerticalScrollIndicator={false}
                        keyboardShouldPersistTaps='always'
                    >


                        <View style={{ width: '95%', alignSelf: 'center', height: screenHeight / 2, marginTop: screenHeight / 60 }}>

                            <View style={styles.contentContainer}>

                                <View style={[styles.textInput, { height: screenHeight / 14 }]}>
                                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', width: '100%' }}>
                                        <TextInput
                                            autoFocus={true}
                                            onChangeText={text => { setPrice(text) }}
                                            value={price}
                                            keyboardType={'number-pad'}
                                            placeholderTextColor={Black}
                                            placeholder={price}
                                            style={{
                                                alignItems: 'center', justifyContent: 'center', textAlignVertical: 'center',
                                                fontFamily: appFont,
                                                fontSize: screenWidth / 20,
                                                textAlign: I18nManager.isRTL ? 'right' : 'left', color: Black, width: '80%',
                                            }}
                                        />
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 20, color: Black }} > {strings('lang.SR')} </Text>
                                    </View>
                                </View>

                            </View>

                            <View style={{ height: 1, width: '100%', backgroundColor: MediumGrey, marginVertical: screenHeight / 50 }}></View>
                            <View
                                style={{
                                    width: '100%',
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    alignSelf: 'center',
                                    flexDirection: 'row'
                                }}
                            >
                                <Button
                                    disabled={price ? false : true}
                                    onPress={() => {
                                        minimum_wallet_charge(price)
                                    }}
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: '60%',
                                        height: screenHeight / 18,
                                        backgroundColor: DarkBlue,
                                        opacity: price ? 1 : .5,
                                        borderRadius: 100,
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontFamily: appFontBold,
                                            fontSize: screenWidth / 28,
                                            color: White,
                                        }}
                                    >
                                        {strings('lang.Confirm')}
                                    </Text>
                                </Button>
                                <Button
                                    onPress={() => { setPrice(''); modalizeRef1.current.close() }}
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: '30%',
                                        height: screenHeight / 18,
                                        backgroundColor: WhiteGery,
                                        borderRadius: 100,
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontFamily: appFontBold,
                                            fontSize: screenWidth / 28,
                                            color: Black,
                                        }}
                                    >
                                        {strings('lang.Close')}
                                    </Text>
                                </Button>
                            </View>
                        </View>

                    </ScrollView>
                </Modalize>

                <View style={{ height: screenHeight / 80 }}></View>
                <FooterDriver navigation={props.navigation} current={'myearnings'} />
            </View >
        );
    }
};

const styles = StyleSheet.create({
    textInput: {
        width: '100%',
        flexDirection: 'row',
        alignSelf: 'center',
        borderRadius: 100,
        borderColor: MediumGrey,
        borderWidth: 1,
        height: '100%',
        color: Black,
        alignItems: 'center',
        justifyContent: 'space-between',
        textAlignVertical: 'center',
        fontFamily: appFont,
        fontSize: screenWidth / 32,
        textAlign: I18nManager.isRTL ? 'right' : 'left',
        paddingHorizontal: 10, overflow: 'hidden'
    },
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'space-evenly',
        width: '90%',
        height: screenHeight / 20,
        backgroundColor: WhiteGery,
        marginBottom: '5%'
    },
    rateContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'space-between',
        width: '90%',
        height: screenHeight / 7,
        marginBottom: '5%'
    },
    users: {
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'space-between',
        width: '90%',
        maxHeight: screenHeight / 7,
        marginVertical: '2%',
        borderWidth: 1,
        borderColor: MediumGrey,
        borderRadius: 5
    },
    priceContainer: {
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'center',
        width: '30%',

    },
    activeButton: { height: '95%', width: '40%', alignItems: "center", justifyContent: "center", alignSelf: 'center', borderBottomColor: DarkBlue, borderBottomWidth: 3, borderRadius: 2 },
    activeLabel: { fontFamily: appFontBold, fontSize: screenWidth / 32, color: DarkBlue, },
    Button: { height: '95%', width: '40%', alignItems: "center", justifyContent: "center", alignSelf: 'center', },
    label: { fontFamily: appFontBold, fontSize: screenWidth / 32, color: Black, },
});

export default Portfolio;
