
import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useEffect, useState } from 'react';
import { Text, View, Image, StyleSheet, I18nManager, ImageBackground, Platform } from 'react-native'
import I18n from 'react-native-i18n';
import { useDispatch } from 'react-redux';

import { appColor1, screenHeight, screenWidth } from '../components/Styles'
import * as authActions from '../../Store/Actions/auth';
import * as dailyTripsActions from '../../Store/Actions/dailyTrips';
import * as profileActions from '../../Store/Actions/profile';
import RNRestart from 'react-native-restart';
import * as driverProfileActions from '../../Store/Actions/driverProfile';
import * as generalActions from '../../Store/Actions/general';


const Splash = props => {
  const [appStatus, setAppStatus] = useState({});
  const dispatch = useDispatch();

  const logout = async () => {
    dispatch(authActions.logout());
    await AsyncStorage.removeItem('token')
    await AsyncStorage.removeItem('userType')
    await AsyncStorage.removeItem('id')
    await AsyncStorage.removeItem('name')
    await AsyncStorage.removeItem('email')
    await AsyncStorage.removeItem('image')
    await AsyncStorage.removeItem('dial_code')
    await AsyncStorage.removeItem('phone')
    await AsyncStorage.removeItem('gender')
    props.navigation.navigate('Login')
  }

  useEffect(() => {




    async function GetInitalData() {
      let AndroidVersion = 1;
      let IosVersion = 1;


      // try {
      // let response = await dispatch(GeneralAction.applicationStatus());

      // if (response.status == 200) {
      //   setAppStatus(response.data)
      // }


      // if (response.data.isUpdateRequired == true || response.data.isMaintenance == true) {
      //   props.navigation.navigate('RequiredUpadte', { appStatus: response.data })
      // }
      // else {
      let token = await AsyncStorage.getItem('token')
      let FcmToken = await AsyncStorage.getItem('FcmToken')
      let userType = await AsyncStorage.getItem('userType')
      let email = await AsyncStorage.getItem('email')
      let lan = await AsyncStorage.getItem('lan')
      let intro = await AsyncStorage.getItem('intro')
      let pendingTrip = {};
      let pendingTripType = '';
      let driverPendingTrip = {};
      let driverPendingType = '';
      let driverPendingOffer = {};
      console.log('token', token);

      let responsee = await dispatch(generalActions.getSplash());
      console.log('responsee', responsee);

      console.log('asd',);

      console.log('1', AndroidVersion);
      console.log('2', responsee.data.settings.ios_version);
      console.log('3', JSON.parse(responsee.data.settings.ios_version));
      if (Platform.OS == 'ios') {
        if (responsee.data.settings.ios_version && IosVersion < JSON.parse(responsee.data.settings.ios_version)) {
          props.navigation.navigate('RequiredUpadte')
          return;
        }
      }
      else if (Platform.OS == 'android') {
        if (responsee.data.settings.android_version && AndroidVersion < JSON.parse(responsee.data.settings.android_version)) {
          props.navigation.navigate('RequiredUpadte')
          return;
        }
      }

      if (token && userType == '1') {

        let addFcmToken = await dispatch(generalActions.addFcmToken(FcmToken));
        console.log('addFcmToken', addFcmToken);
        let response = await dispatch(profileActions.getPendingTrip());
        if (response.success == true) {
          pendingTrip = response.data.trip;
          pendingTripType = response.data.type;
          console.log('response.data', response.data);
        }
        else if (response.message == "Unauthenticated.") {
          logout()
        }
      }

      if (token && userType == '2') {

        let addFcmToken = await dispatch(generalActions.addFcmToken(FcmToken));
        console.log('addFcmToken', addFcmToken);
        let response = await dispatch(driverProfileActions.getPendingTrip());
        if (response.success == true) {
          driverPendingTrip = response.data;
          driverPendingType = response.data.type;
          console.log('response.data', response.data);
        }
        else if (response.message == "Unauthenticated.") {
          logout()
        }
        let response1 = await dispatch(driverProfileActions.getPendingOffer());
        if (response1.success == true) {
          driverPendingOffer = response1.data;
          console.log('response1.data', response1.data);
        }
        else if (response.message == "Unauthenticated.") {
          logout()
        }
      }


      if (lan) {
        if (lan == 'ar') {
          I18n.locale = "ar";
          I18nManager.forceRTL(true);
          I18nManager.allowRTL(true);
          if (token) {
            if (userType == '2') {
              if (driverPendingTrip.trip != null || driverPendingOffer != null) {
                props.navigation.navigate('MapDriver', {
                  screen: 'DriverRequests', pending: true, driverPendingTrip: driverPendingTrip,
                  driverPendingOffer: driverPendingOffer,
                  request: driverPendingType == "DailyTrip" || driverPendingOffer ? 1 : driverPendingType == "VipTrip" ? 3
                    : driverPendingType == "CityTrip" ? 4 : 2,
                  trip: driverPendingOffer ? driverPendingOffer : driverPendingTrip.trip
                })
              } else {
                props.navigation.navigate('DriverRequests')
              }
            }
            else {
              if (email && email != '') {
                if (pendingTrip) {
                  if (pendingTripType == "DailyTrip") {
                    props.navigation.navigate('MapAddress', { screen: 'Home', cityTrip: false, pending: true, pendingTrip: pendingTrip })
                  }
                  else if (pendingTripType == "VipTrip") {
                    props.navigation.navigate('DetailsTrip', { item: pendingTrip, type: 'orderVip' })
                  }
                  else if (pendingTripType == "CityTrip") {
                    props.navigation.navigate('DetailsTrip', { item: pendingTrip, type: 'betweenCities' })
                  }
                  else if (pendingTripType == "UserPackage") {
                    props.navigation.navigate('DetailsTrip', { item: pendingTrip, type: 'packages' })
                  }
                  else {
                    props.navigation.navigate('Home')
                  }
                }
                else {
                  props.navigation.navigate('Home')
                }
              }
              else {
                logout()
              }
            }
          }
          else if (intro == "Done") {
            props.navigation.navigate('Login')
          }
          else {
            props.navigation.navigate('Intro1', { index: 0 })
          }
        }
        else {
          I18n.locale = "en";
          I18nManager.forceRTL(false);
          I18nManager.allowRTL(false);
          if (token) {
            if (userType == '2') {
              if (driverPendingTrip.trip != null || driverPendingOffer != null) {
                props.navigation.navigate('MapDriver', {
                  screen: 'DriverRequests', pending: true, driverPendingTrip: driverPendingTrip,
                  driverPendingOffer: driverPendingOffer,
                  request: driverPendingType == "DailyTrip" || driverPendingOffer ? 1 : driverPendingType == "VipTrip" ? 3
                    : driverPendingType == "CityTrip" ? 4 : 2,
                  trip: driverPendingTrip.trip
                })
              } else {
                props.navigation.navigate('DriverRequests')
              }
            }
            else {
              if (email && email != '') {
                if (pendingTrip) {
                  props.navigation.navigate('MapAddress', { screen: 'Home', cityTrip: false, pending: true, pendingTrip: pendingTrip })
                }
                else {
                  props.navigation.navigate('Home')
                }
              }
              else {
                logout()
              }
            }
          }
          else if (intro == "Done") {
            props.navigation.navigate('Login')
          }
          else {
            props.navigation.navigate('Intro1', { index: 0 })
          }
        }
      }
      else {
        await AsyncStorage.setItem('lan', 'ar')
        await AsyncStorage.setItem('lang', '0')
        I18n.locale = "ar";
        I18nManager.forceRTL(true);
        setTimeout(() => {
          console.log('Restarting app...');
          RNRestart.Restart();
        }, 100);
        if (token) {
          if (userType == '2') {
            if (driverPendingTrip.trip != null || driverPendingOffer != null) {
              props.navigation.navigate('MapDriver', {
                screen: 'DriverRequests', pending: true, driverPendingTrip: driverPendingTrip,
                driverPendingOffer: driverPendingOffer,
                request: driverPendingType == "DailyTrip" || driverPendingOffer ? 1 : driverPendingType == "VipTrip" ? 3
                  : driverPendingType == "CityTrip" ? 4 : 2,
                trip: driverPendingTrip.trip
              })
            } else {
              props.navigation.navigate('DriverRequests')
            }
          }
          else {
            if (email && email != '') {
              if (pendingTrip) {
                props.navigation.navigate('MapAddress', { screen: 'Home', cityTrip: false, pending: true, pendingTrip: pendingTrip })
              }
              else {
                props.navigation.navigate('Home')
              }
            }
            else {
              logout()
            }
          }
        }
        else if (intro == "Done") {
          props.navigation.navigate('Login')
        }
        else {
          props.navigation.navigate('Intro1', { index: 0 })
        }
      }
      // }

      // }
      // catch (err) {
      // }
    }

    setTimeout(() => {
      GetInitalData()
    }, 3000)


  }, []);

  return (
    <ImageBackground source={require('../images/splash2.gif')} style={{ width: screenWidth, height: '100%', resizeMode: 'cover', alignSelf: 'center', }} >
      {/* <ImageBackground source={require('../images/Rectangle17708.jpg')} style={{ width: screenWidth, height: '100%', resizeMode: 'cover', alignSelf: 'center', }} > */}
    </ImageBackground>
  );
};

const styles = StyleSheet.create({

});

export default Splash;
