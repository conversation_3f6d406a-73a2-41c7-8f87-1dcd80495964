import React, { Component, useEffect, useState } from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  StyleSheet,
  ScrollView,
  I18nManager,
  Pressable,
  PermissionsAndroid,
  BackHandler,
} from 'react-native';
import {
  appFont,
  appFontBold,
  Green,
  WhiteGreen,
  screenWidth,
  White,
  DarkGreen,
  Blue,
  MediumGrey,
  DarkGrey,
  WhiteGery,
  screenHeight,
  Black,
  DarkBlue,
  appColor2,
  appColor1,
  Red,
  Grey,
} from '../components/Styles';
import { Button, Textarea } from 'native-base';
import Header from '../components/Header';
// import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
// import Model from './Model';
// import Loading from '../components/Loading';
import { TextInput, TouchableOpacity, TouchableWithoutFeedback } from 'react-native-gesture-handler';
import Footer from '../components/Footer';
import { useDispatch, useSelector } from 'react-redux';
import * as authActions from '../../Store/Actions/auth';
import Toaster from '../components/Toaster';
import Loading from '../components/Loading';
import SelectDropdown from 'react-native-select-dropdown';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker'; // Migration from 2.x.x to 3.x.x => showImagePicker API is removed.
import ImageResizer from 'react-native-image-resizer';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import moment from 'moment';
import * as driverProfileActions from '../../Store/Actions/driverProfile';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';

const MyProfileDriver = props => {

  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);


  const [cover, setCover] = useState();
  const [coverUrl, setCoverUrl] = useState();
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [data, setData] = useState([
    {
      id: 0,
      name: strings('lang.personalinformation')
    },
    {
      id: 1,
      name: strings('lang.cardata')
    },
  ]);
  const [gender, setGender] = useState('male');
  const [dataId, setDataId] = useState(0);
  const [date, setDate] = useState('');
  const [showDate, setShowDate] = useState('');
  const [identityNumber, setIdentityNumber] = useState('');
  const [stcNumber, setStcNumber] = useState('');
  const [ibanNumber, setIbanNumber] = useState('');
  const [cities, setCities] = useState([]);
  const [vehicleOptions, setVehicleOptions] = useState([]);
  const [carBrands, setCarBrands] = useState([]);
  const [carBrandId, setCarBrandId] = useState(0);
  const [carBrand, setCarBrand] = useState({});
  const [carModels, setCarModels] = useState([]);
  const [carModelId, setCarModelId] = useState(0);
  const [carModel, setCarModel] = useState({});
  const [colors, setColors] = useState([]);
  const [colorId, setColorId] = useState(0);
  const [color, setColor] = useState({});
  const [years, setYears] = useState([]);
  const [yearId, setYearId] = useState(0);
  const [year, setYear] = useState({});

  const [city, setCity] = useState({});
  const [cityId, setCityId] = useState(0);
  const [areas, setAreas] = useState([]);
  const [area, setArea] = useState({});
  const [areaId, setAreaId] = useState(0);

  const [profile, setProfile] = useState({});
  const [imageId, setImageId] = useState();
  const [imageIdUrl, setImageIdUrl] = useState();
  const [imageLeadership, setImageLeadership] = useState();
  const [imageLeadershipUrl, setImageLeadershipUrl] = useState();
  const [imageCar, setImageCar] = useState();
  const [imageCarUrl, setImageCarUrl] = useState()

  const [sequence_number, setSequence_number] = useState('');
  const [plate_letter_right, setPlate_letter_right] = useState('');
  const [plate_letter_middle, setPlate_letter_middle] = useState('');
  const [plate_letter_left, setPlate_letter_left] = useState('');
  const [numberCar, setNumberCar] = useState('');
  const [imageCarFront, setImageCarFront] = useState();
  const [imageCarFrontUrl, setImageCarFrontUrl] = useState();
  const [imageCarBack, setImageCarBack] = useState();
  const [imageCarBackUrl, setImageCarBackUrl] = useState();
  const [imageCarRight, setImageCarRight] = useState();
  const [imageCarRightUrl, setImageCarRightUrl] = useState();
  const [imageCarLeft, setImageCarLeft] = useState();
  const [imageCarLeftUrl, setImageCarLeftUrl] = useState();
  const [imageCarInside, setImageCarInside] = useState();
  const [imageCarInsideUrl, setImageCarInsideUrl] = useState();
  const [activeData, setActiveData] = useState(false);
  const [activeData1, setActiveData1] = useState(false);
  const [genderId, setGenderId] = useState('1');

  const dispatch = useDispatch();

  const profilee = useSelector(state => state.driverProfile.driverProfile);
  console.log('profilee', profilee);

  const driverDaily_trip_typess = useSelector(state => state.general.driverDaily_trip_types);
  const [driverDaily_trip_types, setDriverDaily_trip_types] = useState(driverDaily_trip_typess);
  const [driverDaily_trip_typeId, setDriverDaily_trip_typeId] = useState(0);
  const [driverDaily_trip_typeName, setDriverDaily_trip_typeName] = useState('');
  const [err_driverDaily_trip_type, setErr_DriverDaily_trip_type] = useState('');

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        props.navigation.goBack()
        return true;
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );

  useEffect(() => {


    const GetInitalData = (cities, carBrands, colors, years) => {
      let city = cities.find(city => city.id === profilee.city_id);
      setProfile(profilee);
      setCoverUrl(profilee.image);
      setName(profilee.name)
      setPhone(profilee.mobile_number)
      setEmail(profilee.email)
      setGender(profilee.gender)
      setGenderId(profilee.gender == 'male' ? '1' : '2')
      setDate(profilee.date_of_birth_gregorian)
      setIdentityNumber(profilee.identity_number)
      setCityId(profilee.city_id)
      if (profilee.city_id) {
        setCity(city)
      }
      setAreaId(profilee.neighborhood_id)
      if (profilee.neighborhood_id) {
        setArea(city.neighborhoods.find(neighborhood => neighborhood.id === profilee.neighborhood_id))
      }
      if (profilee.vehicle.car_brand) {
        setCarBrand(profilee.vehicle.car_brand)
        setCarBrandId(profilee.vehicle.car_brand.id)
        console.log('carBrands', carBrands);
        console.log('profilee.vehicle.car_brand.id', profilee.vehicle.car_brand.id);
        let carBrand = carBrands.find(brand => brand.id === profilee.vehicle.car_brand.id)
        console.log('carBrand', carBrand);
        if (carBrand) {
          setCarModels(carBrand.models)
        }
      }
      if (profilee.vehicle.car_model) {
        setCarModel(profilee.vehicle.car_model)
        setCarModelId(profilee.vehicle.car_model.id)
      }
      if (profilee.vehicle.color) {
        setColorId(profilee.vehicle.color)
        setColor(colors.find(color => color.id === profilee.vehicle.color))
      }
      if (profilee.vehicle.year) {
        setYearId(profilee.vehicle.year)
        setYear(years.find(year => year.id === profilee.vehicle.year))
      }
      setImageIdUrl(profilee.national_id_image)
      setImageLeadershipUrl(profilee.driving_license_image)
      setImageCarUrl(profilee.car_license_image)
      setIbanNumber(profilee.iban_number)
      setStcNumber(profilee.stc_number)

      setSequence_number(profilee.vehicle.sequence_number)
      setPlate_letter_right(profilee.vehicle.plate_letter_right)
      setPlate_letter_middle(profilee.vehicle.plate_letter_middle)
      setPlate_letter_left(profilee.vehicle.plate_letter_left)
      setNumberCar(profilee.vehicle.plate_number)
      setImageCarFrontUrl(profilee.vehicle.front_image)
      setImageCarBackUrl(profilee.vehicle.back_image)
      setImageCarInsideUrl(profilee.vehicle.inside_image)
      setImageCarLeftUrl(profilee.vehicle.left_image)
      setImageCarRightUrl(profilee.vehicle.right_image)
      setDriverDaily_trip_typeId(profilee.vehicle.daily_trip_type_id)
      if (profilee.vehicle.daily_trip_type_id) {
        setDriverDaily_trip_typeName(driverDaily_trip_typess.find(item => item.id === profilee.vehicle.daily_trip_type_id).name)
        console.log('');
      }
    };

    const getCities = async () => {
      setLoading(true)
      try {
        let response = await dispatch(driverProfileActions.getCities());
        if (response.success == true) {
          setCities(response.data)
          getVehicleOptions(response.data)
        }
        else {
          if (response.message) {
            Toaster(
              'top',
              'danger',
              Red,
              response.message,
              White,
              1500,
              screenHeight / 15,
            );
          }
        }
        setLoading(false);
      }
      catch (err) {
        console.log('err', err)
        setLoading(false);
      }
    };

    const getVehicleOptions = async (cities) => {
      setLoading(true)
      try {
        let response = await dispatch(driverProfileActions.getVehicleOptions());
        if (response.success == true) {
          setVehicleOptions(response.data)
          setCarBrands(response.data.car_brands)
          setColors(response.data.colors)
          setYears(response.data.years)
          GetInitalData(cities, response.data.car_brands, response.data.colors, response.data.years);
        }
        else {
          if (response.message) {
            Toaster(
              'top',
              'danger',
              Red,
              response.message,
              White,
              1500,
              screenHeight / 15,
            );
          }
        }
        setLoading(false);
      }
      catch (err) {
        console.log('err', err)
        setLoading(false);
      }
    };

    getCities();

  }, [profilee]);


  const editProfile = async () => {
    if (dataId == 0) {
      setLoading(true)
      try {
        let response = await dispatch(driverProfileActions.updateProfile(
          name,
          phone,
          email,
          gender,
          date,
          identityNumber,
          cityId,
          areaId,
          imageId,
          cover,
          imageLeadership,
          imageCar,
          stcNumber,
          ibanNumber,
        ));

        if (response.data) {
          Toaster(
            'top',
            'success',
            DarkGreen,
            strings('lang.profileupdatedsuccessfully'),
            White,
            1500,
            screenHeight / 50,
          );
          await AsyncStorage.setItem("id", JSON.stringify(response.data.id));
          await AsyncStorage.setItem("name", response.data.name ? response.data.name : '');
          await AsyncStorage.setItem("email", response.data.email ? response.data.email : '');
          await AsyncStorage.setItem("image", response.data.image ? response.data.image : '');
          await AsyncStorage.setItem("dial_code", response.data.dial_code ? response.data.dial_code : '');
          await AsyncStorage.setItem("phone", response.data.mobile_number ? response.data.mobile_number : '');
          await AsyncStorage.setItem("gender", response.data.gender ? response.data.gender : '');
          setActiveData(false);
          setActiveData1(false)
          setLoading(false)
        } else {
          if (response.message) {
            Toaster(
              'top',
              'danger',
              Red,
              response.message,
              White,
              1500,
              screenHeight / 50,
            );
            console.log(response);
            setLoading(false)
          }
        }
      } catch (err) {
        console.log(err);
        setLoading(false)
      }
    }
    else {

    }
  };

  const editVehicle = async () => {
    if (dataId == 1) {
      if (carBrandId == 0 || carModelId == 0 || yearId == 0 || colorId == 0) {
        Toaster(
          'top',
          'danger',
          Red,
          strings('lang.pleaseCompleteData'),
          White,
          1500,
          screenHeight / 50,
        );
      }
      else {
        setLoading(true)
        try {
          let response = await dispatch(driverProfileActions.updateVehicle(
            sequence_number,
            plate_letter_right,
            plate_letter_left,
            plate_letter_middle,
            numberCar,
            imageCarFront,
            imageCarBack,
            imageCarRight,
            imageCarLeft,
            imageCarInside,
            driverDaily_trip_typeId,
            carBrandId,
            carModelId,
            yearId,
            colorId
          ));

          if (response.data) {
            Toaster(
              'top',
              'success',
              DarkGreen,
              strings('lang.profileupdatedsuccessfully'),
              White,
              1500,
              screenHeight / 50,
            );
            setActiveData(false);
            setActiveData1(false)
            setLoading(false)
          } else {
            if (response.message) {
              Toaster(
                'top',
                'danger',
                Red,
                response.message,
                White,
                1500,
                screenHeight / 50,
              );
              console.log(response);
              setLoading(false)
            }
          }
        } catch (err) {
          console.log(err);
          setLoading(false)
        }
      }
    }
    else {

    }
  };

  const chooseImagesCover = async () => {

    let options = {
      quality: 0.5,
      maxWidth: 500,
      maxheight: 400,
      title: 'Select Image',
      customButtons: [
        {
          name: 'customOptionKey',
          title: 'Choose Photo from Custom Option',
        },
      ],
      storageOptions: {
        skipBackup: true,
        path: 'images',
      },
    };
    launchImageLibrary(options, response => {
      console.log('Response = ', response);

      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.error) {
        console.log('ImagePicker Error: ', response.error);
      } else if (response.customButton) {
        console.log('User tapped custom button: ', response.customButton);
        alert(response.customButton);
      } else {
        // let cover = response.assets[0];
        // You can also display the image using data:
        // let source = {
        //   uri: 'data:image/jpeg;base64,' + response.data
        // };
        let cover = {
          uri: response.assets[0].uri,
          type: response.assets[0].type,
          name: response.assets[0].fileName,
          link: '',
        };

        setCover(cover);

      }
    });
  };


  const chooseImages = async (type) => {

    let options = {
      quality: 0.5,
      maxWidth: 500,
      maxheight: 400,
      title: 'Select Image',
      customButtons: [
        {
          name: 'customOptionKey',
          title: 'Choose Photo from Custom Option',
        },
      ],
      storageOptions: {
        skipBackup: true,
        path: 'images',
      },
    };
    launchImageLibrary(options, response => {
      console.log('Response = ', response);

      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.error) {
        console.log('ImagePicker Error: ', response.error);
      } else if (response.customButton) {
        console.log('User tapped custom button: ', response.customButton);
        alert(response.customButton);
      } else {
        // let cover = response.assets[0];
        // You can also display the image using data:
        // let source = {
        //   uri: 'data:image/jpeg;base64,' + response.data
        // };
        let cover = {
          uri: response.assets[0].uri,
          type: response.assets[0].type,
          name: response.assets[0].fileName,
          link: '',
        };
        switch (type) {
          case 'ID':
            setImageId(cover);
            break;
          case 'License':
            setImageLeadership(cover);
            break;
          case 'Car':
            setImageCar(cover);
            break;
          case 'Criminal':
            setImageCriminalStatus(cover);
            break;

          default:
            break;
        }
      }
    });
  };

  const chooseImagesCar = async (type) => {

    let options = {
      quality: 0.5,
      maxWidth: 500,
      maxheight: 400,
      title: 'Select Image',
      customButtons: [
        {
          name: 'customOptionKey',
          title: 'Choose Photo from Custom Option',
        },
      ],
      storageOptions: {
        skipBackup: true,
        path: 'images',
      },
    };
    launchImageLibrary(options, response => {
      console.log('Response = ', response);

      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.error) {
        console.log('ImagePicker Error: ', response.error);
      } else if (response.customButton) {
        console.log('User tapped custom button: ', response.customButton);
        alert(response.customButton);
      } else {
        // let cover = response.assets[0];
        // You can also display the image using data:
        // let source = {
        //   uri: 'data:image/jpeg;base64,' + response.data
        // };
        let cover = {
          uri: response.assets[0].uri,
          type: response.assets[0].type,
          name: response.assets[0].fileName,
          link: '',
        };
        switch (type) {
          case 'Front':
            setImageCarFront(cover);
            break;
          case 'Back':
            setImageCarBack(cover);
            break;
          case 'Right':
            setImageCarRight(cover);
            break;
          case 'Left':
            setImageCarLeft(cover);
            break;
          case 'Inside':
            setImageCarInside(cover);
            break;
          default:
            break;
        }
      }
    });
  };



  return (
    <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
      <Header
        title={strings('lang.MyAccount')}
        backPress={() => {
          props.navigation.goBack();
        }}
      />
      <ScrollView showsVerticalScrollIndicator={false} style={{ height: screenHeight / 1.2 }}>

        {loading
          ?
          <Loading />
          :
          <></>
        }

        <View
          style={{
            alignSelf: 'center',
            width: screenWidth,
            height: screenHeight / 6,
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
          }}
        >
          <TouchableOpacity onPress={() => { if (activeData) { chooseImagesCover() } }} style={{ width: screenWidth / 4, height: screenWidth / 4, alignSelf: 'center', borderRadius: screenWidth / 8, overflow: 'hidden' }}>
            <Image
              source={cover ? { uri: cover.uri } : coverUrl ? { uri: coverUrl } : require('../images/Group10019.png')}
              style={{ width: '100%', height: '100%', resizeMode: 'cover' }}
            />
          </TouchableOpacity>
          <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: Black }}>
            {profile.name}
          </Text>
        </View>

        <View style={{ height: screenHeight / 100 }}></View>

        <View style={styles.changeData}>
          {data.map((item, idex) => {
            return (
              <Pressable
                onPress={() => { setDataId(item.id) }}
                style={[item.id == dataId ? styles.active : styles.unActive]}>
                <Text style={[styles.textData, { color: item.id == dataId ? DarkBlue : Black }]}>{item.name}</Text>
              </Pressable>
            )
          })}
        </View>

        {dataId == 0
          ?
          <>

            {/* <Text numberOfLines={1} style={styles.label}> {strings('lang.Beginningoffreetime')}</Text>
            {activeData
              ?
              <TouchableOpacity
                onPress={() => { setDatePickerVisibility(!datePickerVisible) }}
                style={styles.contentContainer}>
                <View style={styles.textTime}>
                  <Text style={{
                    color: Black, textAlignVertical: 'center',
                    fontFamily: appFont,
                    fontSize: screenWidth / 32,
                  }}>
                    {startTime
                      ?
                      startTime
                      :
                      strings('lang.Setthetime')
                    }
                  </Text>
                </View>
              </TouchableOpacity>
              :
              <View style={styles.activeData}>
                <Text numberOfLines={1} style={styles.labelGrey}>
                  {startTime}
                </Text>
              </View>
            }

            <Text numberOfLines={1} style={styles.label}> {strings('lang.Endoffreetime')}</Text>
            {activeData
              ?
              <TouchableOpacity
                onPress={() => { setDatePickerVisibility1(!datePickerVisible1) }}
                style={styles.contentContainer}>
                <View style={styles.textTime}>
                  <Text style={{
                    color: Black, textAlignVertical: 'center',
                    fontFamily: appFont,
                    fontSize: screenWidth / 32,
                  }}>
                    {endTime
                      ?
                      endTime
                      :
                      strings('lang.Setthetime')
                    }
                  </Text>
                </View>
              </TouchableOpacity>
              :
              <View style={styles.activeData}>
                <Text numberOfLines={1} style={styles.labelGrey}>
                  {endTime}
                </Text>
              </View>
            } */}

            {activeData
              ?
              <>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.Username')}</Text>
                <TextInput
                  onChangeText={text => {
                    setName(text);
                  }}
                  value={name}
                  placeholderTextColor={Black}
                  style={styles.textInput}
                />

                <Text numberOfLines={1} style={styles.label}> {strings('lang.Phone')}</Text>
                <TextInput
                  onChangeText={text => {
                    setPhone(text);
                  }}
                  value={phone}
                  keyboardType={'number-pad'}
                  placeholderTextColor={Black}
                  style={styles.textInput}
                />

                <Text numberOfLines={1} style={styles.label}> {strings('lang.Email')}</Text>
                <TextInput
                  onChangeText={text => {
                    setEmail(text);
                  }}
                  value={email}
                  keyboardType={'email-address'}
                  placeholderTextColor={Black}
                  style={styles.textInput}
                />
                {/* 
                <Text numberOfLines={1} style={styles.label}> {strings('lang.Gender')}</Text>
                <View style={styles.contentContainer}>
                  <View style={[styles.textInput1, { backgroundColor: WhiteGery }]}>
                    <Pressable onPress={() => { setGenderId('1'); setGender('male') }} style={styles.genderContainer}>
                      <View style={styles.gendercircleConatiner}>
                        <View style={[styles.gendercircle, { backgroundColor: genderId == '1' ? DarkBlue : White }]}>
                        </View>
                      </View>
                      <Text style={styles.genderText}>{strings('lang.male')}</Text>
                    </Pressable>
                    <Pressable onPress={() => { setGenderId('2'); setGender('female') }} style={styles.genderContainer}>
                      <View style={styles.gendercircleConatiner}>
                        <View style={[styles.gendercircle, { backgroundColor: genderId == '2' ? DarkBlue : White }]}>
                        </View>
                      </View>
                      <Text style={styles.genderText}>{strings('lang.female')}</Text>
                    </Pressable>
                  </View>
                </View> */}

                <Text numberOfLines={1} style={styles.label}> {strings('lang.DateOfBirth')}</Text>
                <View style={{ ...styles.textInput, ...{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' } }}>
                  <Text style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 32, marginEnd: 5 }}>{date ? date.slice(0, 10) : ''}</Text>
                  <DateTimePickerModal
                    isVisible={showDate}
                    mode="date"
                    onConfirm={
                      (selectedDate) => {
                        console.log('selectedDate', selectedDate)
                        const currentDate = selectedDate || date;
                        // setDate(currentDate);
                        console.log('currentDateTime', moment(currentDate).format())
                        setShowDate(false)
                        setDate(moment(currentDate).format());
                      }
                    }
                    onCancel={() => { setShowDate(false) }}
                  />
                  <Pressable onPress={() => { setShowDate(true) }} style={styles.selectContainer}>
                    <Text style={styles.text}>
                      {strings('lang.Locate')}
                    </Text>
                  </Pressable>
                </View>

                <Text numberOfLines={1} style={styles.label}> {strings('lang.IDNumber')}</Text>
                <TextInput
                  onChangeText={text => {
                    setIdentityNumber(text);
                  }}
                  value={identityNumber}
                  placeholderTextColor={Black}
                  style={styles.textInput}
                />

                <Text numberOfLines={1} style={styles.label}> {strings('lang.IBAN')}</Text>
                <TextInput
                  onChangeText={text => {
                    setIbanNumber(text);
                  }}
                  value={ibanNumber}
                  placeholderTextColor={Black}
                  style={styles.textInput}
                />

                <Text numberOfLines={1} style={styles.label}> {strings('lang.STC')}</Text>
                <TextInput
                  onChangeText={text => {
                    setStcNumber(text);
                  }}
                  value={stcNumber}
                  placeholderTextColor={Black}
                  style={styles.textInput}
                />

                <Text numberOfLines={1} style={styles.label}> {strings('lang.City')}</Text>
                <SelectDropdown
                  renderDropdownIcon={() => {
                    return (
                      // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                      <Image
                        source={require('../images/Group9817.png')}
                        style={styles.icon1}
                      />
                    );
                  }}
                  buttonTextAfterSelection={selectedItem => {
                    return selectedItem.name;
                  }}
                  dropdownIconPosition={'left'}
                  dropdownStyle={{ borderRadius: 5 }}
                  defaultButtonText={strings('lang.City')}
                  buttonTextStyle={{
                    color: cityId ? Black : MediumGrey,
                    fontFamily: appFont,
                    fontSize: screenWidth / 28,
                    textAlign: 'left',
                  }}
                  rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                  buttonStyle={{
                    width: screenWidth / 1.1,
                    alignSelf: 'center',
                    textAlign: 'left',
                    fontFamily: appFont,
                    backgroundColor: White,
                    borderRadius: 20,
                    height: screenHeight / 20,
                    marginVertical: '2%',
                    borderWidth: 1,
                    borderColor: MediumGrey,
                    flexDirection: 'row',
                  }}
                  data={cities}
                  onSelect={selectedItem => {
                    setCity(selectedItem)
                    setCityId(selectedItem.id)
                    setAreas([])
                    setAreas(selectedItem.neighborhoods)
                    setArea({})
                    setAreaId(0)
                    // setError_areaId('');
                  }}
                  rowTextForSelection={item => {
                    return item.name;
                  }}
                />

                <Text numberOfLines={1} style={styles.label}> {strings('lang.District')}</Text>
                <SelectDropdown
                  renderDropdownIcon={() => {
                    return (
                      // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                      <Image
                        source={require('../images/Group9817.png')}
                        style={styles.icon1}
                      />
                    );
                  }}
                  buttonTextAfterSelection={selectedItem => {
                    return areaId ? selectedItem.name : '';
                  }}
                  dropdownIconPosition={'left'}
                  dropdownStyle={{ borderRadius: 5 }}
                  defaultButtonText={strings('lang.Area')}
                  buttonTextStyle={{
                    color: areaId ? Black : MediumGrey,
                    fontFamily: appFont,
                    fontSize: screenWidth / 28,
                    textAlign: 'left',
                  }}
                  rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                  buttonStyle={{
                    width: screenWidth / 1.1,
                    alignSelf: 'center',
                    textAlign: 'left',
                    fontFamily: appFont,
                    backgroundColor: White,
                    borderRadius: 20,
                    height: screenHeight / 20,
                    marginVertical: '2%',
                    borderWidth: 1,
                    borderColor: MediumGrey,
                    flexDirection: 'row',
                  }}
                  data={areas}
                  onSelect={selectedItem => {
                    setArea(selectedItem)
                    setAreaId(selectedItem.id)
                    // setError_areaId('');
                  }}
                  rowTextForSelection={item => {
                    return item.name;
                  }}
                />
              </>
              :
              <>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.Username')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {name}
                  </Text>
                </View>

                <Text numberOfLines={1} style={styles.label}> {strings('lang.Phone')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {phone}
                  </Text>
                </View>


                <Text numberOfLines={1} style={styles.label}> {strings('lang.Email')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {email}
                  </Text>
                </View>

                {/* <Text numberOfLines={1} style={styles.label}> {strings('lang.Gender')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {gender ? strings(`lang.${gender}`) : ''}
                  </Text>
                </View> */}

                <Text numberOfLines={1} style={styles.label}> {strings('lang.DateOfBirth')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {date ? date.slice(0, 10) : ''}
                  </Text>
                </View>

                <Text numberOfLines={1} style={styles.label}> {strings('lang.IDNumber')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {identityNumber}
                  </Text>
                </View>

                <Text numberOfLines={1} style={styles.label}> {strings('lang.IBAN')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {ibanNumber}
                  </Text>
                </View>

                <Text numberOfLines={1} style={styles.label}> {strings('lang.STC')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {stcNumber}
                  </Text>
                </View>

                <Text numberOfLines={1} style={styles.label}> {strings('lang.City')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {city.name}
                  </Text>
                </View>

                <Text numberOfLines={1} style={styles.label}> {strings('lang.District')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {area.name}
                  </Text>
                </View>

              </>
            }

            <Text style={styles.label}>{strings('lang.ImageoftheIDorIqamafromthefront')}</Text>
            {imageIdUrl
              ?
              <Pressable
                style={{
                  flexDirection: 'row',
                  width: '95%',
                  alignSelf: 'center',
                  height: screenHeight / 6,
                  marginBottom: 10,
                  borderWidth: 1,
                  borderColor: MediumGrey,
                }}
                onPress={() => { if (activeData) { chooseImages('ID') } }}
              >
                <Image
                  source={{ uri: imageIdUrl }}
                  style={{
                    resizeMode: 'contain',
                    width: '100%',
                    height: '100%'
                  }}
                />
              </Pressable>
              :
              imageId
                ?
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      marginBottom: 10,
                      borderWidth: 1,
                      borderColor: MediumGrey,
                    }}
                    onPress={() => { if (activeData) { chooseImages('ID') } }}
                  >
                    <Image
                      source={{ uri: imageId.uri }}
                      style={{
                        resizeMode: 'contain',
                        width: '100%',
                        height: '100%'
                      }}
                    />
                  </Pressable>
                )
                :
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      borderStyle: 'dashed',
                      borderWidth: 1,
                      borderColor: MediumGrey,
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 10,
                    }}
                    onPress={() => { if (activeData) { chooseImages('ID') } }}
                  >
                    <Image
                      source={require('../images/plus.png')}
                      style={{ width: '50%', height: '50%', resizeMode: 'contain', }}
                    />
                  </Pressable>
                )
            }

            <Text style={styles.label}>{strings('lang.imageofthedriverslicensefromthefront')}</Text>
            {imageLeadershipUrl
              ?
              <Pressable
                style={{
                  flexDirection: 'row',
                  width: '95%',
                  alignSelf: 'center',
                  height: screenHeight / 6,
                  marginBottom: 10,
                  borderWidth: 1,
                  borderColor: MediumGrey,
                }}
                onPress={() => { if (activeData) { chooseImages('License') } }}
              >
                <Image
                  source={{ uri: imageLeadershipUrl }}
                  style={{
                    resizeMode: 'contain',
                    width: '100%',
                    height: '100%'
                  }}
                />
              </Pressable>
              :
              imageLeadership
                ?
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      marginBottom: 10,
                      borderWidth: 1,
                      borderColor: MediumGrey,
                    }}
                    onPress={() => { if (activeData) { chooseImages('License') } }}
                  >
                    <Image
                      source={{ uri: imageLeadership.uri }}
                      style={{
                        resizeMode: 'contain',
                        width: '100%',
                        height: '100%'
                      }}
                    />
                  </Pressable>
                )
                :
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      borderStyle: 'dashed',
                      borderWidth: 1,
                      borderColor: MediumGrey,
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 10,
                    }}
                    onPress={() => { if (activeData) { chooseImages('License') } }}
                  >
                    <Image
                      source={require('../images/plus.png')}
                      style={{ width: '50%', height: '50%', resizeMode: 'contain', }}
                    />
                  </Pressable>
                )
            }

            <Text style={styles.label}>{strings('lang.imageofthevehicleregistrationformfromthefront')}</Text>
            {imageCarUrl
              ?
              <Pressable
                style={{
                  flexDirection: 'row',
                  width: '95%',
                  alignSelf: 'center',
                  height: screenHeight / 6,
                  marginBottom: 10,
                  borderWidth: 1,
                  borderColor: MediumGrey,
                }}
                onPress={() => { if (activeData) { chooseImages('Car') } }}
              >
                <Image
                  source={{ uri: imageCarUrl }}
                  style={{
                    resizeMode: 'contain',
                    width: '100%',
                    height: '100%'
                  }}
                />
              </Pressable>
              :
              imageCar
                ?
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      marginBottom: 10,
                      borderWidth: 1,
                      borderColor: MediumGrey,
                    }}
                    onPress={() => { if (activeData) { chooseImages('Car') } }}
                  >
                    <Image
                      source={{ uri: imageCar.uri }}
                      style={{
                        resizeMode: 'contain',
                        width: '100%',
                        height: '100%'
                      }}
                    />
                  </Pressable>
                )
                :
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      borderStyle: 'dashed',
                      borderWidth: 1,
                      borderColor: MediumGrey,
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 10,
                    }}
                    onPress={() => { if (activeData) { chooseImages('Car') } }}
                  >
                    <Image
                      source={require('../images/plus.png')}
                      style={{ width: '50%', height: '50%', resizeMode: 'contain', }}
                    />
                  </Pressable>
                )
            }




            {/* <Text numberOfLines={1} style={styles.label}> {strings('lang.socialstatus')}</Text>
            {activeData
              ?
              <View style={styles.contentContainer}>
                <View style={[styles.textInput1, { backgroundColor: WhiteGery }]}>
                  <Pressable onPress={() => { setSocialStatusId('1') }} style={styles.genderContainer}>
                    <View style={styles.gendercircleConatiner}>
                      <View style={[styles.gendercircle, { backgroundColor: socialStatusId == '1' ? DarkBlue : White }]}>
                      </View>
                    </View>
                    <Text style={styles.genderText}>{strings('lang.bachelor')}</Text>
                  </Pressable>
                  <Pressable onPress={() => { setSocialStatusId('2') }} style={styles.genderContainer}>
                    <View style={styles.gendercircleConatiner}>
                      <View style={[styles.gendercircle, { backgroundColor: socialStatusId == '2' ? DarkBlue : White }]}>
                      </View>
                    </View>
                    <Text style={styles.genderText}>{strings('lang.married')}</Text>
                  </Pressable>
                </View>
              </View>
              :
              <View style={styles.activeData}>
                <Text numberOfLines={1} style={styles.labelGrey}>
                  {socialState}
                </Text>
              </View>
            } */}

            {/* <Text numberOfLines={1} style={styles.label}> {strings('lang.Doyousmoke')}</Text>
            {activeData
              ?
              <View style={styles.contentContainer}>
                <View style={[styles.textInput1, { backgroundColor: WhiteGery }]}>
                  <Pressable onPress={() => { setsmokeId('1') }} style={styles.genderContainer}>
                    <View style={styles.gendercircleConatiner}>
                      <View style={[styles.gendercircle, { backgroundColor: smokeId == '1' ? DarkBlue : White }]}>
                      </View>
                    </View>
                    <Text style={styles.genderText}>{strings('lang.Yes')}</Text>
                  </Pressable>
                  <Pressable onPress={() => { setsmokeId('2') }} style={styles.genderContainer}>
                    <View style={styles.gendercircleConatiner}>
                      <View style={[styles.gendercircle, { backgroundColor: smokeId == '2' ? DarkBlue : White }]}>
                      </View>
                    </View>
                    <Text style={styles.genderText}>{strings('lang.No')}</Text>
                  </Pressable>
                </View>
              </View>
              :
              <View style={styles.activeData}>
                <Text numberOfLines={1} style={styles.labelGrey}>
                  {smoker}
                </Text>
              </View>
            } */}

            {/* <Text numberOfLines={1} style={styles.label}> {strings('lang.sabbatical')}</Text>
            {activeData
              ?
              <View style={styles.contentContainer}>
                <View style={[styles.textInput1, { backgroundColor: WhiteGery }]}>
                  <Pressable onPress={() => { setBusyId('1') }} style={styles.genderContainer}>
                    <View style={styles.gendercircleConatiner}>
                      <View style={[styles.gendercircle, { backgroundColor: busyId == '1' ? DarkBlue : White }]}>
                      </View>
                    </View>
                    <Text style={styles.genderText}>{strings('lang.free')}</Text>
                  </Pressable>
                  <Pressable onPress={() => { setBusyId('2') }} style={styles.genderContainer}>
                    <View style={styles.gendercircleConatiner}>
                      <View style={[styles.gendercircle, { backgroundColor: busyId == '2' ? DarkBlue : White }]}>
                      </View>
                    </View>
                    <Text style={styles.genderText}>{strings('lang.Unavailable')}</Text>
                  </Pressable>
                </View>
              </View>
              :
              <View style={styles.activeData}>
                <Text numberOfLines={1} style={styles.labelGrey}>
                  {busy}
                </Text>
              </View>
            } */}

            {/* <Text style={styles.label}>{strings('lang.Criminalstatus')}</Text>
            {imageCriminalStatus
              ?
              (
                <View
                  style={{
                    flexDirection: 'row',
                    width: '95%',
                    alignSelf: 'center',
                    height: screenHeight / 6,
                    marginBottom: 10,
                    borderWidth: 1,
                    borderColor: MediumGrey,
                  }}
                >
                  {activeData
                    ?
                    < Button transparent block
                      onPress={() => { chooseImages('Criminal') }}
                      style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: 5, backgroundColor: MediumGrey, position: "absolute", zIndex: 100, alignSelf: "flex-start", justifyContent: "center", end: 0, top: 0 }}>
                      <Image source={require('../images/trash.png')} style={{ width: '130%', height: '130%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />

                    </Button>
                    :
                    <></>
                  }

                  <Image
                    source={{ uri: imageCriminalStatus.uri }}
                    style={{
                      resizeMode: 'contain',
                      width: '100%',
                      height: '100%'
                    }}
                  />
                </View>
              )
              :
              (
                <View
                  style={{
                    flexDirection: 'row',
                    borderStyle: 'dashed',
                    borderWidth: 1,
                    borderColor: MediumGrey,
                    width: '95%',
                    alignSelf: 'center',
                    height: screenHeight / 6,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: 10,
                  }}
                >
                  <Image
                    source={require('../images/carr.png')}
                    style={{ width: '50%', height: '50%', resizeMode: 'contain', }}
                  />
                  {activeData
                    ?
                    <Pressable onPress={() => { chooseImages('Criminal') }}
                      style={{ position: 'absolute', start: '0%', top: '8%', height: '15%', width: '10%' }}>
                      <Image
                        source={require('../images/x.png')}
                        style={{ width: '100%', height: '100%', resizeMode: 'contain', }}
                      />
                    </Pressable>
                    :
                    <></>
                  }
                </View>
              )
            } */}


          </>
          :
          <>

            {activeData1
              ?
              <>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.CarBrand')}</Text>
                <SelectDropdown
                  renderDropdownIcon={() => {
                    return (
                      // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                      <Image
                        source={require('../images/Group9817.png')}
                        style={styles.icon1}
                      />
                    );
                  }}
                  buttonTextAfterSelection={selectedItem => {
                    return selectedItem.name;
                  }}
                  dropdownIconPosition={'left'}
                  dropdownStyle={{ borderRadius: 5 }}
                  defaultButtonText={carBrandId ? carBrand.name : strings('lang.CarBrand')}
                  buttonTextStyle={{
                    color: carBrandId ? Black : MediumGrey,
                    fontFamily: appFont,
                    fontSize: screenWidth / 28,
                    textAlign: 'left',
                  }}
                  rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                  buttonStyle={{
                    width: screenWidth / 1.1,
                    alignSelf: 'center',
                    textAlign: 'left',
                    fontFamily: appFont,
                    backgroundColor: White,
                    borderRadius: 20,
                    height: screenHeight / 20,
                    marginVertical: '2%',
                    borderWidth: 1,
                    borderColor: MediumGrey,
                    flexDirection: 'row',
                  }}
                  data={carBrands}
                  onSelect={selectedItem => {
                    setCarBrand(selectedItem);
                    setCarBrandId(selectedItem.id);
                    setCarModels([])
                    setCarModels(selectedItem.models)
                    setCarModel({})
                    setCarModelId(0)
                  }}
                  rowTextForSelection={item => {
                    return item.name;
                  }}
                />
                <Text numberOfLines={1} style={styles.label}> {strings('lang.CarModel')}</Text>
                <SelectDropdown
                  renderDropdownIcon={() => {
                    return (
                      // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                      <Image
                        source={require('../images/Group9817.png')}
                        style={styles.icon1}
                      />
                    );
                  }}
                  buttonTextAfterSelection={selectedItem => {
                    return selectedItem.name;
                  }}
                  dropdownIconPosition={'left'}
                  dropdownStyle={{ borderRadius: 5 }}
                  defaultButtonText={carModelId ? carModel.name : strings('lang.CarModel')}
                  buttonTextStyle={{
                    color: carModelId ? Black : MediumGrey,
                    fontFamily: appFont,
                    fontSize: screenWidth / 28,
                    textAlign: 'left',
                  }}
                  rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                  buttonStyle={{
                    width: screenWidth / 1.1,
                    alignSelf: 'center',
                    textAlign: 'left',
                    fontFamily: appFont,
                    backgroundColor: White,
                    borderRadius: 20,
                    height: screenHeight / 20,
                    marginVertical: '2%',
                    borderWidth: 1,
                    borderColor: MediumGrey,
                    flexDirection: 'row',
                  }}
                  data={carModels}
                  onSelect={selectedItem => {
                    setCarModel(selectedItem);
                    setCarModelId(selectedItem.id);
                  }}
                  rowTextForSelection={item => {
                    return item.name;
                  }}
                />
                <Text numberOfLines={1} style={styles.label}> {strings('lang.Year')}</Text>
                <SelectDropdown
                  renderDropdownIcon={() => {
                    return (
                      // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                      <Image
                        source={require('../images/Group9817.png')}
                        style={styles.icon1}
                      />
                    );
                  }}
                  buttonTextAfterSelection={selectedItem => {
                    return selectedItem.name;
                  }}
                  dropdownIconPosition={'left'}
                  dropdownStyle={{ borderRadius: 5 }}
                  defaultButtonText={yearId ? year.name : strings('lang.Year')}
                  buttonTextStyle={{
                    color: yearId ? Black : MediumGrey,
                    fontFamily: appFont,
                    fontSize: screenWidth / 28,
                    textAlign: 'left',
                  }}
                  rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                  buttonStyle={{
                    width: screenWidth / 1.1,
                    alignSelf: 'center',
                    textAlign: 'left',
                    fontFamily: appFont,
                    backgroundColor: White,
                    borderRadius: 20,
                    height: screenHeight / 20,
                    marginVertical: '2%',
                    borderWidth: 1,
                    borderColor: MediumGrey,
                    flexDirection: 'row',
                  }}
                  data={years}
                  onSelect={selectedItem => {
                    setYear(selectedItem);
                    setYearId(selectedItem.id);
                  }}
                  rowTextForSelection={item => {
                    return item.name;
                  }}
                />
                <Text numberOfLines={1} style={styles.label}> {strings('lang.CarColor')}</Text>
                <View style={{ width: '95%', alignSelf: 'center', flexDirection: 'row', flexWrap: 'wrap', backgroundColor: WhiteGery, borderRadius: 10, paddingVertical: 10, marginBottom: 10, paddingHorizontal: '5%', }}>
                  {colors.map((color) => {
                    return (
                      <TouchableOpacity onPress={() => { setColorId(color.id) }} style={{ marginHorizontal: '4%', marginVertical: 2, backgroundColor: color.id, width: screenWidth / 12, height: screenWidth / 12, borderRadius: screenWidth / 24, borderWidth: .8, borderColor: MediumGrey, alignItems: 'center', justifyContent: 'center' }}>
                        {color.id == colorId
                          &&
                          <Image source={require('../images/true.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain' }} />
                        }
                      </TouchableOpacity>
                    );
                  })}
                </View>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.carType')}</Text>
                <SelectDropdown
                  renderDropdownIcon={() => {
                    return (
                      // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                      <Image
                        source={require('../images/Group9817.png')}
                        style={styles.icon1}
                      />
                    );
                  }}
                  buttonTextAfterSelection={selectedItem => {
                    return selectedItem.name;
                  }}
                  dropdownIconPosition={'left'}
                  dropdownStyle={{ borderRadius: 5 }}
                  defaultButtonText={driverDaily_trip_typeName ? driverDaily_trip_typeName : strings('lang.carType')}
                  buttonTextStyle={{
                    color: driverDaily_trip_typeId ? Black : MediumGrey,
                    fontFamily: appFont,
                    fontSize: screenWidth / 28,
                    textAlign: 'left',
                  }}
                  rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                  buttonStyle={{
                    width: screenWidth / 1.1,
                    alignSelf: 'center',
                    textAlign: 'left',
                    fontFamily: appFont,
                    backgroundColor: White,
                    borderRadius: 20,
                    height: screenHeight / 20,
                    marginVertical: '2%',
                    borderWidth: 1,
                    borderColor: MediumGrey,
                    flexDirection: 'row',
                  }}
                  data={driverDaily_trip_types}
                  onSelect={selectedItem => {
                    setDriverDaily_trip_typeName(selectedItem.name);
                    setDriverDaily_trip_typeId(selectedItem.id);
                    setErr_DriverDaily_trip_type('');
                  }}
                  rowTextForSelection={item => {
                    return item.name;
                  }}
                />
                <Text numberOfLines={1} style={styles.label}> {strings('lang.Therightletterofthepainting')}</Text>
                <TextInput
                  onChangeText={text => {
                    setPlate_letter_right(text);
                  }}
                  value={plate_letter_right}
                  placeholderTextColor={Black}
                  style={styles.textInput}
                />
                <Text numberOfLines={1} style={styles.label}> {strings('lang.Themiddleletterofthepainting')}</Text>
                <TextInput
                  onChangeText={text => {
                    setPlate_letter_middle(text);
                  }}
                  value={plate_letter_middle}
                  keyboardType={'number-pad'}
                  placeholderTextColor={Black}
                  style={styles.textInput}
                />
                <Text numberOfLines={1} style={styles.label}> {strings('lang.Theleftletterofthepainting')}</Text>
                <TextInput
                  onChangeText={text => {
                    setPlate_letter_left(text);
                  }}
                  value={plate_letter_left}
                  placeholderTextColor={Black}
                  style={styles.textInput}
                />
                <Text numberOfLines={1} style={styles.label}> {strings('lang.SerialNumber')}</Text>
                <TextInput
                  onChangeText={text => {
                    setSequence_number(text);
                  }}
                  value={sequence_number}
                  placeholderTextColor={Black}
                  style={styles.textInput}
                />

                <Text numberOfLines={1} style={styles.label}> {strings('lang.carplatenumber')}</Text>
                <TextInput
                  onChangeText={text => {
                    setNumberCar(text);
                  }}
                  value={numberCar}
                  keyboardType={'number-pad'}
                  placeholderTextColor={Black}
                  style={styles.textInput}
                />
              </>
              :
              <>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.CarBrand')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {carBrand.name}
                  </Text>
                </View>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.CarModel')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {carModel.name}
                  </Text>
                </View>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.Year')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {year.name}
                  </Text>
                </View>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.CarColor')}</Text>
                <View style={{ marginHorizontal: '5%', marginVertical: 2, backgroundColor: color.id, width: screenWidth / 12, height: screenWidth / 12, borderRadius: screenWidth / 24, borderWidth: .8, borderColor: MediumGrey }}></View>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.carType')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {driverDaily_trip_typeName}
                  </Text>
                </View>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.Therightletterofthepainting')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {plate_letter_right}
                  </Text>
                </View>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.Themiddleletterofthepainting')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {plate_letter_middle}
                  </Text>
                </View>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.Theleftletterofthepainting')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {plate_letter_left}
                  </Text>
                </View>

                <Text numberOfLines={1} style={styles.label}> {strings('lang.SerialNumber')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {sequence_number}
                  </Text>
                </View>
                <Text numberOfLines={1} style={styles.label}> {strings('lang.carplatenumber')}</Text>
                <View style={styles.activeData}>
                  <Text numberOfLines={1} style={styles.labelGrey}>
                    {numberCar}
                  </Text>
                </View>
              </>
            }


            <Text style={styles.label}>{strings('lang.message6')}</Text>
            {imageCarFrontUrl
              ?
              <Pressable
                style={{
                  flexDirection: 'row',
                  width: '95%',
                  alignSelf: 'center',
                  height: screenHeight / 6,
                  marginBottom: 10,
                  borderWidth: 1,
                  borderColor: MediumGrey,
                }}
                onPress={() => { if (activeData1) { chooseImagesCar('Front') } }}
              >
                <Image
                  source={{ uri: imageCarFrontUrl }}
                  style={{
                    resizeMode: 'contain',
                    width: '100%',
                    height: '100%'
                  }}
                />
              </Pressable>
              :
              imageCarFront
                ?
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      marginBottom: 10,
                      borderWidth: 1,
                      borderColor: MediumGrey,
                    }}
                    onPress={() => { if (activeData1) { chooseImagesCar('Front') } }}
                  >
                    <Image
                      source={{ uri: imageCarFront.uri }}
                      style={{
                        resizeMode: 'contain',
                        width: '100%',
                        height: '100%'
                      }}
                    />
                  </Pressable>
                )
                :
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      borderStyle: 'dashed',
                      borderWidth: 1,
                      borderColor: MediumGrey,
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 10,
                    }}
                    onPress={() => { if (activeData1) { chooseImagesCar('Front') } }}
                  >
                    <Image
                      source={require('../images/plus.png')}
                      style={{ width: '50%', height: '50%', resizeMode: 'contain', }}
                    />
                  </Pressable>
                )
            }


            <Text style={styles.label}>{strings('lang.message7')}</Text>
            {imageCarBackUrl
              ?
              <Pressable
                style={{
                  flexDirection: 'row',
                  width: '95%',
                  alignSelf: 'center',
                  height: screenHeight / 6,
                  marginBottom: 10,
                  borderWidth: 1,
                  borderColor: MediumGrey,
                }}
                onPress={() => { if (activeData1) { chooseImagesCar('Back') } }}
              >
                <Image
                  source={{ uri: imageCarBackUrl }}
                  style={{
                    resizeMode: 'contain',
                    width: '100%',
                    height: '100%'
                  }}
                />
              </Pressable>
              :
              imageCarBack
                ?
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      marginBottom: 10,
                      borderWidth: 1,
                      borderColor: MediumGrey,
                    }}
                    onPress={() => { if (activeData1) { chooseImagesCar('Back') } }}
                  >
                    <Image
                      source={{ uri: imageCarBack.uri }}
                      style={{
                        resizeMode: 'contain',
                        width: '100%',
                        height: '100%'
                      }}
                    />
                  </Pressable>
                )
                :
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      borderStyle: 'dashed',
                      borderWidth: 1,
                      borderColor: MediumGrey,
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 10,
                    }}
                    onPress={() => { if (activeData1) { chooseImagesCar('Back') } }}
                  >
                    <Image
                      source={require('../images/plus.png')}
                      style={{ width: '50%', height: '50%', resizeMode: 'contain', }}
                    />
                  </Pressable>
                )
            }


            <Text style={styles.label}>{strings('lang.message2')}</Text>
            {imageCarRightUrl
              ?
              <Pressable
                style={{
                  flexDirection: 'row',
                  width: '95%',
                  alignSelf: 'center',
                  height: screenHeight / 6,
                  marginBottom: 10,
                  borderWidth: 1,
                  borderColor: MediumGrey,
                }}
                onPress={() => { if (activeData1) { chooseImagesCar('Right') } }}
              >
                <Image
                  source={{ uri: imageCarRightUrl }}
                  style={{
                    resizeMode: 'contain',
                    width: '100%',
                    height: '100%'
                  }}
                />
              </Pressable>
              :
              imageCarRight
                ?
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      marginBottom: 10,
                      borderWidth: 1,
                      borderColor: MediumGrey,
                    }}
                    onPress={() => { if (activeData1) { chooseImagesCar('Right') } }}
                  >
                    <Image
                      source={{ uri: imageCarRight.uri }}
                      style={{
                        resizeMode: 'contain',
                        width: '100%',
                        height: '100%'
                      }}
                    />
                  </Pressable>
                )
                :
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      borderStyle: 'dashed',
                      borderWidth: 1,
                      borderColor: MediumGrey,
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 10,
                    }}
                    onPress={() => { if (activeData1) { chooseImagesCar('Right') } }}
                  >
                    <Image
                      source={require('../images/plus.png')}
                      style={{ width: '50%', height: '50%', resizeMode: 'contain', }}
                    />
                  </Pressable>
                )
            }


            <Text style={styles.label}>{strings('lang.message3')}</Text>
            {imageCarLeftUrl
              ?
              <Pressable
                style={{
                  flexDirection: 'row',
                  width: '95%',
                  alignSelf: 'center',
                  height: screenHeight / 6,
                  marginBottom: 10,
                  borderWidth: 1,
                  borderColor: MediumGrey,
                }}
                onPress={() => { if (activeData1) { chooseImagesCar('Left') } }}
              >
                <Image
                  source={{ uri: imageCarLeftUrl }}
                  style={{
                    resizeMode: 'contain',
                    width: '100%',
                    height: '100%'
                  }}
                />
              </Pressable>
              :
              imageCarLeft
                ?
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      marginBottom: 10,
                      borderWidth: 1,
                      borderColor: MediumGrey,
                    }}
                    onPress={() => { if (activeData1) { chooseImagesCar('Left') } }}
                  >
                    <Image
                      source={{ uri: imageCarLeft.uri }}
                      style={{
                        resizeMode: 'contain',
                        width: '100%',
                        height: '100%'
                      }}
                    />
                  </Pressable>
                )
                :
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      borderStyle: 'dashed',
                      borderWidth: 1,
                      borderColor: MediumGrey,
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 10,
                    }}
                    onPress={() => { if (activeData1) { chooseImagesCar('Left') } }}
                  >
                    <Image
                      source={require('../images/plus.png')}
                      style={{ width: '50%', height: '50%', resizeMode: 'contain', }}
                    />
                  </Pressable>
                )
            }


            <Text style={styles.label}>{strings('lang.message8')}</Text>
            {imageCarInsideUrl
              ?
              <Pressable
                style={{
                  flexDirection: 'row',
                  width: '95%',
                  alignSelf: 'center',
                  height: screenHeight / 6,
                  marginBottom: 10,
                  borderWidth: 1,
                  borderColor: MediumGrey,
                }}
                onPress={() => { if (activeData1) { chooseImagesCar('Inside') } }}
              >
                <Image
                  source={{ uri: imageCarInsideUrl }}
                  style={{
                    resizeMode: 'contain',
                    width: '100%',
                    height: '100%'
                  }}
                />
              </Pressable>
              :
              imageCarInside
                ?
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      marginBottom: 10,
                      borderWidth: 1,
                      borderColor: MediumGrey,
                    }}
                    onPress={() => { if (activeData1) { chooseImagesCar('Inside') } }}
                  >
                    <Image
                      source={{ uri: imageCarInside.uri }}
                      style={{
                        resizeMode: 'contain',
                        width: '100%',
                        height: '100%'
                      }}
                    />
                  </Pressable>
                )
                :
                (
                  <Pressable
                    style={{
                      flexDirection: 'row',
                      borderStyle: 'dashed',
                      borderWidth: 1,
                      borderColor: MediumGrey,
                      width: '95%',
                      alignSelf: 'center',
                      height: screenHeight / 6,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 10,
                    }}
                    onPress={() => { if (activeData1) { chooseImagesCar('Inside') } }}
                  >
                    <Image
                      source={require('../images/plus.png')}
                      style={{ width: '50%', height: '50%', resizeMode: 'contain', }}
                    />
                  </Pressable>
                )
            }


          </>
        }

        {/* <DateTimePickerModal
          isVisible={datePickerVisible}
          mode="time"
          onConfirm={
            (selectedDate) => {
              console.log('selectedDate', selectedDate)
              const currentDate = selectedDate || date1;
              // setDate1(currentDate);
              console.log('currentDateTime', moment(currentDate).format())
              setDatePickerVisibility(false)
              setStartTime(moment(currentDate).format('LT'));
            }
          }
          onCancel={() => { setDatePickerVisibility(false) }}
        />

        <DateTimePickerModal
          isVisible={datePickerVisible1}
          mode="time"
          onConfirm={
            (selectedDate) => {
              console.log('selectedDate', selectedDate)
              const currentDate = selectedDate || date1;
              // setDate1(currentDate);
              console.log('currentDateTime', moment(currentDate).format())
              setDatePickerVisibility1(false)
              setEndTime(moment(currentDate).format('LT'));
            }
          }
          onCancel={() => { setDatePickerVisibility1(false) }}
        /> */}

      </ScrollView>

      {loading
        ?
        <></>
        :
        activeData ?
          <Pressable
            onPress={() => {
              if (dataId == 0) {
                editProfile()
              }
              else {
                editVehicle()
              }
            }}
            // onPress={sendMassage}
            style={styles.buttonContainer}
          >
            <Text style={styles.buttonText}>{strings('lang.Save')}</Text>
          </Pressable>
          :
          <Pressable
            onPress={() => { setActiveData(true); setActiveData1(true) }}
            // onPress={sendMassage}
            style={styles.buttonContainer}
          >
            <Text style={styles.buttonText}>{strings('lang.Datamodification')}</Text>
          </Pressable>
      }

      {/* <View style={{ height: screenHeight / 10 }}></View> */}
      {/* <Footer current={'list'} navigation={props.navigation} /> */}
    </View>
  );
};

const styles = StyleSheet.create({

  activeData: {
    width: '95%',
    height: screenHeight / 20,
    backgroundColor: WhiteGery,
    alignItems: 'flex-start',
    alignSelf: 'center',
    justifyContent: 'center',
    paddingHorizontal: '2.5%',
    marginBottom: 10,
    borderRadius: 100,
    // marginTop: 5
  },
  textInput: {
    minWidth: '95%',
    maxWidth: '95%',
    height: screenHeight / 20,
    alignItems: 'flex-start',
    alignSelf: 'center',
    justifyContent: 'center',
    paddingHorizontal: '2.5%',
    marginBottom: 10,
    // borderWidth: 1,
    // borderColor: Grey,
    borderRadius: 100,
    fontFamily: appFontBold,
    fontSize: screenWidth / 30,
    color: Black,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
    backgroundColor: WhiteGery

  },
  textImg: {
    fontSize: screenWidth / 30,
    fontFamily: appFont,
    color: Black,
    marginTop: '2%',
    marginStart: '2%'
  },
  labelContainer: {
    width: '90%',
    alignSelf: 'center',
    height: screenHeight / 18,
    justifyContent: 'center',
    paddingHorizontal: 5,
    borderBottomColor: DarkGrey,
    borderBottomWidth: 1,
  },
  itemContainer: {
    width: '90%',
    alignSelf: 'center',
    height: screenHeight / 20,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputsContainer: {
    width: '100%',
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  label: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 28,
    color: Black,
    alignSelf: 'flex-start',
    marginStart: '2.5%'
  },
  labelWhite: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 30,
    color: White,
  },
  labelGrey: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 30,
    color: Black,
    textAlign: I18nManager.isRTL ? 'left' : 'left',
  },
  button: {
    width: '90%',
    height: 40,
    backgroundColor: appColor1,
    alignSelf: 'center',
    marginHorizontal: 5,
    justifyContent: 'center',
    borderRadius: 5,
    alignItems: 'center',
    padding: 5,
    marginBottom: 0,
    zIndex: 3,
  },
  Changebutton: {
    width: '90%',
    height: 40,
    backgroundColor: Red,
    alignSelf: 'center',
    marginHorizontal: 5,
    justifyContent: 'center',
    borderRadius: 5,
    alignItems: 'center',
    padding: 5,
    marginBottom: 0,
    zIndex: 3,
  },
  changeData: {
    width: screenWidth,
    height: screenHeight / 18,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'space-evenly',
    marginBottom: 10,
    backgroundColor: WhiteGery,
    flexDirection: 'row',
    paddingHorizontal: '5%'
  },
  unActive: {
    width: screenWidth / 2.8,
    height: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  active: {
    width: screenWidth / 2.8,
    height: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 3,
    borderBottomLeftRadius: 5,
    borderBottomRightRadius: 5,
    borderBottomColor: DarkBlue
  },
  textData: {
    fontFamily: appFont,
    fontSize: screenWidth / 33
  },
  btnContainer: { width: '20%', height: '100%', justifyContent: 'center' },
  dataContainer: { flex: 1, },
  iconContainer: { borderRadius: 10, width: screenHeight / 18, height: screenHeight / 18, alignItems: 'center', justifyContent: 'center', backgroundColor: WhiteGery },
  icon: {
    width: '60%',
    height: '60%',
    resizeMode: 'contain',
    tintColor: appColor1,
    // backgroundColor:'red'
  },
  input: {
    height: 45,
    borderRadius: 10,
    color: Black,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    width: '90%',
    marginBottom: 10,
    backgroundColor: WhiteGery,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: WhiteGery,
  },

  inputText: {
    color: DarkGrey,
    fontFamily: appFontBold,
    fontSize: screenWidth / 33,
    alignSelf: 'flex-start',
    textAlign: 'left',
    // textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  dropdownInput: {
    height: screenHeight / 18,
    borderRadius: 5,
    flexDirection: 'row',
    marginBottom: 10,
    backgroundColor: WhiteGery,
    width: screenWidth / 1.6,
    paddingHorizontal: 10,
    textAlign: 'right',
  },
  // icon: {
  //   width: '5%',
  //   height: '15%',
  //   tintColor: appColor1,
  //   resizeMode: 'contain',
  //   marginHorizontal: '5%',
  //   // position: 'absolute',
  //   // right: I18nManager.isRTL ? 0 : '100%',
  // },
  buttonContainer: {
    backgroundColor: DarkBlue,
    width: '95%',
    height: screenHeight / 17,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    borderRadius: 100,
    elevation: 10,
    marginTop: 10,
    marginBottom: screenHeight / 50,
  },
  buttonText: {
    color: White,
    fontFamily: appFontBold,
    fontSize: screenWidth / 25,
  },
  buttonContainer1: {
    backgroundColor: WhiteGery,
    width: '95%',
    height: 45,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    borderRadius: 25,
    elevation: 10,
    borderColor: appColor1,
    borderWidth: 0.8,
  },
  buttonText1: {
    color: appColor1,
    fontFamily: appFontBold,
    fontSize: screenWidth / 28,
  },
  textImg: {
    fontSize: screenWidth / 30,
    fontFamily: appFont,
    color: Black,
    marginTop: '2%', alignSelf: 'flex-start'
  },
  imageContainer: {
    flexDirection: 'row',
    width: '100%',
    alignSelf: 'center',
    height: screenHeight / 6,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: MediumGrey,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconConatiner2: { height: '100%', width: '10%', alignItems: 'center', justifyContent: 'center', },
  genderContainer: { width: screenWidth / 2.5, height: screenHeight / 22, alignItems: 'center', justifyContent: 'flex-start', flexDirection: 'row', paddingHorizontal: '5%' },
  gendercircleConatiner: { width: screenHeight / 50, height: screenHeight / 50, borderRadius: screenHeight / 100, backgroundColor: White, borderWidth: .5, borderColor: MediumGrey, alignItems: 'center', justifyContent: 'center' },
  gendercircle: { width: screenHeight / 75, height: screenHeight / 75, borderRadius: screenHeight / 150, backgroundColor: White, },
  genderText: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 33, marginStart: 5 },
  contentContainer: {
    width: '95%',
    alignSelf: 'center',
    justifyContent: 'center',
    marginBottom: 10,
    height: screenHeight / 20,
    borderRadius: 5,
    paddingHorizontal: 0,
    flexDirection: 'row',
  },
  textInput1: {
    width: '100%',
    flexDirection: 'row',
    alignSelf: 'center',
    borderRadius: 100,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'space-between',
    textAlignVertical: 'center',
    fontFamily: appFont,
    fontSize: screenWidth / 32,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
    paddingHorizontal: 10, overflow: 'hidden'
  },
  textTime: {
    width: '100%',
    alignSelf: 'center',
    borderRadius: 20,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: WhiteGery
  },
  selectContainer: {
    // backgroundColor: White,
    width: screenWidth / 8,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    // position: 'absolute',
    // end: 0
  },
  text: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 32, textDecorationLine: 'underline', textAlign: I18nManager.isRTL ? 'right' : 'left', },
  icon1: { width: '2%', height: '80%', resizeMode: 'contain', tintColor: Black, transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] },

});

export default MyProfileDriver;
