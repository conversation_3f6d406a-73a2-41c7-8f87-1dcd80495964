import React, { Component, useEffect, useState } from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  StyleSheet,
  I18nManager,
  ActivityIndicator,
  Platform,
  BackHandler,
  RefreshControl,
  FlatList
} from 'react-native';
import {
  appFont,
  appFontBold,
  Green,
  WhiteGreen,
  screenWidth,
  White,
  DarkGreen,
  Blue,
  MediumGrey,
  DarkGrey,
  WhiteGery,
  Black,
  screenHeight,
  Red,
  appColor2,
  DarkBlue,
} from '../components/Styles';
import { Button } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Loading from '../components/Loading';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import Notificationcontainer from '../components/Notificationcontainer';
import * as notificationsActions from '../../Store/Actions/notifications';
import { useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// import Model from './Model';
// import Loading from '../components/Loading';
const Notifications = props => {

  const dispatch = useDispatch();
  const [termsAndConditionData, setTermsAndConditionData] = useState('');
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [noResault, setNoResault] = useState('');
  const [page, setPage] = useState(0);
  const [lastPage, setLastPage] = useState(0);
  const [loadingMore, setLoadingMore] = useState(false);
  const [loadingNotification, setLoadingNotification] = useState(false);
  const [Reload, setReload] = useState(false);


  useEffect(() => {
    const getNotifications = async () => {
      setLoading(true)
      try {
        let response = await dispatch(notificationsActions.getNotifications(1));
        if (response.success == true) {
          if (response.data.notifications.items.length == 0) {
            setNotifications([]);
            setNoResault(strings('lang.No_Results'));
          }
          else {
            setNotifications(response.data.notifications.items);
            setLastPage(response.data.notifications.last_page);
            setPage(1)
            setNoResault('')
          }
          setLoading(false);
        }
        else {
          if (response.message) {
            // Toaster(
            //   'top',
            //   'danger',
            //   Red,
            //   response.message,
            //   White,
            //   1500,
            //   screenHeight / 15,
            // );
          }
        }
        setLoading(false);
      }
      catch (err) {
        console.log('err', err)
        setLoading(false);
      }
    };

    getNotifications();
  }, [Reload]);

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        props.navigation.goBack()
        return true;
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );

  const onRefresh = async () => {
    setLoading(true)
    try {
      let response = await dispatch(notificationsActions.getNotifications(1));
      if (response.success == true) {
        if (response.data.notifications.items.length == 0) {
          setNotifications([]);
          setNoResault(strings('lang.No_Results'));
        }
        else {
          setNotifications(response.data.notifications.items);
          setLastPage(response.data.notifications.last_page);
          setPage(1)
          setNoResault('')
        }
        setLoading(false);
      }
      else {
        if (response.message) {
          Toaster(
            'top',
            'danger',
            Red,
            response.message,
            White,
            1500,
            screenHeight / 15,
          );
        }
      }
      setLoading(false);
    }
    catch (err) {
      console.log('err', err)
      setLoading(false);
    }
  };

  const LoadMore = async () => {
    console.log('page', page);
    console.log('lastpage', lastPage);
    if (page < lastPage) {
      setLoadingNotification(true)
      try {
        let response = await dispatch(notificationsActions.getNotifications(page + 1));
        if (response.success == true) {
          setNotifications([...notifications, ...response.data.notifications.items])
          setPage(page + 1);
          setNoResault('')
          setLoadingNotification(false);
        }
        else {
          if (response.message) {
            Toaster(
              'top',
              'danger',
              Red,
              response.message,
              White,
              1500,
              screenHeight / 15,
            );
          }
          setLoadingNotification(false);
        }
      } catch (err) {
        setLoadingNotification(false);
      }

    }
    else {
    }
  }

  const deleteNotifications = async (id) => {
    console.log('id', id);
    setLoadingMore(true)
    try {
      let response = await dispatch(notificationsActions.deleteNotifications(id));
      if (response.success == true) {
        getNotifications()
        Toaster(
          'top',
          'success',
          'green',
          strings('lang.deletedSucess'),
          White,
          1500,
          screenHeight / 15,
        );
      }
      else {
        if (response.message) {
          Toaster(
            'top',
            'danger',
            Red,
            response.message,
            White,
            1500,
            screenHeight / 15,
          );
        }
      }
      setLoadingMore(false);
    }
    catch (err) {
      console.log('err', err)
      setLoadingMore(false);
    }
  };

  const getNotifications = async () => {
    // setLoading(true)
    try {
      let response = await dispatch(notificationsActions.getNotifications(1));
      if (response.success == true) {
        if (response.data.notifications.items.length == 0) {
          setNotifications([]);
          setNoResault(strings('lang.No_Results'));
        }
        else {
          setNotifications(response.data.notifications.items);
          setLastPage(response.data.notifications.last_page);
          setPage(1)
          setNoResault('')
        }
        // setLoading(false);
      }
      else {
        if (response.message) {
          Toaster(
            'top',
            'danger',
            Red,
            response.message,
            White,
            1500,
            screenHeight / 15,
          );
        }
      }
      // setLoading(false);
    }
    catch (err) {
      console.log('err', err)
      // setLoading(false);
    }
  };

  if (loading) {

    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.Notifications')}
          drawerPress={() => {
            props.navigation.navigate('More');
          }}
          backPress={() => {
            props.navigation.goBack();
          }}
        />

        <ScrollView
          style={{ width: '100%', height: '100%', marginBottom: '2%' }}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.section}>
            <View style={styles.textContanier}>
              <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: screenHeight / 7, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>

            </View>
          </View>
          <View style={{ height: screenHeight / 8 }}></View>
        </ScrollView>

      </View>
    );

  } else {
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.Notifications')}
          drawerPress={() => {
            props.navigation.navigate('More');
          }}
          backPress={() => {
            props.navigation.goBack();
          }}
        />

        {loadingMore ? <Loading /> : <></>}
        {noResault ? (
          <Text
            style={{
              fontSize: screenWidth / 18,
              fontFamily: appFontBold,
              color: Red,
              marginTop: screenHeight / 2.6,
              alignSelf: 'center',
            }}
          >
            {noResault}
          </Text>
        ) : (
          <View></View>
        )}

        <FlatList
          data={notifications}
          refreshControl={
            <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) =>
            <Notificationcontainer deleteNotiPress={() => { deleteNotifications(item.id) }} item={item} />
          }
          keyExtractor={item => item.id}
          numColumns={1}
          style={{ alignSelf: "center", width: screenWidth / 1.06, }}
          // contentContainerStyle={{ width: screenWidth / 1.1, justifyContent: 'space-between', flexDirection: 'row', flexWrap: 'wrap' }}
          // style={{ alignSelf: "center", width: screenWidth, overflow: "hidden", flexDirection: "column", flexWrap: "wrap", }}
          onEndReachedThreshold={0.1}
          onEndReached={LoadMore}
        />

        {loadingNotification
          ?
          <ActivityIndicator size={Platform.OS == 'ios' ? 80 : 40} color={DarkBlue} style={{ marginTop: 0 }} />
          :
          <></>}


        <View style={{ height: screenHeight / 20 }}></View>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: 'white',
  },
  section: { width: '90%', alignSelf: 'center' },
  labelContanier: {
    width: '100%',
    marginVertical: '3%',
    alignItems: 'flex-start',
  },
  textContanier: {
    width: '100%',
    marginVertical: '2%',
    alignItems: 'flex-start',
  },
  label: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 20, marginTop: screenHeight / 50 },
  body: {
    fontFamily: appFontBold,
    color: DarkGrey,
    fontSize: screenWidth / 30,
    alignSelf: I18nManager.isRTL ? 'flex-start' : 'flex-end',
  },
});

export default Notifications;
