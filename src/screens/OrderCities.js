import React, { Component, useEffect, useRef, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    TextInput,
    BackHandler,
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    appColor1,
    MediumGreen,
    DarkYellow,
    DarkBlue,
    Gold,
    WhiteBlue,
} from '../components/Styles';
import { Button, Input, Textarea } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import SelectDropdown from 'react-native-select-dropdown';
import RNDateTimePicker from '@react-native-community/datetimepicker';
import moment from 'moment';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import Loading from '../components/Loading';
import * as cityTripsActions from '../../Store/Actions/cityTrips';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import AsyncStorage from '@react-native-async-storage/async-storage';


const OrderCities = props => {
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [cities, setCities] = useState([]);
    const [fromCityId, setFromCityId] = useState(0);
    const [toCityId, setToCityId] = useState(0);
    const [date, setDate] = useState('');
    const [showDate, setShowDate] = useState('');
    const [passengers_count, setPassengers_count] = useState([
        {
            id: 0,
            number: 1
        },
        {
            id: 1,
            number: 2
        },
        {
            id: 2,
            number: 3
        },
        {
            id: 3,
            number: 4
        },
        {
            id: 4,
            number: 5
        },
        {
            id: 5,
            number: 6
        },
        {
            id: 6,
            number: 7
        },
    ]);
    const [count, setCount] = useState(0);
    const [countId, setCountId] = useState(null);
    const [source, setSource] = useState('');
    const [sourceLabel, setSourceLabel] = useState('');
    const [fromLat, setFromLat] = useState('');
    const [fromLng, setFromLng] = useState('');
    const [destination, setDestination] = useState('');
    const [destinationLabel, setDestinationLabel] = useState('');
    const [toLat, setToLat] = useState('');
    const [toLng, setTolng] = useState('');
    const [token, setToken] = useState('');

    const IsFocused = useIsFocused();

    const loginfirst = async () => {
        Toaster(
            'top',
            'danger',
            Red,
            strings('lang.mustLogin'),
            White,
            1500,
            screenHeight / 15,
        );
    }

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                props.navigation.goBack()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    useEffect(() => {
        setLoading(true)
        async function GetInitalData() {
            let token = await AsyncStorage.getItem('token')
            setToken(token)
        }
        GetInitalData()

        console.log(props.route.params);

        if (props.route.params.source == true) {
            if (props.route.params.addressDesription) {
                setSource(props.route.params.addressDesription);
                setSourceLabel(props.route.params.addressLabel);
                setFromLat(props.route.params.lat);
                setFromLng(props.route.params.lng);
            }
        }
        if (props.route.params.source == false) {
            if (props.route.params.addressDesription) {
                setDestination(props.route.params.addressDesription);
                setDestinationLabel(props.route.params.addressLabel);
                setToLat(props.route.params.lat);
                setTolng(props.route.params.lng);
            }
        }
        setLoading(false)

    }, [props, IsFocused]);

    useEffect(() => {
        const getCities = async () => {
            setLoading(true)
            try {
                let response = await dispatch(cityTripsActions.getCities());
                if (response.success == true) {
                    setCities(response.data)
                }
                else {
                    if (response.message) {
                        // Toaster(
                        //     'top',
                        //     'danger',
                        //     Red,
                        //     response.message,
                        //     White,
                        //     1500,
                        //     screenHeight / 15,
                        // );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getCities()
    }, []);

    const createNewTripBetweenCities = async () => {
        if (!token) {
            loginfirst()
        }
        else if (fromCityId == 0 || toCityId == 0 || date == '' || count == 0 || fromLat == '' || fromLng == '' || source == '' || toLat == '' || toLng == '' || destination == '') {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.pleaseCompleteData'),
                White,
                1500,
                screenHeight / 50,
            );
        }
        else {
            let expected_distance = 0;
            if (fromLat && fromLng && toLat && toLng) {
                let GOOGLE_DISTANCES_API_KEY = 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'
                let ApiURL = "https://maps.googleapis.com/maps/api/distancematrix/json?";
                let mode = 'driving'
                let params = `origins=${fromLat},${fromLng}&destinations=${toLat},${toLng}&mode=${mode}&key=${GOOGLE_DISTANCES_API_KEY}`;
                let finalApiURL = `${ApiURL}${encodeURI(params)}`;

                console.log("finalApiURL:\n");
                console.log(finalApiURL);

                // get duration/distance from base to each target
                try {
                    let response = await fetch(finalApiURL);
                    let responseJson = await response.json();
                    console.log("responseJson:\n");
                    console.log(responseJson);
                    let distance = (responseJson.rows[0].elements[0].distance.value);
                    let duration = (Math.round(responseJson.rows[0].elements[0].duration.value / 60));
                    expected_distance = distance
                }
                catch (error) {
                    console.error(error);
                }
            }
            console.log('sad', expected_distance);
            try {
                setLoadingMore(true)
                let response = await dispatch(cityTripsActions.createNewTrip(
                    '',
                    date.slice(0, 10),
                    '',
                    count,
                    fromCityId,
                    toCityId,
                    fromLat,
                    fromLng,
                    source,
                    toLat,
                    toLng,
                    destination,
                    expected_distance
                ));

                if (response.success == true) {
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Orderconfirmed'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    props.navigation.push('Home')
                    setLoadingMore(false)
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false)
                }
            } catch (err) {
                console.log(err);
                setLoadingMore(false)
            }
        }
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Intercityrequest')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Intercityrequest')}
                    // drawerPress={() => {
                    //     props.navigation.navigate('More');
                    // }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                {loadingMore
                    ?
                    <Loading />
                    :
                    <></>}

                <ScrollView
                    style={{ width: '95%', height: '100%', alignSelf: 'center', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >

                    <View style={{ flexDirection: 'row', width: '96%', alignSelf: 'center' }}>

                        <View style={{ width: '100%', alignSelf: 'flex-end', height: '100%', }}>
                            <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 7, justifyContent: 'space-between' }}>
                                <View style={[styles.iconConatiner2, { paddingVertical: '5%' }]}>
                                    <Image source={require('../images/bluelocation.png')} style={styles.locationImageSmall} />

                                    <View style={{ height: '60%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, zIndex: 0, }}></View>

                                    <Image source={require('../images/yallowlocation.png')} style={styles.locationImageSmall} />
                                </View>
                                <View style={{ width: '90%' }}>
                                    <SelectDropdown
                                        renderDropdownIcon={() => {
                                            return (
                                                // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                                                <Image
                                                    source={require('../images/Group9817.png')}
                                                    style={styles.icon}
                                                />
                                            );
                                        }}
                                        buttonTextAfterSelection={selectedItem => {
                                            return selectedItem.name;
                                        }}
                                        dropdownIconPosition={'left'}
                                        dropdownStyle={{ borderRadius: 5 }}
                                        defaultButtonText={strings('lang.from')}
                                        buttonTextStyle={{
                                            color: fromCityId ? Black : MediumGrey,
                                            fontFamily: appFont,
                                            fontSize: screenWidth / 28,
                                            textAlign: 'left',
                                        }}
                                        rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                                        buttonStyle={{
                                            width: '100%',
                                            alignSelf: 'center',
                                            textAlign: 'left',
                                            fontFamily: appFont,
                                            backgroundColor: White,
                                            borderRadius: 20,
                                            height: screenHeight / 18,
                                            marginVertical: '2%',
                                            borderWidth: 1,
                                            borderColor: MediumGrey,
                                            flexDirection: 'row',
                                        }}
                                        data={cities}
                                        onSelect={selectedItem => {
                                            setFromCityId(selectedItem.id)
                                            // setError_areaId('');
                                        }}
                                        rowTextForSelection={item => {
                                            return item.name;
                                        }}
                                    />

                                    <SelectDropdown
                                        renderDropdownIcon={() => {
                                            return (
                                                // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                                                <Image
                                                    source={require('../images/Group9817.png')}
                                                    style={styles.icon}
                                                />
                                            );
                                        }}
                                        buttonTextAfterSelection={selectedItem => {
                                            return selectedItem.name;
                                        }}
                                        dropdownIconPosition={'left'}
                                        dropdownStyle={{ borderRadius: 5 }}
                                        defaultButtonText={strings('lang.to')}
                                        buttonTextStyle={{
                                            color: toCityId ? Black : MediumGrey,
                                            fontFamily: appFont,
                                            fontSize: screenWidth / 28,
                                            textAlign: 'left',
                                        }}
                                        rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                                        buttonStyle={{
                                            width: '100%',
                                            alignSelf: 'center',
                                            textAlign: 'left',
                                            fontFamily: appFont,
                                            backgroundColor: White,
                                            borderRadius: 20,
                                            height: screenHeight / 18,
                                            marginVertical: '2%',
                                            borderWidth: 1,
                                            borderColor: MediumGrey,
                                            flexDirection: 'row',
                                        }}
                                        data={cities}
                                        onSelect={selectedItem => {
                                            setToCityId(selectedItem.id)
                                            // setError_areaId('');
                                        }}
                                        rowTextForSelection={item => {
                                            return item.name;
                                        }}
                                    />

                                </View>
                            </View>

                            <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 7, justifyContent: 'space-between' }}>
                                <View style={[styles.iconConatiner2, { paddingVertical: '5%' }]}>
                                    <Image source={require('../images/Group-288.png')} style={styles.locationImageSmall} />

                                    <View style={{ height: '60%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, zIndex: 0, }}></View>

                                    <Image source={require('../images/Group-277.png')} style={styles.locationImageSmall} />
                                </View>
                                <View style={{ width: '90%' }}>
                                    <Pressable
                                        onPress={() => { props.navigation.navigate('Search', { screen: 'OrderCities', source: true }) }}
                                        style={styles.contentContainer2}>

                                        <View style={styles.textInput}>
                                            <Text numberOfLines={1} style={{
                                                color: source ? Black : MediumGrey, textAlignVertical: 'center',
                                                fontFamily: appFont,
                                                fontSize: screenWidth / 32,
                                                textAlign: I18nManager.isRTL ? 'left' : 'left', width: '100%'
                                            }}>
                                                {
                                                    sourceLabel
                                                        ? sourceLabel
                                                        :
                                                        source
                                                            ?
                                                            source
                                                            :
                                                            strings('lang.Yourlocationis')
                                                }
                                            </Text>
                                            {/* <Pressable onPress={() => { props.navigation.navigate('Destenation', { screen: 'OrderCities', source: true }) }}>
                                                <Text style={{
                                                    color: DarkBlue, textAlignVertical: 'center',
                                                    fontFamily: appFont,
                                                    fontSize: screenWidth / 36,
                                                    textAlign: I18nManager.isRTL ? 'right' : 'left',
                                                    marginStart: '2%', textDecorationStyle: 'solid', textDecorationLine: 'underline'
                                                }}>
                                                    {strings('lang.Locate')}
                                                </Text>
                                            </Pressable> */}
                                        </View>
                                    </Pressable>

                                    <Pressable
                                        onPress={() => { props.navigation.navigate('Search', { screen: 'OrderCities', source: false }) }}
                                        style={styles.contentContainer2}>

                                        <View style={styles.textInput}>
                                            <Text numberOfLines={1} style={{
                                                color: destination ? Black : MediumGrey, textAlignVertical: 'center',
                                                fontFamily: appFont,
                                                fontSize: screenWidth / 32,
                                                textAlign: I18nManager.isRTL ? 'left' : 'left',
                                                marginStart: '2%', width: '100%'
                                            }}>
                                                {
                                                    destinationLabel ?
                                                        destinationLabel :
                                                        destination
                                                            ?
                                                            destination
                                                            :
                                                            strings('lang.destination')
                                                }
                                            </Text>
                                            {/* <Pressable onPress={() => { props.navigation.navigate('Destenation', { screen: 'OrderCities', source: false }) }}>
                                                <Text style={{
                                                    color: DarkBlue, textAlignVertical: 'center',
                                                    fontFamily: appFont,
                                                    fontSize: screenWidth / 36,
                                                    textAlign: I18nManager.isRTL ? 'right' : 'left',
                                                    marginStart: '2%', textDecorationStyle: 'solid', textDecorationLine: 'underline'
                                                }}>
                                                    {strings('lang.Locate')}
                                                </Text>
                                            </Pressable> */}
                                        </View>
                                    </Pressable>
                                </View>
                            </View>

                            <View
                                // onPress={() => { setShowDate(true) }}

                                style={styles.contentContainer}>
                                <View style={styles.iconConatiner2}>
                                    <Image source={require('../images/Group10477.png')} style={[styles.locationImage]} />
                                </View>
                                <View
                                    style={styles.contentContainer2}>

                                    <View style={styles.textInput}>
                                        <Text style={{
                                            color: date ? Black : MediumGrey, textAlignVertical: 'center',
                                            fontFamily: appFont,
                                            fontSize: screenWidth / 32,
                                            textAlign: I18nManager.isRTL ? 'right' : 'left',
                                            marginStart: '2%'
                                        }}>{date ? date.slice(0, 10) : strings('lang.Date')}
                                        </Text>
                                        <TouchableOpacity
                                            onPress={() => { setShowDate(true) }}
                                        >
                                            <Text style={{
                                                color: DarkBlue, textAlignVertical: 'center',
                                                fontFamily: appFont,
                                                fontSize: screenWidth / 36,
                                                textAlign: I18nManager.isRTL ? 'right' : 'left',
                                                marginStart: '2%', textDecorationStyle: 'solid', textDecorationLine: 'underline'
                                            }}>
                                                {strings('lang.Locate')}
                                            </Text>
                                        </TouchableOpacity>

                                    </View>
                                </View>
                                <DateTimePickerModal
                                    isVisible={showDate}
                                    mode="date"
                                    onConfirm={
                                        (selectedDate) => {
                                            console.log('selectedDate', selectedDate)
                                            const currentDate = selectedDate || date;
                                            // setDate(currentDate);
                                            console.log('currentDateTime', moment(currentDate).format())
                                            setShowDate(false)
                                            setDate(moment(currentDate).format());
                                        }
                                    }
                                    onCancel={() => { setShowDate(false) }}
                                />
                            </View>

                            <View style={styles.contentContainer}>
                                <View style={styles.iconConatiner2}>
                                    <Image source={require('../images/66.png')} style={[styles.locationImage, {}]} />
                                </View>
                                <SelectDropdown
                                    renderDropdownIcon={() => {
                                        return (
                                            // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                                            <Image
                                                source={require('../images/Group9817.png')}
                                                style={styles.icon}
                                            />
                                        );
                                    }}
                                    buttonTextAfterSelection={selectedItem => {
                                        return selectedItem.number;
                                    }}
                                    dropdownIconPosition={'left'}
                                    dropdownStyle={{ borderRadius: 5 }}
                                    defaultButtonText={strings('lang.Numberofindividuals')}
                                    buttonTextStyle={{
                                        color: countId ? Black : MediumGrey,
                                        fontFamily: appFont,
                                        fontSize: screenWidth / 28,
                                        textAlign: 'left',
                                    }}
                                    rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                                    buttonStyle={{
                                        width: '100%',
                                        alignSelf: 'center',
                                        textAlign: 'left',
                                        fontFamily: appFont,
                                        backgroundColor: White,
                                        borderRadius: 20,
                                        height: screenHeight / 18,
                                        marginVertical: '2%',
                                        borderWidth: 1,
                                        borderColor: MediumGrey,
                                        flexDirection: 'row',
                                    }}
                                    data={passengers_count}
                                    onSelect={selectedItem => {
                                        setCount(selectedItem.number)
                                        setCountId(selectedItem.id)
                                        // setError_areaId('');
                                    }}
                                    rowTextForSelection={item => {
                                        return item.number;
                                    }}
                                />
                            </View>

                        </View>
                    </View>



                </ScrollView>

                <View
                    style={{
                        width: '100%',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignSelf: 'flex-end',
                        marginTop: screenHeight / 80,
                    }}
                >
                    <Button
                        onPress={() => { createNewTripBetweenCities() }}
                        style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            alignSelf: 'center',
                            width: '95%',
                            height: screenHeight / 20,
                            backgroundColor: DarkBlue,
                            borderRadius: 100,

                        }}
                    >
                        <Text
                            style={{
                                fontFamily: appFontBold,
                                fontSize: screenWidth / 28,
                                color: White,
                            }}
                        >
                            {strings('lang.Sendorder')}
                        </Text>
                    </Button>
                </View>


                <View style={{ height: screenHeight / 15 }}></View>

            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        height: screenHeight / 9,
        width: '100%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row',
        marginVertical: '5%',
        // backgroundColor: Red
    },
    imageContainer: {
        height: '80%',
        width: '10%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    image: {
        height: '25%',
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
    },
    locationImage: {
        height: '80%',
        width: '60%',
        resizeMode: 'contain'
    },
    inputContainer: {
        height: '100%',
        width: '90%',
        // alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    input: {
        height: screenHeight / 20,
        borderRadius: 20,
        width: '100%',
        backgroundColor: White,
        borderWidth: .8,
        flexDirection: 'row',
        paddingStart: '2%',
        borderColor: MediumGrey,
    },
    activeInput: {
        borderColor: DarkBlue,
        borderWidth: .8,
        height: screenHeight / 20,
        borderRadius: 20,
        width: '100%',
        flexDirection: 'row',
        paddingStart: '2%',
        backgroundColor: White,
    },
    input1: {
        height: screenHeight / 5,
        borderRadius: 20,
        width: '90%',
        backgroundColor: White,
        borderWidth: .8,
        flexDirection: 'row',
        paddingStart: '2%',
        borderColor: MediumGrey,
        marginVertical: '5%',
        alignSelf: 'flex-end'
    },
    activeInput1: {
        borderColor: DarkBlue,
        borderWidth: .8,
        height: screenHeight / 5,
        borderRadius: 20,
        width: '90%',
        flexDirection: 'row',
        paddingStart: '2%',
        backgroundColor: White,
        marginVertical: '5%',
        alignSelf: 'flex-end'
    },
    inputText: {
        color: Black,
        width: '80%',
        fontFamily: appFontBold,
        fontSize: screenWidth / 33,
        alignSelf: 'center',
        textAlign: I18nManager.isRTL ? 'right' : 'left',
    },
    selectContainer: {
        // backgroundColor: White,
        width: screenWidth / 8,
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        end: 0
    },
    icon: {
        width: '5%',
        height: '40%',
        tintColor: MediumGrey,
        resizeMode: 'contain',
        marginHorizontal: '7%',
    },
    text: { fontFamily: appFontBold, color: DarkBlue, fontSize: screenWidth / 40, textDecorationLine: 'underline' },
    labelBlack: { fontFamily: appFont, color: Black, fontSize: screenWidth / 33, },
    contentContainer: {
        width: '90%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2%',
        height: screenHeight / 18,
        borderRadius: 5,
        paddingHorizontal: 0,
        flexDirection: 'row',
    },
    iconConatiner2: { height: '100%', width: '10%', alignItems: 'center', justifyContent: 'center', },
    contentContainer2: {
        width: '100%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2%',
        height: screenHeight / 18,
        borderRadius: 5,
        paddingHorizontal: 0,
        flexDirection: 'row',
    },
    locationImageSmall: {
        resizeMode: 'contain',
        width: '100%',
        height: '20%'
    },
    textInput: {
        width: '100%',
        flexDirection: 'row',
        alignSelf: 'center',
        borderRadius: 100,
        borderColor: MediumGrey,
        borderWidth: 1,
        height: '100%',
        alignItems: 'center',
        justifyContent: 'space-between',
        textAlignVertical: 'center',
        fontFamily: appFont,
        fontSize: screenWidth / 32,
        textAlign: I18nManager.isRTL ? 'right' : 'left',
        paddingHorizontal: 10, overflow: 'hidden'
    },
});

export default OrderCities;
