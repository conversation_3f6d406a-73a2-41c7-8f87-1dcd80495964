import React, { Component, useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  StyleSheet,
  I18nManager,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import {
  appFont,
  appFontBold,
  Green,
  WhiteGreen,
  screenWidth,
  White,
  DarkGreen,
  Blue,
  MediumGrey,
  DarkGrey,
  WhiteGery,
  Black,
  screenHeight,
  Red,
  appColor1,
  MediumGreen,
  DarkYellow,
  DarkBlue,
  Gold,
} from '../components/Styles';
import { Button } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import RBSheet from 'react-native-raw-bottom-sheet';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import * as paymentActions from '../../Store/Actions/payment';
import * as dailyTripsActions from '../../Store/Actions/dailyTrips';
import * as packagesActions from '../../Store/Actions/packages';
import * as vipTripsActions from '../../Store/Actions/vipTrips';
import * as cityTripsActions from '../../Store/Actions/cityTrips';
import Loading from '../components/Loading';
import RNRestart from 'react-native-restart';
import * as prizesAction from '../../Store/Actions/prizes';


const PaymentMethods = props => {
  const dispatch = useDispatch();
  const refRbSheet = useRef();
  const [termsAndConditionData, setTermsAndConditionData] = useState('');
  const [couponActive, setCouponActive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [paymentMethodId, setPaymentMethodId] = useState(0);
  const [activeText, setActiveText] = useState(0);
  const [code, setCode] = useState(0);
  const [isSubscription, setIsSubscription] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState([
  ]);

  const [userPackage, setUserPackage] = useState({});
  const [item, setItem] = useState({});
  const [trip, setTrip] = useState({});
  const [tripType, setTripType] = useState('');

  useEffect(() => {
    setIsSubscription(props.route.params.subscription)
    if (props.route.params.subscription) {
      setUserPackage(props.route.params.package)
    }
    else {
      setTrip(props.route.params.item)
      setTripType(props.route.params.type)
    }
    console.log(props.route.params);

    const getPaymentMethods = async () => {
      setLoading(true)
      try {
        let response = await dispatch(paymentActions.getPaymentMethods());
        if (response.success == true) {
          setPaymentMethods(response.data)
        }
        else {
          if (response.message) {
            Toaster(
              'top',
              'danger',
              Red,
              response.message,
              White,
              1500,
              screenHeight / 15,
            );
          }
        }
        setLoading(false);
      }
      catch (err) {
        console.log('err', err)
        setLoading(false);
      }
    };

    const applyUserPrize = async () => {
      setLoading(true)
      try {
        let response = await dispatch(prizesAction.applyUserPrize(props.route.params.item.id));
        if (response.success == true) {

        }
        else {
          if (response.message) {
            Toaster(
              'top',
              'danger',
              Red,
              response.message,
              White,
              1500,
              screenHeight / 15,
            );
          }
        }
        setLoading(false);
      }
      catch (err) {
        console.log('err', err)
        setLoading(false);
      }
    };

    getPaymentMethods();
    // applyUserPrize();

  }, []);



  const acceptOffer = async () => {
    if (paymentMethodId == 0) {
      Toaster(
        'top',
        'danger',
        Red,
        strings('lang.ChoosePaymentMethod'),
        White,
        1500,
        screenHeight / 50,
      );
      setLoadingMore(false)
    }
    else {
      props.navigation.navigate('PaymentWebView', { id: props.route.params.item.id, type: 'UserPackage', paymentMethodId: paymentMethodId })
    }
  }

  const createPackages = async () => {
    // setLoadingMore(true)
    if (paymentMethodId == 0) {
      Toaster(
        'top',
        'danger',
        Red,
        strings('lang.ChoosePaymentMethod'),
        White,
        1500,
        screenHeight / 50,
      );
      setLoadingMore(false)
    }
    else {
      props.navigation.navigate('PaymentWebView', { id: props.route.params.item.id, type: 'UserPackage', paymentMethodId: paymentMethodId })
    }
  }

  const approveTrip = async () => {
    // setLoadingMore(true)
    if (paymentMethodId == 0) {
      Toaster(
        'top',
        'danger',
        Red,
        strings('lang.ChoosePaymentMethod'),
        White,
        1500,
        screenHeight / 50,
      );
      // setLoadingMore(false)
    }
    else {
      // try {
      // setLoadingMore(true)
      // let response = null;

      if (tripType == 'dailytTrip') {
        props.navigation.navigate('PaymentWebView', { id: trip.id, type: 'DailyTrip', paymentMethodId: paymentMethodId, trip: trip })
        // response = await dispatch(dailyTripsActions.confirmPayment(trip.id, paymentMethodId));
        // Toaster(
        //   'top',
        //   'success',
        //   DarkGreen,
        //   strings('lang.Paid'),
        //   White,
        //   1500,
        //   screenHeight / 50,
        // );
      }
      else if (tripType == 'orderVip') {
        props.navigation.navigate('PaymentWebView', { id: trip.id, type: 'VipTrip', paymentMethodId: paymentMethodId, trip: trip })
        // response = await dispatch(vipTripsActions.confirmPayment(trip.id, paymentMethodId));
        // Toaster(
        //   'top',
        //   'success',
        //   DarkGreen,
        //   strings('lang.Paid'),
        //   White,
        //   1500,
        //   screenHeight / 50,
        // );
      }
      else {
        props.navigation.navigate('PaymentWebView', { id: trip.id, type: 'CityTrip', paymentMethodId: paymentMethodId, trip: trip })
        // response = await dispatch(cityTripsActions.confirmPayment(trip.id, paymentMethodId));
        // Toaster(
        //   'top',
        //   'success',
        //   DarkGreen,
        //   strings('lang.Paid'),
        //   White,
        //   1500,
        //   screenHeight / 50,
        // );
      }
      // if (response.success == true) {
      //   if (tripType == 'dailytTrip') {
      //     props.navigation.navigate('MapAddress', { screen: 'Home', cityTrip: false, pending: true, pendingTrip: response.data })
      //   }
      //   else {
      //     props.navigation.navigate('DetailsTrip', { item: trip, type: tripType })
      //   }
      // }
      // else {
      //   if (response.message) {
      //     Toaster(
      //       'top',
      //       'danger',
      //       Red,
      //       response.message,
      //       White,
      //       1500,
      //       screenHeight / 50,
      //     );
      //   }
      //   setLoadingMore(false)
      // }
      // } catch (err) {
      //   console.log(err);
      //   setLoadingMore(false)
      // }
    }
  }

  const applyCoupon = async () => {
    setLoadingMore(true)

    let response = await dispatch(dailyTripsActions.applyCoupon(code, props.route.params.item.id))
    if (response.success == true) {
      setCouponActive(true)
      setTrip(response.data)
      setLoadingMore(false)
      Toaster(
        'top',
        'success',
        DarkGreen,
        strings('lang.Couponenteredsuccessfully'),
        White,
        1500,
        screenHeight / 15,
      );
    }
    else {
      setLoadingMore(false)
      Toaster(
        'top',
        'danger',
        Red,
        response.message,
        White,
        1500,
        screenHeight / 15,
      );
    }
    setLoadingMore(false)
  }


  if (loading) {
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.Paymentmethod')}
          drawerPress={() => {
            props.navigation.navigate('More');
          }}
          backPress={() => {
            RNRestart.Restart();
          }}
        />

        <ScrollView
          style={{ width: '100%', height: '100%', marginBottom: '2%' }}
          showsVerticalScrollIndicator={false}
        >
          {[{ height: 6 }, { height: 6.5 }].map((item) => {
            return <View style={{ width: '90%', height: screenHeight / item.height, backgroundColor: WhiteGery, alignSelf: "center", marginTop: "5%", marginBottom: "2%" }} >
              <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                <View style={{ width: '100%', height: '100%', backgroundColor: WhiteGery, }} />
              </SkeletonPlaceholder>
            </View>
          })}
          <View style={{ height: screenHeight / 8 }}></View>
        </ScrollView>

      </View>
    );

  }
  else {
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.Paymentmethod')}
          // drawerPress={() => {
          //   props.navigation.navigate('More');
          // }}
          backPress={() => {
            RNRestart.Restart();
          }}
        />
        {loadingMore
          ?
          <Loading />
          :
          <></>
        }

        <ScrollView
          style={{ width: '90%', height: '100%', alignSelf: 'center', marginBottom: '2%' }}
          showsVerticalScrollIndicator={false}
        >
          <View style={{ height: screenHeight / 40 }}></View>

          <Text style={styles.label}>{strings('lang.PromoCode')}</Text>
          {couponActive
            ?
            <View style={styles.promoCodeContainer}>
              <View style={activeText == 1 ? styles.activeInput : styles.input}>
                <View style={styles.inputText}>
                  <Text style={[styles.text, { color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{code}</Text>
                </View>
              </View>
              <View
                style={styles.confirmContainer}>
                <Text style={styles.text}>{strings('lang.Done')}</Text>
              </View>
            </View>
            :
            <View style={styles.promoCodeContainer}>
              <View style={activeText == 1 ? styles.activeInput : styles.input}>
                <TextInput
                  style={styles.inputText}
                  value={code}
                  onChangeText={text => setCode(text)}
                  onFocus={() => setActiveText(1)}
                  onBlur={() => {
                    setActiveText(0);
                  }}
                />
              </View>
              <Pressable
                disabled={couponActive ? true : false}
                onPress={() => { applyCoupon() }}
                style={styles.confirmContainer}>
                <Text style={styles.text}>{strings('lang.Confirm')}</Text>
              </Pressable>
            </View>

          }

          {isSubscription
            ?
            <>
              <Text style={styles.label}>{strings('lang.totalprice')}</Text>
              <View style={styles.priceContainer}>
                <View style={styles.labelContainer}>
                  <Text style={styles.labelBlack}>{strings('lang.Deliveryprice')}</Text>
                  <View style={{ height: 1, width: '60%', borderStyle: 'dashed', borderColor: Gold, borderWidth: 1 }}></View>
                  <Text style={styles.labelBlack}>{props.route.params.item.price}{' '}{strings('lang.SR')}</Text>
                </View>

                {/* <View style={styles.labelContainer}>
                  <Text style={styles.labelBlack}>{strings('lang.tax')}</Text>
                  <View style={{ height: 1, width: '60%', borderStyle: 'dashed', borderColor: Gold, borderWidth: 1 }}></View>
                  <Text style={styles.labelBlack}>{userPackage.discount}{' '}{strings('lang.SR')}</Text>
                </View> */}

                {props.route.params.item.discount_amount &&
                  <View style={styles.labelContainer}>
                    <Text style={styles.labelBlack}>{strings('lang.Discountvalue')}</Text>
                    <View style={{ height: 1, width: '60%', borderStyle: 'dashed', borderColor: Gold, borderWidth: 1 }}></View>
                    <Text style={styles.labelBlack}>{props.route.params.item.discount_amount}{' '}{strings('lang.SR')}</Text>
                  </View>
                }


                <View style={{ height: 3, width: '100%', backgroundColor: Gold, marginVertical: '2%' }}></View>

                <View style={styles.labelContainer}>
                  <Text style={styles.labelBlack}>{strings('lang.Priceafterdiscount')}</Text>
                  <Text style={styles.labelBlack}>{props.route.params.item.final_price}{' '}{strings('lang.SR')}</Text>
                </View>
              </View>
            </>
            :
            <>
              <Text style={styles.label}>{strings('lang.totalprice')}</Text>
              <View style={styles.priceContainer}>
                <View style={styles.labelContainer}>
                  <Text style={styles.labelBlack}>{strings('lang.Deliveryprice')}</Text>
                  <View style={{ height: 1, width: '60%', borderStyle: 'dashed', borderColor: Gold, borderWidth: 1 }}></View>
                  <Text style={styles.labelBlack}>{trip.final_price}{' '}{strings('lang.SR')}</Text>
                </View>


                {couponActive &&
                  <>
                    <View style={styles.labelContainer}>
                      <Text style={styles.labelBlack}>{strings('lang.Discountvalue')}</Text>
                      <View style={{ height: 1, width: '60%', borderStyle: 'dashed', borderColor: Gold, borderWidth: 1 }}></View>
                      <Text style={styles.labelBlack}>{trip.discount}{' '}{strings('lang.SR')}</Text>
                    </View>

                    <View style={{ height: 3, width: '100%', backgroundColor: Gold, marginVertical: '2%' }}></View>

                    <View style={styles.labelContainer}>
                      <Text style={styles.labelBlack}>{strings('lang.Priceafterdiscount')}</Text>
                      <Text style={styles.labelBlack}>{trip.final_price}{' '}{strings('lang.SR')}</Text>
                    </View>
                  </>

                }
                {/* <View style={styles.labelContainer}>
                  <Text style={styles.labelBlack}>{strings('lang.tax')}</Text>
                  <View style={{ height: 1, width: '60%', borderStyle: 'dashed', borderColor: Gold, borderWidth: 1 }}></View>
                  <Text style={styles.labelBlack}>{'5'}{' '}{strings('lang.SR')}</Text>
                </View> */}

                {/* <View style={styles.labelContainer}>
                  <Text style={styles.labelBlack}>{strings('lang.PromoCode')}</Text>
                  <View style={{ height: 1, width: '60%', borderStyle: 'dashed', borderColor: Gold, borderWidth: 1 }}></View>
                  <Text style={styles.labelBlack}>{'0'}{' '}{strings('lang.SR')}</Text>
                </View> */}

                {/* <View style={{ height: 3, width: '100%', backgroundColor: Gold, marginVertical: '2%' }}></View>

                <View style={styles.labelContainer}>
                  <Text style={styles.labelBlack}>{strings('lang.Total')}</Text>
                  <Text style={styles.labelBlack}>{props.route.params.item.price + 5}{' '}{strings('lang.SR')}</Text>
                </View> */}
              </View>
            </>
          }

          {/* <>
              <Text style={styles.label}>{strings('lang.EnterforthePromocode')}</Text>
              <View
                style={{
                  width: '100%',
                  height: screenHeight / 20,
                  justifyContent: 'center',
                  flexDirection: 'row',
                }}
              >
                <View style={styles.input}>
                  <TextInput
                    placeholderTextColor={'#707070'}
                    placeholder={strings('lang.PromoCode')}
                    style={styles.inputText}
                    value={code}
                    onChangeText={text => {
                      setCode(text);
                    }}
                    editable={disableState}
                  />
                </View>
                {!disableState
                  ?
                  <Button
                    style={{
                      width: '25%',
                      height: '100%',
                      backgroundColor: Red,
                      alignItems: 'center',
                      justifyContent: 'center',
                      alignSelf: 'center',
                      borderRadius: 15,
                      borderTopStartRadius: 0,
                      borderBottomStartRadius: 0,
                    }}
                    onPress={() => { removeCupontoCart() }}
                  >
                    <Text
                      style={{
                        fontFamily: appFontBold,
                        color: White,
                        fontSize: screenWidth / 25,
                        alignSelf: 'center',
                      }}
                    >
                      {strings('lang.Delete')}
                    </Text>
                  </Button>
                  :
                  <Button
                    style={{
                      width: '25%',
                      height: '100%',
                      backgroundColor: appColor1,
                      alignItems: 'center',
                      justifyContent: 'center',
                      alignSelf: 'center',
                      borderRadius: 15,
                      borderTopStartRadius: 0,
                      borderBottomStartRadius: 0,
                    }}
                    onPress={() => { if (code.length != 0) { addCupontoCart() } }}
                  >
                    <Text
                      style={{
                        fontFamily: appFontBold,
                        color: White,
                        fontSize: screenWidth / 25,
                        alignSelf: 'center',
                      }}
                    >
                      {strings('lang.Enter')}
                    </Text>
                  </Button>
                }
              </View>
            </> */}


          <Text style={styles.label}>{strings('lang.Paymentmethod')}</Text>
          <View style={styles.paymentContainer}>
            {paymentMethods.map((item) => {
              return <Pressable
                onPress={() => { setPaymentMethodId(item.id) }}
                style={styles.Payment}>
                <View style={item.id == paymentMethodId ? styles.activeIconContainer : styles.iconContainer}>
                  <Image
                    source={{ uri: item.image }}
                    // source={require('../images/Group191.png')}
                    style={{ width: '70%', height: '70%', resizeMode: 'contain', }} />
                </View>
                <Text style={styles.paymentText}>{item.name}</Text>
              </Pressable>
            })}

          </View>

        </ScrollView>

        <Button
          onPress={() => {
            if (props.route.params.subscription == true) {
              // createPackages();
              acceptOffer();
            }
            else {
              approveTrip();
            }
          }}
          transparent
          style={{
            width: '90%', height: screenHeight / 20,
            alignSelf: 'center', justifyContent: 'center', alignItems: 'center',
            backgroundColor: DarkBlue, borderRadius: 25,
          }}>
          <Text style={{ fontFamily: appFontBold, color: White, fontSize: screenWidth / 25, }}>{strings('lang.Confirm').toUpperCase()}</Text>
        </Button>


        <RBSheet
          ref={refRbSheet}
          height={screenHeight / 2}
          openDuration={280}
          customStyles={{
            container: {
              // justifyContent: "center",
              alignItems: "center",
              width: '90%',
              marginBottom: screenHeight / 4,
              alignSelf: 'center',
              borderRadius: 25

            },
            wrapper: {

            },
            draggableIcon: {
              // width: '25%',
              // backgroundColor: DarkGrey
            }
          }}
          onClose={() => { props.navigation.navigate('Home') }}
          closeOnDragDown={false}
          closeOnPressBack={false}
          closeOnPressMask={false}
        >
          <View style={{ alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between', width: '100%', paddingTop: '10%', }}>

            <View style={{ width: '80%', marginStart: 15, height: '30%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', }}>
              <Image source={require('../images/Group10546.png')} style={{ resizeMode: 'contain', width: '50%', height: '90%', }} />
            </View>

            <View style={{ height: '30%', width: '90%', alignItems: 'center', alignSelf: 'center', justifyContent: 'center', }}>
              <Text style={{ fontSize: screenWidth / 20, fontFamily: appFontBold, color: Black, textAlign: 'center' }}>{strings('lang.message5')}</Text>
            </View>

            <View style={{ alignItems: "center", justifyContent: 'center', width: '90%', alignSelf: "center", height: '30%' }}>
              {isSubscription == true
                ?
                <Button onPress={() => { refRbSheet.current.close(); props.navigation.navigate("Packages") }} style={{ width: '90%', alignSelf: "center", height: 45, backgroundColor: DarkBlue, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, marginVertical: 5 }}>
                  <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.socialist1')}</Text>
                </Button>
                :
                <Button onPress={() => { refRbSheet.current.close(); props.navigation.navigate("Orders") }} style={{ width: '90%', alignSelf: "center", height: 45, backgroundColor: DarkBlue, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, marginVertical: 5 }}>
                  <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.Orders')}</Text>
                </Button>
              }

              <Button onPress={() => { refRbSheet.current.close(); props.navigation.navigate("Home") }} style={{ width: '90%', alignSelf: "center", height: 45, backgroundColor: WhiteGery, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, marginVertical: 5 }}>
                <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Black, }}>{strings('lang.Home')}</Text>
              </Button>
            </View>
          </View>
        </RBSheet>

        <View style={{ height: screenHeight / 15 }}></View>

      </View>
    );
  }
};

const styles = StyleSheet.create({
  promoCodeContainer: {
    height: screenHeight / 20,
    width: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    marginVertical: '2%',
    marginBottom: '5%',
  },
  input: {
    height: screenHeight / 20,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    width: '70%',
    backgroundColor: White,
    borderWidth: 1,
    flexDirection: 'row',
    paddingStart: '2%',
    borderColor: MediumGrey,
  },
  // input: {
  //   height: '100%',
  //   borderTopStartRadius: 15,
  //   borderBottomStartRadius: 15,
  //   flexDirection: 'row',
  //   width: '75%',
  //   marginBottom: 10,
  //   color: Black,
  //   borderColor: MediumGrey,
  //   borderWidth: 1,
  //   paddingHorizontal: 10,
  // },
  // inputText: {
  //   color: DarkGrey,
  //   width: '100%',
  //   fontFamily: appFontBold,
  //   fontSize: screenWidth / 30,
  //   alignSelf: 'center',
  //   textAlign: Platform.OS == 'ios' ? I18nManager.isRTL ? 'right' : 'left' : null,
  //   height: '100%',
  //   paddingHorizontal: 5
  // },
  activeInput: {
    borderColor: DarkBlue,
    borderWidth: 1,
    height: screenHeight / 20,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    width: '70%',
    flexDirection: 'row',
    paddingStart: '2%',
    backgroundColor: White,
  },
  inputText: {
    color: Black,
    width: '100%',
    fontFamily: appFontBold,
    fontSize: screenWidth / 33,
    alignSelf: 'center',
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  confirmContainer: {
    height: screenHeight / 20,
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
    width: '30%',
    backgroundColor: DarkBlue,
    alignItems: 'center',
    justifyContent: 'center'
  },
  priceContainer: {
    height: screenHeight / 7,
    width: '100%',
    backgroundColor: White,
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginBottom: '5%'
  },
  paymentContainer: {
    width: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
    overflow: 'hidden'
  },
  Payment: {
    alignItems: 'center',
    justifyContent: 'center',
    height: screenHeight / 7,
    width: '33%',
    marginBottom: '2%'
  },
  activeIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: screenWidth / 4,
    width: screenWidth / 4,
    borderRadius: 20,
    backgroundColor: White,
    borderColor: Gold,
    borderWidth: 1
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: screenWidth / 4,
    width: screenWidth / 4,
    borderRadius: 20,
    backgroundColor: WhiteGery,
  },
  paymentText: { color: Black, fontSize: screenWidth / 40, fontFamily: appFont },

  labelContainer: {
    flexDirection: 'row', width: '100%', alignItems: 'center', justifyContent: 'space-between'
  },
  label: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 26, alignSelf: 'flex-start', marginBottom: screenHeight / 100 },
  body: {
    fontFamily: appFontBold,
    color: DarkGrey,
    fontSize: screenWidth / 30,
    alignSelf: I18nManager.isRTL ? 'flex-start' : 'flex-end',
  },
  paymentActive: {
    width: '98%',
    height: 70,
    alignSelf: 'center',
    borderColor: DarkYellow,
    borderWidth: 1,
    borderRadius: 5,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    marginVertical: 8,
    marginBottom: 5
  },
  paymentUnactive: {
    width: '98%',
    height: 70,
    alignSelf: 'center',
    borderColor: MediumGrey,
    borderWidth: 1,
    borderRadius: 5,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    marginVertical: 8,
    marginBottom: 5
  },
  text: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 28, opacity: .8 },
  labelBlack: { fontFamily: appFont, color: Black, fontSize: screenWidth / 33, },
});

export default PaymentMethods;
