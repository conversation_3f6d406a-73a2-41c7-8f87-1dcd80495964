import React, { Component, useCallback, useEffect, useRef, useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, PermissionsAndroid, BackHandler, I18nManager, Keyboard, Modal, InteractionManager, Linking, Animated, ActivityIndicator, Alert, Platform } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor2, appColor1, Black, Red, MediumGreen, Grey, DarkBlue, DarkYellow, Gold, LightGreen, Green2 } from '../components/Styles';
import { Button, Container, Textarea } from 'native-base';
import Header from '../components/Header';
import Geolocation from 'react-native-geolocation-service';
import MapView, { Callout, Marker, Polygon } from 'react-native-maps';

import { strings } from './i18n';
import { FlatList, ScrollView, TextInput, TouchableOpacity } from 'react-native-gesture-handler';
import Loading from '../components/Loading';
import SelectDropdown from 'react-native-select-dropdown';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { Modalize } from 'react-native-modalize';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import RBSheet from 'react-native-raw-bottom-sheet';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import * as dailyTripsActions from '../../Store/Actions/dailyTrips';
import * as cityTripsActions from '../../Store/Actions/cityTrips';
import * as generalActions from '../../Store/Actions/general';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch, useSelector } from 'react-redux';
import Toaster from '../components/Toaster';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import moment from 'moment';
import StarRating from 'react-native-star-rating';
import Geocoder from 'react-native-geocoding';
import MapViewDirections from 'react-native-maps-directions';
import { updateLocation } from '../../Store/Actions/socket';
import UserOffer from '../components/UserOffer';
import * as geolib from 'geolib';
// import Animated, { Easing } from 'react-native-reanimated';


export const CLEAROFFERS = 'CLEAROFFERS';


const ASPECT_RATIO = screenWidth / screenHeight

const DISABLED_REGION = [
    { latitude: 31.255331800000008, longitude: 29.9850249, latitudeDelta: 0.01, longitudeDelta: 0.01 },
    // Add more regions as needed
];



const MapAddress = props => {

    const polygonCoordinates = [
        {
            latitude: 31.257686,
            longitude: 29.981498,
        },
        {
            latitude:
                31.256302,
            longitude: 29.983452,
        },
        {
            latitude:
                31.254766275260835,
            longitude: 29.984158168209575
            ,
        },
        {
            latitude: 31.255098,
            longitude: 29.982007,
        },
        {
            latitude:
                31.256505,
            longitude: 29.980285
            ,
        },
        {
            latitude:
                31.257837,
            longitude: 29.979531
            ,
        },
    ];

    //   const isUserInsidePolygon = () => {
    //     if (userLocation) {
    //       return isPointInPolygon(userLocation, polygonCoordinates);
    //     }
    //     return false;
    //   };

    const [region, SetRegion] = useState({
        latitude: 0,
        longitude: 0,
        longitudeDelta: 0.01 * ASPECT_RATIO,
        latitudeDelta: 0.01
    })
    const [lat, SetLat] = useState(0)
    const [lng, SetLng] = useState(0)

    const [cars, setCars] = useState([]);
    const [carID, setCarID] = useState(null);

    const [passengers_count, setPassengers_count] = useState([
        {
            id: 0,
            number: 1
        },
        {
            id: 1,
            number: 2
        },
        {
            id: 2,
            number: 3
        },
        {
            id: 3,
            number: 4
        },
        {
            id: 4,
            number: 5
        },
        {
            id: 5,
            number: 6
        },
    ]);
    const [count, setCount] = useState(0);
    const [countId, setCountId] = useState(null);

    const [cities, setCities] = useState([]);
    const [DriverOffers, setDriverOffers] = useState([]);
    const [DriverOffersId, setDriverOffersId] = useState(0);
    const [fromCityId, setFromCityId] = useState(0);
    const [toCityId, setToCityId] = useState(0);


    const [source, setSource] = useState('');
    const [sourceId, setSourceId] = useState(0);
    const [sourceLabel, setSourceLabel] = useState('');
    const [fromLat, setFromLat] = useState('');
    const [fromLng, setFromLng] = useState('');
    const [destination, setDestination] = useState('');
    const [destinationId, setDestinationId] = useState(0);
    const [destinationLabel, setDestinationLabel] = useState('');
    const [toLat, setToLat] = useState('');
    const [toLng, setTolng] = useState('');

    const [date, setDate] = useState('');
    const [showDate, setShowDate] = useState('');

    const [price, setPrice] = useState(0);
    const [priceRecommended, setPriceRecommended] = useState(0);
    const [maxPrice, setMaxPrice] = useState(0);
    const [minPrice, setMinPrice] = useState(0);
    const [originalPrice, setOriginalPrice] = useState(0);

    const [newPrice, setNewPrice] = useState(0);
    const [priceLimit, setPriceLimit] = useState(false);
    const [comment, setComment] = useState('');
    const [genderId, setGenderId] = useState('all');
    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [loadingAddress, setLoadingAddress] = useState(false);

    const modalizeRef = useRef();
    const modalizeRef1 = useRef();
    const modalizeRef2 = useRef();
    const modalizeRef3 = useRef();
    const refRbSheet = useRef();
    const contentRef = useRef();
    const contentRef1 = useRef();
    const contentRef2 = useRef();
    const contentRef3 = useRef();
    const refRBSheetReasons = useRef();
    const IsFocused = useIsFocused();
    const modalizeRefRat = useRef();
    const contentRefRat = useRef();

    const [dectecting, setDetecting] = useState(false)
    const [gender, setGender] = useState('');
    const [tripBegin, setTripBegin] = useState(false)
    const [driveArrived, setDriveArrived] = useState(false)
    const [pending, setPending] = useState(false)
    const [trip, setTrip] = useState({})
    const [distance, setDistance] = useState('');
    const [duration, setDuration] = useState('');
    const reasonss = useSelector(state => state.general.clientReasons);
    const working_cities = useSelector(state => state.general.working_cities);
    const [reasons, setReasons] = useState(reasonss);
    const [reasonsId, setReasonId] = useState(null);
    const [reasonText, setReasonText] = useState(strings('lang.message15'));
    const [starCount, setStarCount] = useState(0);
    const [review, setReview] = useState('');

    const [viewId, setViewId] = useState(1);
    const translateY = useRef(new Animated.Value(0)).current;
    const [isMapMoving, setIsMapMoving] = useState(false);
    const [userInteracted, setUserInteracted] = useState(false);
    const [initialAnimationCompleted, setInitialAnimationCompleted] = useState(false);

    const [nearbyDrivers, setNearbyDrivers] = useState([]);

    const [token, setToken] = useState('');
    const [mapLoaded, setMapLoaded] = useState(false);
    const [isPointInPolygon, setIsPointInPolygon] = useState(false);
    const [newMessgae, setNewMessgae] = useState(false);
    const [user, setUser] = useState({});


    const dispatch = useDispatch();

    const trips = useSelector((state) => state.trips);
    const general = useSelector((state) => state.general);
    const currentLat = useSelector((state) => state.socket.currentLat);
    const currentLng = useSelector((state) => state.socket.currentLat);
    const five_offer = useSelector(state => state.general.five_offer);
    const newMessage = useSelector((state) => state.message);

    let DriverOfferss = [];

    const loginfirst = async () => {
        Toaster(
            'top',
            'danger',
            Red,
            strings('lang.mustLogin'),
            White,
            1500,
            screenHeight / 15,
        );
    }

    useEffect(() => {
        console.log('Message reducer In Screen', newMessage);
        if (newMessage.message.chat_message) {
            setNewMessgae(true)
        }
    }, [newMessage]);

    //To Cancel Offer After 1 Min
    useEffect(() => {
        const timerId = setInterval(() => {
            let date = moment().format('YYYY-MM-DD HH:mm:ss');
            // console.log('date', date.slice(0, 20));
            let driverOfferss = [...DriverOffers]
            // console.log('DriverOfferss1', DriverOffers);
            const index = DriverOffers.findIndex(arrayItem => arrayItem.offer_end_time === date.slice(0, 20));
            // const index = DriverOffers.findIndex(arrayItem => arrayItem.offer_end_time === "2023-12-10 17:06:46");
            if (index !== -1) {
                console.log(index);
                DriverOfferss.splice(index, 1);
                setDriverOffers(DriverOfferss)
                if (driverOfferss.length - 1 == 0) {
                    setModalVisible(false)
                }
            }
        }, 1000)
        return () => clearInterval(timerId);
    }, [DriverOffers])

    //To Handle Socket IO
    useEffect(() => {
        const handleSocket = async () => {

            console.log('tripsio', trips);
            if (trips.offer.offer) {
                console.log('new');
                setDriverOffers([...DriverOffers, trips.offer.offer])
                // setModalVisible(false)
                setModalVisible(true)
                console.log('DriverOffers', [...DriverOffers, trips.offer.offer]);
                dispatch({ type: CLEAROFFERS });
            }
            if (trips.offerUpdated.offer) {
                console.log('updated');
                let DriverOfferss = [...DriverOffers];
                console.log('DriverOfferss', DriverOfferss);
                const index = DriverOfferss.findIndex(arrayItem => arrayItem.id === trips.offerUpdated.offer.id);
                console.log('index', index);
                if (trips.offerUpdated.offer.status == "pending") {
                    if (index != -1) {
                        DriverOfferss[index] = trips.offerUpdated.offer;
                        console.log('DriverOfferss', DriverOfferss);
                        setDriverOffers(DriverOfferss)
                        console.log('2222222222');
                        dispatch({ type: CLEAROFFERS });
                    }
                    else {
                        console.log('1111111111');
                        setDriverOffers([...DriverOffers, trips.offerUpdated.offer])
                        console.log('DriverOfferss', [...DriverOffers, trips.offerUpdated.offer]);
                        dispatch({ type: CLEAROFFERS });
                    }
                    // setModalVisible(false)
                    setModalVisible(true)
                }
                else if (trips.offerUpdated.offer.status == 'rejected') {
                    if (index !== -1) {
                        console.log('DriverOfferss1', DriverOfferss);
                        console.log(index);
                        DriverOfferss.splice(index, 1);
                        console.log('DriverOfferss2', DriverOfferss);
                        setDriverOffers(DriverOfferss)
                        console.log('asd', DriverOfferss.length);
                        dispatch({ type: CLEAROFFERS });
                        if (DriverOfferss.length == 0) {
                            setModalVisible(false)
                        }
                    }
                    // setLoadingMore(false)
                }
                console.log('DriverOffers', DriverOfferss);
                dispatch({ type: CLEAROFFERS });
            }
            if (trips.updatedDailyTrip.daily_trip) {
                if (trips.updatedDailyTrip.daily_trip.id == trip.id) {
                    setTrip(trips.updatedDailyTrip.daily_trip)
                    if (trips.updatedDailyTrip.daily_trip.status == "finished") {
                        setLoadingMore(false)
                        modalizeRefRat.current.open();
                        setViewId(0)
                        // modalizeRef2.current.close();
                        // modalizeRef3.current.close();
                        setTripBegin(false)
                        setDriveArrived(false)
                        setDetecting(false)
                        setDriverOffers([])
                        setModalVisible(false)
                    }
                    if (trips.updatedDailyTrip.daily_trip.driver_offer == null) {
                        setLoadingMore(false)
                        modalizeRefRat.current.open();
                        setViewId(2)
                        // modalizeRef2.current.close();
                        // modalizeRef3.current.close();
                        modalizeRefRat.current.close()
                        setDriveArrived(false)
                        setDetecting(true)
                    }
                }
            }

        }

        handleSocket()
    }, [trips]);

    useEffect(() => {
        const requestLocationPermission = async () => {
            try {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                    {
                        'title': 'Location Permission',
                        'message': 'MyMapApp needs access to your location'
                    }
                )

                if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                    get_Lat_User()
                    // Alert.alert("Please allow location")
                } else {
                    console.log("Location permission denied")
                    setLoading(false)

                }
            } catch (err) {
                console.warn(err)
                setLoading(false)
            }
        }

        const RequestIosPermissio = () => {
            Geolocation.requestAuthorization('always').then((res) => {
                console.log('res', res);
                if (res == 'denied') {
                    //  Alert.alert("Please allow location to continuo")/
                    // Linking.openURL('app-settings:');
                    showCustomAlert()
                }
            });
            setLoading(false)
        }

        const showCustomAlert = () => {
            Alert.alert(
                'Permission Alert',
                'The app will not work properly without this permission ',
                [
                    {
                        text: 'Access permission from settings',
                        onPress: () => {
                            console.log('start permission');
                            Linking.openURL('app-settings:');
                            // get_Lat_User()
                        },
                    },
                    {
                        text: 'Cancel',
                        onPress: () => {
                            console.log('exit');
                            props.navigation.navigate('Home')
                        },
                    },
                ],
                { cancelable: false }
            );
        };

        const get_Lat_User = async () => {
            // Geolocation.requestAuthorization();
            // Geolocation.setRNConfiguration({
            //   skipPermissionRequests: false,
            //   authorizationLevel: 'whenInUse',
            // });
            if (Platform.OS == 'ios') {
                RequestIosPermissio()

            }

            Geolocation.getCurrentPosition(
                (position) => {
                    console.log('position', position);
                    var lat = parseFloat(position.coords.latitude)
                    var longi = parseFloat(position.coords.longitude)
                    var initialRegion = {
                        latitude: lat,
                        longitude: longi,
                        longitudeDelta: 0.01 * ASPECT_RATIO,
                        latitudeDelta: 0.01
                    }
                    SetRegion(initialRegion)
                    SetLat(lat)
                    SetLng(longi)
                    dispatch(updateLocation(lat, longi));

                },
                (error) => {
                    console.log('error', error);

                },
                { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 1000 },
            );
            setLoading(false)
        }

        if (Platform.OS == 'ios') {
            get_Lat_User()
        }
        else {
            requestLocationPermission();
        }
    }, []);

    useEffect(() => {

        if (props.route.params.pending == true) {
            setTrip(props.route.params.pendingTrip)
            if (props.route.params.pendingTrip.driver_offer == null) {
                setDetecting(true)
            }
            console.log('1111111', props.route.params.pendingTrip);
        }

        const getDriverOffers = async (id) => {
            setLoading(true)
            try {
                let response = await dispatch(dailyTripsActions.getDriverOffers(id));
                if (response.success == true) {
                    if (response.data.length == 0) {

                    } else {
                        setModalVisible(!modalVisible)
                        setDriverOffers(response.data)
                    }
                }
                else {
                    if (response.message) {
                        // Toaster(
                        //     'top',
                        //     'danger',
                        //     Red,
                        //     response.message,
                        //     White,
                        //     1500,
                        //     screenHeight / 15,
                        // );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getData = async () => {
            let gender = await AsyncStorage.getItem('gender');
            setGender(gender);
            let token = await AsyncStorage.getItem('token')
            setToken(token)
        };

        const getDailyTripsTypes = async () => {
            setLoading(true)
            try {
                let response = await dispatch(dailyTripsActions.getDailyTripsTypes());
                if (response.success == true) {
                    setCars(response.data)
                    if (carID) {
                        // setCarID(0)
                    }
                    else {
                        // setCarID(response.data[0] ? response.data[0].id : 0)
                    }
                }
                else {
                    if (response.message) {
                        // Toaster(
                        //     'top',
                        //     'danger',
                        //     Red,
                        //     response.message,
                        //     White,
                        //     1500,
                        //     screenHeight / 15,
                        // );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getCities = async () => {
            setLoading(true)
            try {
                let response = await dispatch(cityTripsActions.getCities());
                if (response.success == true) {
                    setCities(response.data)
                }
                else {
                    if (response.message) {
                        // Toaster(
                        //     'top',
                        //     'danger',
                        //     Red,
                        //     response.message,
                        //     White,
                        //     1500,
                        //     screenHeight / 15,
                        // );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getUser = async () => {
            setLoading(true)
            try {
                let response = await dispatch(generalActions.getUser());
                if (response.success == true) {
                    setUser(response.data)
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getData();
        getDailyTripsTypes();
        getCities();
        getUser();
        getNearbyDrivers();

        if (props.route.params.source == true) {
            if (props.route.params.addressDesription) {
                setSource(props.route.params.addressDesription);
                setSourceId(props.route.params.addressId);
                setSourceLabel(props.route.params.addressLabel);
                setFromLat(props.route.params.lat);
                setFromLng(props.route.params.lng);
                getDistance(props.route.params.lat, props.route.params.lng, toLat, toLng, carID)
                for (let city of working_cities) {
                    let isPointInPolygon = geolib.isPointInPolygon({ latitude: props.route.params.lat, longitude: props.route.params.lng }, city.coordinates)
                    setIsPointInPolygon(isPointInPolygon)
                    if (isPointInPolygon) {
                        break;
                    }
                }
                SetRegion(props.route.params.region)
            }
        }
        if (props.route.params.source == false) {
            if (props.route.params.addressDesription) {
                setDestination(props.route.params.addressDesription);
                setDestinationId(props.route.params.addressId);
                setDestinationLabel(props.route.params.addressLabel);
                setToLat(props.route.params.lat);
                setTolng(props.route.params.lng);
                getDistance(fromLat, fromLng, props.route.params.lat, props.route.params.lng, carID)
            }
        }

        if (props.route.params.pending == true) {
            console.log('22222222');
            // setTrip(props.route.params.pendingTrip)
            setPrice(props.route.params.pendingTrip.client_price)
            getDriverOffers(props.route.params.pendingTrip.id);
            // modalizeRef.current.close()
            if (props.route.params.pendingTrip.driver_offer) {
                // modalizeRef3.current.open()
                setDriveArrived(true)
                setViewId(3)
                setReasonText('')
            }
            else {
                setViewId(2)
                setDriveArrived(false)
                // modalizeRef2.current.open()
            }
            // setDetecting(true)
            setTripBegin(true)
            setLoadingMore(false)
        }
        else {
            setTrip({})
            setPrice(0)
            // modalizeRef.current.open()
            // modalizeRef2.current.close()
            // modalizeRef3.current.close()
            setViewId(1)
            setDetecting(false)
            setTripBegin(false)
            setLoadingMore(false)
        }

        // modalizeRef3.current.open();

    }, [props, IsFocused]);



    const RequestIosPermissio = () => {
        Geolocation.requestAuthorization('always').then((res) => {
            console.log('res', res);
            if (res == 'denied') {
                //  Alert.alert("Please allow location to continuo")/
                // Linking.openURL('app-settings:');
                props.navigation.navigate('Home')
            }
        });
        setLoading(false)
    }

    const get_Lat_User = async () => {
        // Geolocation.requestAuthorization();
        // Geolocation.setRNConfiguration({
        //   skipPermissionRequests: false,
        //   authorizationLevel: 'whenInUse',
        // });
        if (Platform.OS == 'ios') {
            RequestIosPermissio()

        }
        // console.log('SetRegion', region);
        Geolocation.getCurrentPosition(
            async (position) => {
                console.log('position', position);
                var lat = parseFloat(position.coords.latitude)
                var longi = parseFloat(position.coords.longitude)
                var initialRegion = {
                    latitude: lat,
                    longitude: longi,
                    longitudeDelta: 0.01 * ASPECT_RATIO,
                    latitudeDelta: 0.01
                }
                SetRegion(initialRegion)
                SetLat(lat)
                SetLng(longi)
                Geocoder.init("AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I");
                await Geocoder.from(initialRegion.latitude, initialRegion.longitude, 'ar')
                    .then(json => {
                        var addressComponent = json.results[0].formatted_address;
                        console.log(addressComponent);
                        setSource(addressComponent);
                        setSourceId(0);
                        setSourceLabel('');
                        setFromLat(initialRegion.latitude);
                        setFromLng(initialRegion.longitude);
                        getDistance(lat, longi, toLat, toLng, carID)
                    })
                    .catch(error => console.log('error', error));
                for (let city of working_cities) {
                    let isPointInPolygon = geolib.isPointInPolygon({ latitude: initialRegion.latitude, longitude: initialRegion.longitude }, city.coordinates)
                    setIsPointInPolygon(isPointInPolygon)
                    if (isPointInPolygon) {
                        setLoadingMore(false)
                        // Animated.timing(translateY, {
                        //     toValue: 0,
                        //     duration: 150,
                        //     useNativeDriver: true,
                        // }).start();
                        setLoadingAddress(false)
                        return;
                    }
                }

            },
            (error) => {
                console.log('error', error);

            },
            { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 10000 },
        );
        setLoading(false)
    }

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                // props.navigation.openDrawer();
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    const createNewTrip = async () => {

        if (!token) {
            loginfirst()
        }
        else if ((sourceId == 0 && (fromLat == '' || fromLng == '' || source == '')) || (destinationId == 0 && (toLat == '' || toLng == '' || destination == '')) || priceLimit) {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.pleaseCompleteData'),
                White,
                1500,
                screenHeight / 50,
            );
        }
        else {
            try {
                setLoadingMore(true)
                let response = await dispatch(dailyTripsActions.createNewTrip(
                    carID,
                    sourceId == 0 ? fromLat : null,
                    sourceId == 0 ? fromLng : null,
                    sourceId == 0 ? source : null,
                    destinationId == 0 ? toLat : null,
                    destinationId == 0 ? toLng : null,
                    destinationId == 0 ? destination : null,
                    comment,
                    price,
                    sourceId != 0 ? sourceId : null,
                    destinationId != 0 ? destinationId : null,
                    duration,
                    distance * 1000,
                    genderId,
                    maxPrice,
                    minPrice,
                    originalPrice
                ));

                if (response.success == true) {
                    // Toaster(
                    //     'top',
                    //     'success',
                    //     DarkGreen,
                    //     strings('lang.Orderconfirmed'),
                    //     White,
                    //     1500,
                    //     screenHeight / 50,
                    // );
                    setTrip(response.data)
                    // modalizeRef2.current.open()
                    // modalizeRef1.current.close()
                    setViewId(2)
                    setDriveArrived(false)
                    setDetecting(true)
                    setTripBegin(true)
                    setLoadingMore(false)
                    getNearbyDrivers();
                    setReasonText(strings('lang.message15'));

                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false)
                }
            } catch (err) {
                console.log(err);
                setLoadingMore(false)
            }
        }
    }

    // const calculateDistance = (lattitude1, longittude1, lattitude2, longittude2) => {

    //     const toRadian = n => (n * Math.PI) / 180

    //     let lat2 = lattitude2
    //     let lon2 = longittude2
    //     let lat1 = lattitude1
    //     let lon1 = longittude1

    //     console.log(lat1, lon1 + "===" + lat2, lon2)
    //     let R = 6371  // km
    //     let x1 = lat2 - lat1
    //     let dLat = toRadian(x1)
    //     let x2 = lon2 - lon1
    //     let dLon = toRadian(x2)
    //     let a =
    //         Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    //         Math.cos(toRadian(lat1)) * Math.cos(toRadian(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2)
    //     let c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    //     let d = R * c
    //     console.log("distance==?", d)
    //     // setPrice(d);
    //     return d
    // }

    const updatePrice = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(dailyTripsActions.updatePrice(
                trip.id,
                price
            ));

            if (response.success == true) {
                // setModalVisible(true)
                setLoadingMore(false)
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const changeOfferStatus = async (status, offer_id) => {
        try {
            setLoadingMore(true)
            let response = await dispatch(dailyTripsActions.changeOfferStatus(
                trip.id,
                status,
                offer_id
            ));

            if (response.success == true) {
                if (status == 'rejected') {
                    // let driverOfferss = [...DriverOffers]
                    // console.log('DriverOfferss', DriverOfferss);

                    // const index = driverOfferss.findIndex(arrayItem => arrayItem.id === offer_id);
                    // if (index !== -1) {
                    //     console.log('DriverOfferss1', DriverOfferss);
                    //     console.log(index);
                    //     DriverOfferss.splice(index, 1);
                    //     console.log('DriverOfferss2', DriverOfferss);
                    //     setDriverOffers(DriverOfferss)
                    //     console.log('asd', driverOfferss.length);
                    //     if (driverOfferss.length - 1 == 0) {
                    //         setModalVisible(false)
                    //     }
                    // }
                    setLoadingMore(false)
                }
                else {
                    setDriverOffers([])
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Successfullyaccepted'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    setReasonText('')
                    setModalVisible(!modalVisible);
                    // modalizeRef3.current.open();
                    setViewId(3)
                    setTrip(response.data)
                    // modalizeRef2.current.close();
                    setDriveArrived(true);
                    // setDriver(false);
                    setDetecting(false);
                    setLoadingMore(false);
                }

            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const cancelTrip = async () => {
        refRBSheetReasons.current.close();
        if (reasonsId == null && reasonText == '') {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.Pleasechooseorwriteareasonforcancellation'),
                White,
                1500,
                screenHeight / 50,
            );
        } else {
            try {
                setLoadingMore(true)
                let response = await dispatch(dailyTripsActions.cancelTrip(
                    trip.id,
                    reasonsId,
                    reasonText
                ));

                if (response.success == true) {
                    setLoadingMore(false)
                    // modalizeRef.current.open();
                    // modalizeRef2.current.close();
                    // modalizeRef3.current.close();
                    setSource('')
                    setSourceId(0)
                    setSourceLabel('')
                    setDuration('')
                    setDistance('')
                    setPrice('')
                    setDestination('')
                    setDestinationId(0)
                    setDestinationLabel('')
                    setComment('')
                    setFromLat('')
                    setFromLng('')
                    setToLat('')
                    setTolng('')
                    setTrip({})
                    setTripBegin(false)
                    setDriveArrived(false)
                    setDetecting(false)
                    setDriverOffers([])
                    setModalVisible(false)
                    setViewId(1);
                    props.navigation.push('MapAddress', { screen: 'Home', pending: false, pendingTrip: null })
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false)
                }
            } catch (err) {
                console.log(err);
                setLoadingMore(false)
            }
        }
    }

    // const getLocationWhenCancelTrip = async () => {
    //     setLoadingMore(true)

    //     setLoadingAddress(true)

    //     Geolocation.getCurrentPosition(
    //         async (position) => {
    //             console.log('position', position);
    //             var lat = parseFloat(position.coords.latitude)
    //             var longi = parseFloat(position.coords.longitude)
    //             var initialRegion = {
    //                 latitude: lat,
    //                 longitude: longi,
    //                 longitudeDelta: 0.01 * ASPECT_RATIO,
    //                 latitudeDelta: 0.01
    //             }
    //             SetRegion(initialRegion)
    //             SetLat(lat)
    //             SetLng(longi)
    //             Geocoder.init("AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I");
    //             await Geocoder.from(initialRegion.latitude + initialRegion.latitudeDelta, initialRegion.longitude, 'ar')
    //                 .then(json => {
    //                     var addressComponent = json.results[0].formatted_address;
    //                     console.log('var addressComponent', addressComponent);
    //                     setSource(addressComponent);
    //                     setSourceId(0);
    //                     setSourceLabel('');
    //                     setFromLat(initialRegion.latitude + initialRegion.latitudeDelta / 4);
    //                     setFromLng(initialRegion.longitude);
    //                     getDistance(addressComponent, destination, carID)
    //                 })
    //                 .catch(error => console.log('error', error));
    //             for (let city of working_cities) {
    //                 let isPointInPolygon = geolib.isPointInPolygon({ latitude: initialRegion.latitude + initialRegion.latitudeDelta / 4, longitude: initialRegion.longitude }, city.coordinates)
    //                 setIsPointInPolygon(isPointInPolygon)
    //                 if (isPointInPolygon) {
    //                     setLoadingMore(false)
    //                     Animated.timing(translateY, {
    //                         toValue: 0,
    //                         duration: 150,
    //                         useNativeDriver: true,
    //                     }).start();
    //                     setLoadingAddress(false)
    //                     return;
    //                 }
    //             }
    //             setLoadingMore(false)

    //         },
    //         (error) => {
    //             console.log('error', error);

    //         },
    //         { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 1000 },
    //     );




    // };

    const rateDriver = async () => {

        try {
            setLoadingMore(true)
            let response = await dispatch(dailyTripsActions.rateDriver(
                trip.id,
                starCount,
                review
            ));

            if (response.success == true) {

                setLoadingMore(false)
                setStarCount(0)

                setSource('')
                setSourceId(0)
                setSourceLabel('')
                setDuration('')
                setDistance('')
                setPrice('')
                setDestination('')
                setDestinationId(0)
                setDestinationLabel('')
                setComment('')
                setFromLat('')
                setFromLng('')
                setToLat('')
                setTolng('')
                setTrip({})
                setTripBegin(false)
                setDriveArrived(false)
                setDetecting(false)
                setDriverOffers([])
                setModalVisible(false)
                setViewId(1);
                modalizeRefRat.current.close();
                // modalizeRef.current.open();
                setViewId(1)

            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    const getDistance = async (fromLatt, fromLngg, toLatt, toLngg, carID) => {
        // if (source && destination) {
        // setLoadingMore(true)
        setLoadingAddress(true)
        let GOOGLE_DISTANCES_API_KEY = 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'
        // let ApiURL = "https://maps.googleapis.com/maps/api/distancematrix/json?";
        let mode = 'driving'
        // let params = `origins=${source}&destinations=${destination}&mode=${mode}&key=${GOOGLE_DISTANCES_API_KEY}`;
        // let finalApiURL = `${ApiURL}${encodeURI(params)}`;
        // console.log("finalApiURL:\n");
        // console.log(ApiURL);

        setLoadingAddress(false)

        // get duration/distance from base to each target
        try {
            // let response = await fetch(finalApiURL);
            // let responseJson = await response.json();
            // console.log("responseJson:\n");
            // console.log(responseJson);
            // setDistance(responseJson.rows[0].elements[0].distance.value / 1000)
            // setDuration(Math.round(responseJson.rows[0].elements[0].duration.value / 60))
            console.log('from lat', fromLatt, fromLngg);
            console.log('to lat', toLatt, toLngg);
            let dist = 0;
            let response = await dispatch(dailyTripsActions.getDistancee(
                fromLatt,
                fromLngg,
                toLatt, toLngg
            ));
            if (response.success == true) {
                console.log('response.success', response.data);
                dist = response.data.distance_m / 1000
                setDistance(response.data.distance_m / 1000)
                setDuration(response.data.duration)
            }

            try {
                let response = await dispatch(dailyTripsActions.calculatePrice(
                    dist,
                    carID
                ));

                if (response.success == true) {
                    setPrice(JSON.stringify(response.data.original_price))
                    setPriceRecommended(response.data.price)
                    setMaxPrice(response.data.max_price)
                    console.log('response.data.max_price', response.data.max_price);
                    console.log('response.data.price', response.data.price);
                    console.log('response.data.min_price', response.data.min_price);
                    setMinPrice(response.data.min_price)
                    setOriginalPrice(response.data.original_price)
                    setLoadingAddress(false)
                }
                else {
                    setLoadingAddress(false)
                }
            } catch (err) {
                console.log(err);
                setLoadingAddress(false)
            }

            // if (dist < general.settings.start_distance) {
            //     setPrice(general.settings.start_price)
            // }
            // else {
            //     let remainDistance = (dist) - general.settings.start_distance;
            //     setPrice(Math.round((general.settings.start_price + (remainDistance * general.settings.price_per_km))))
            // }
        } catch (error) {
            setLoadingAddress(false)
            console.error(error);
        }
        // }
    }



    const [isVisible, setIsVisible] = useState(true);
    const fadeAnim = useState(new Animated.Value(1))[0];

    useEffect(() => {
        if (isVisible) {

            const fadeInOut = () => {
                Animated.timing(
                    fadeAnim,
                    {
                        toValue: isVisible ? 1 : 0,
                        duration: 1000,
                        useNativeDriver: true,
                    }
                ).start(
                    () => {
                        setIsVisible(false)

                    }
                );
            }
            fadeInOut();

        } else {
            const fadeInOut = () => {
                Animated.timing(
                    fadeAnim,
                    {
                        toValue: isVisible ? 1 : 0,
                        duration: 1000,
                        useNativeDriver: true,
                    }
                ).start(
                    () => {
                        setIsVisible(true)

                    }
                );
            }
            fadeInOut();
        }

    }, [isVisible, fadeAnim]);


    // const handleRegionChange = () => {
    //     // if (userInteracted && initialAnimationCompleted) {
    //     if (userInteracted) {
    //         console.log('qwe');
    //         // Callback when the map region is changing
    //         // Animated.timing(translateY, {
    //         //     toValue: screenHeight / 1.5, // Adjust the value based on your UI design
    //         //     duration: 150,
    //         //     useNativeDriver: true,
    //         // }).start();
    //     }
    // };
    const [isFirstTime, setIsFirstTime] = useState(true);
    const [Positionn, setPositionn] = useState(true);



    const handleRegionChangeComplete = async (newRegion) => {
        // setPositionn(true)
        if (Platform.OS == 'android') {
            if (loadingAddress) {
                return;
            }
            if (isFirstTime) {

                setLoadingAddress(true)
                Geolocation.getCurrentPosition(
                    async position => {
                        console.log('position', position);
                        var lat = parseFloat(position.coords.latitude)
                        var longi = parseFloat(position.coords.longitude)
                        var initialRegion = {
                            latitude: lat,
                            longitude: longi,
                            longitudeDelta: 0.01 * ASPECT_RATIO,
                            latitudeDelta: 0.01
                        }
                        SetRegion(initialRegion)
                        SetLat(lat)
                        SetLng(longi)
                        const { latitude, longitude, } = position.coords;
                        await Geocoder.from(latitude, longitude, 'ar')
                            .then(json => {
                                var addressComponent = json.results[0].formatted_address;
                                console.log(json);
                                setSource(addressComponent);
                                setSourceId(0);
                                setSourceLabel('');
                                setFromLat(initialRegion.latitude);
                                setFromLng(initialRegion.longitude);
                                console.log('setFromLat', fromLat, fromLng);
                                getDistance(lat, longi, toLat, toLng, carID)
                                //   setAddressDesription(addressComponent)
                            })
                            .catch(error => console.log('error', error));
                        // try {
                        //     const res = await Geocoder.from(initialRegion.latitude, initialRegion.longitude,);
                        //     const addressComponent = res.results[0].formatted_address;
                        //     console.log('mostafa', position);
                        //     setSource(addressComponent);
                        //     setSourceId(0);
                        //     setFromLat(initialRegion.latitude);
                        //     setFromLng(initialRegion.longitude);
                        //     setSourceLabel('');
                        //     getDistance(addressComponent, destination, carID)
                        //   setAddressDesription(addressComponent)
                        for (let city of working_cities) {
                            let isPointInPolygon = geolib.isPointInPolygon({ latitude: initialRegion.latitude, longitude: initialRegion.longitude }, city.coordinates)
                            setIsPointInPolygon(isPointInPolygon)
                            console.log('setIsPointInPolygon', isPointInPolygon);
                            if (isPointInPolygon) {
                                setLoadingMore(false)
                                // Animated.timing(translateY, {
                                //     toValue: 0,
                                //     duration: 150,
                                //     useNativeDriver: true,
                                // }).start();
                                setLoadingAddress(false)
                                return;
                            }
                        }
                        setLoadingAddress(false)
                        // } catch (error) {
                        //     console.error(error);
                        //     setLoadingAddress(false)
                        // }
                    },
                    error => {
                        console.error(error);
                    },
                    { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
                );
                setLoadingAddress(false)
                setIsFirstTime(false);
            }

            setLoadingAddress(false)
        }


        // if (userInteracted && initialAnimationCompleted) {
        if (userInteracted) {
            if (!trip.id) {
                setLoadingMore(true)
                if (Platform.OS == 'ios') {
                    RequestIosPermissio()

                }
                // Callback when the map region change is complete
                console.log('changed');
                setLoadingAddress(true)
                var initial_Region = {
                    longitudeDelta: newRegion.longitudeDelta,
                    latitudeDelta: newRegion.latitudeDelta,
                    longitude: newRegion.longitude,
                    latitude: newRegion.latitude
                }
                // var initial_Region = {
                //     latitude: region.latitude - region.latitudeDelta / 2,
                //     longitude: region.longitude + region.longitudeDelta / 2,
                //     latitudeDelta: region.latitudeDelta,
                //     longitudeDelta: region.longitudeDelta
                // }

                SetRegion(initial_Region)
                SetLat(newRegion.latitude)
                SetLng(newRegion.longitude)

                Geocoder.init("AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I");
                await Geocoder.from(newRegion.latitude, newRegion.longitude, 'ar')
                    .then(json => {
                        var addressComponent = json.results[0].formatted_address;
                        console.log(json);
                        setSource(addressComponent);
                        setSourceId(0);
                        setSourceLabel('');
                        setFromLat(newRegion.latitude);
                        setFromLng(newRegion.longitude);
                        console.log('setFromLat', newRegion.latitude, newRegion.longitude);
                        getDistance(newRegion.latitude, newRegion.longitude, toLat, toLng, carID)
                        // calculateDistance(addressComponent, destination, carID)
                        //   setAddressDesription(addressComponent)
                    })
                    .catch(error => console.log('error', error));
                console.log('newRegion', newRegion);
                for (let city of working_cities) {
                    let isPointInPolygon = geolib.isPointInPolygon({ latitude: newRegion.latitude, longitude: newRegion.longitude }, city.coordinates)
                    setIsPointInPolygon(isPointInPolygon)
                    if (isPointInPolygon) {
                        setLoadingMore(false)
                        // Animated.timing(translateY, {
                        //     toValue: 0,
                        //     duration: 150,
                        //     useNativeDriver: true,
                        // }).start();
                        setLoadingAddress(false)
                        return;
                    }
                }
                setLoadingMore(false)
            }
            else {
                setLoadingAddress(true)
                var initial_Region = {
                    longitudeDelta: newRegion.longitudeDelta,
                    latitudeDelta: newRegion.latitudeDelta,
                    longitude: newRegion.longitude,
                    latitude: newRegion.latitude
                }
                // var initial_Region = {
                //     latitude: region.latitude - region.latitudeDelta / 2,
                //     longitude: region.longitude + region.longitudeDelta / 2,
                //     latitudeDelta: region.latitudeDelta,
                //     longitudeDelta: region.longitudeDelta
                // }

                SetRegion(initial_Region)
                SetLat(newRegion.latitude)
                SetLng(newRegion.longitude)
            }

            // Animated.timing(translateY, {
            //     toValue: 0,
            //     duration: 150,
            //     useNativeDriver: true,
            // }).start();
            setLoadingAddress(false)
        }
        // setPositionn(false)
    };

    const getNearbyDrivers = async () => {
        console.log('35', dectecting);
        // if (dectecting == true) {
        try {
            let response = await dispatch(dailyTripsActions.getNearbyDrivers());

            if (response.success == true) {
                setNearbyDrivers(response.data)
            }
            else {
                if (response.message) {

                }
            }
        } catch (err) {
            console.log(err);
        }

    }

    const [actionCount, setActionCount] = useState(0);

    useEffect(() => {
        const getDistance = async () => {
            // if (source && destination) {
            // setLoadingMore(true)
            setLoadingAddress(true)
            let GOOGLE_DISTANCES_API_KEY = 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'
            // let ApiURL = "https://maps.googleapis.com/maps/api/distancematrix/json?";
            let mode = 'driving'
            // let params = `origins=${source}&destinations=${destination}&mode=${mode}&key=${GOOGLE_DISTANCES_API_KEY}`;
            // let finalApiURL = `${ApiURL}${encodeURI(params)}`;
            // console.log("finalApiURL:\n");
            // console.log(ApiURL);

            setLoadingAddress(false)

            // get duration/distance from base to each target
            try {
                // let response = await fetch(finalApiURL);
                // let responseJson = await response.json();
                // console.log("responseJson:\n");
                // console.log(responseJson);
                // setDistance(responseJson.rows[0].elements[0].distance.value / 1000)
                // setDuration(Math.round(responseJson.rows[0].elements[0].duration.value / 60))

                let dist = 0;
                let response = await dispatch(dailyTripsActions.getDistancee(
                    fromLat, fromLng,
                    toLat, toLng
                ));
                if (response.success == true) {
                    console.log('response.success', response.data);
                    dist = response.data.distance_m / 1000
                    setDistance(response.data.distance_m / 1000)
                    setDuration(response.data.duration)
                }

                try {
                    let response = await dispatch(dailyTripsActions.calculatePrice(
                        fromLat, fromLng, toLat, toLng,
                        carID
                    ));

                    if (response.success == true) {
                        setPrice(JSON.stringify(response.data.original_price))
                        setPriceRecommended(response.data.price)
                        setMaxPrice(response.data.max_price)
                        console.log('response.data.max_price', response.data.max_price);
                        console.log('response.data.price', response.data.price);
                        console.log('response.data.min_price', response.data.min_price);
                        setMinPrice(response.data.min_price)
                        setOriginalPrice(response.data.original_price)
                        setLoadingAddress(false)
                    }
                    else {
                        setLoadingAddress(false)
                    }
                } catch (err) {
                    console.log(err);
                    setLoadingAddress(false)
                }

                // if (dist < general.settings.start_distance) {
                //     setPrice(general.settings.start_price)
                // }
                // else {
                //     let remainDistance = (dist) - general.settings.start_distance;
                //     setPrice(Math.round((general.settings.start_price + (remainDistance * general.settings.price_per_km))))
                // }
            } catch (error) {
                setLoadingAddress(false)
                console.error(error);
            }
            // }
        }
        // getDistance()
        // Initialize Geocoder with your API key (required)
        Geocoder.init("AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I");

        // Function to get the device's address
        const getAddress = () => {
            if (Platform.OS == 'ios') {
                setLoadingAddress(true)
                Geolocation.getCurrentPosition(
                    async position => {
                        console.log('position', position);
                        var lat = parseFloat(position.coords.latitude)
                        var longi = parseFloat(position.coords.longitude)
                        var initialRegion = {
                            latitude: lat,
                            longitude: longi,
                            longitudeDelta: 0.01 * ASPECT_RATIO,
                            latitudeDelta: 0.01
                        }
                        SetRegion(initialRegion)
                        SetLat(lat)
                        SetLng(longi)
                        const { latitude, longitude, } = position.coords;
                        await Geocoder.from(latitude, longitude, 'ar')
                            .then(json => {
                                var addressComponent = json.results[0].formatted_address;
                                console.log(json);
                                setSource(addressComponent);
                                setSourceId(0);
                                setSourceLabel('');
                                setFromLat(lat);
                                setFromLng(longi);
                                getDistance(lat, longi, toLat, toLng, carID)
                                //   setAddressDesription(addressComponent)
                            })
                            .catch(error => console.log('error', error));
                        // try {
                        //     const res = await Geocoder.from(initialRegion.latitude, initialRegion.longitude,);
                        //     const addressComponent = res.results[0].formatted_address;
                        //     console.log('mostafa', position);
                        //     setSource(addressComponent);
                        //     setSourceId(0);
                        //     setFromLat(initialRegion.latitude);
                        //     setFromLng(initialRegion.longitude);
                        //     setSourceLabel('');
                        //     getDistance(addressComponent, destination, carID)
                        //   setAddressDesription(addressComponent)
                        for (let city of working_cities) {
                            let isPointInPolygon = geolib.isPointInPolygon({ latitude: initialRegion.latitude, longitude: initialRegion.longitude }, city.coordinates)
                            setIsPointInPolygon(isPointInPolygon)
                            console.log('setIsPointInPolygon', isPointInPolygon);
                            if (isPointInPolygon) {
                                setLoadingMore(false)
                                // Animated.timing(translateY, {
                                //     toValue: 0,
                                //     duration: 150,
                                //     useNativeDriver: true,
                                // }).start();
                                setLoadingAddress(false)
                                return;
                            }
                        }
                        setLoadingAddress(false)
                        // } catch (error) {
                        //     console.error(error);
                        //     setLoadingAddress(false)
                        // }
                    },
                    error => {
                        console.error(error);
                    },
                    { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
                );
                setLoadingAddress(false)
                setIsFirstTime(false);
            }
        };

        // Call getAddress when the component mounts
        getAddress();
    }, []);


    const handleMapLayout = () => {
        // Set the userInteracted flag when the map layout is complete
        setUserInteracted(true);
    };

    // useEffect(() => {
    //     // Ensure the view is initially visible when the screen loads
    //     Animated.timing(translateY, {
    //         toValue: 0,
    //         duration: 300,
    //         useNativeDriver: true,
    //     }).start(() => {
    //         setInitialAnimationCompleted(true);
    //     });
    // }, []);

    return (
        <View style={{ flex: 1, alignItems: 'center', backgroundColor: WhiteGery }}>
            {loadingMore ? <Loading /> : <></>}

            {dectecting
                ?
                <>
                    <Image source={require('../images/tooltip_pulse.gif')} style={{ opacity: .25, resizeMode: "contain", width: '40%', height: '40%', zIndex: 2, position: 'absolute', start: '30%', top: '11%' }} />
                    <View style={{ zIndex: 1000000, width: screenWidth, position: "absolute", top: screenHeight / 2, alignItems: 'center', justifyContent: 'center' }} >
                        <Animated.Text
                            style={{
                                opacity: fadeAnim,
                                color: DarkBlue,
                                fontFamily: appFontBold, fontSize: screenWidth / 20
                            }}>
                            {isVisible ? strings('lang.Searchingforadriver') : strings('lang.Searchingforadriver')}
                        </Animated.Text>

                    </View>
                </>


                :
                (trip == {} || trip && !trip.driver_offer || trip && trip.driver_offer.status != 'accepted')
                    ?
                    <>
                        <View style={{
                            zIndex: 2, position: 'absolute', top: '16%', padding: '3%',
                            backgroundColor: DarkBlue,
                            borderRadius: 15, width: '50%'
                        }}>
                            {/* {loadingAddress ? <Loading /> : */}
                            <Text
                                numberOfLines={2}
                                style={{
                                    color: White, textAlign: I18nManager.isRTL ? 'left' : 'right',
                                    fontFamily: appFontBold, fontSize: screenWidth / 33
                                }}>
                                {source}
                            </Text>
                            {/* } */}
                        </View>
                        <Image source={require('../images/darklocation.png')} style={{ resizeMode: "contain", width: '7%', height: '7%', zIndex: 2, position: 'absolute', start: Platform.OS == 'ios' ? '45%' : '44%', top: Platform.OS == 'ios' ? '23%' : '22%' }} />
                    </>
                    :
                    <></>
            }


            {/* <Image source={require('../images/Group_7.png')} style={{ resizeMode: "contain", width: '7%', height: '7%', zIndex: 2, position: 'absolute', start: '46%', top: '16%' }} />
            <Image source={require('../images/Group_6.png')} style={{ resizeMode: "contain", width: '7%', height: '7%', zIndex: 2, position: 'absolute', start: '46%', top: '32%' }} />
            <Image source={require('../images/Group_5.png')} style={{ resizeMode: "contain", width: '7%', height: '7%', zIndex: 2, position: 'absolute', start: '70%', top: '26%' }} />
            <Image source={require('../images/Group_5.png')} style={{ resizeMode: "contain", width: '7%', height: '7%', zIndex: 2, position: 'absolute', start: '20%', top: '26%' }} /> */}

            {tripBegin == true ?
                viewId != 3
                    ?
                    <Header
                        title={driveArrived == false ? strings('lang.Searchforadriver') : ''}
                        HideMore={driveArrived == false ? true : false}
                        cancelPress={() => {
                            if (driveArrived == false) {
                                if (viewId == 2) {
                                    cancelTrip()
                                }
                                else {
                                    refRBSheetReasons.current.open();
                                }
                            }
                        }}
                        backPress={() => {
                            if (driveArrived == false) {
                                if (viewId == 2) {
                                    cancelTrip()
                                }
                                else {
                                    refRBSheetReasons.current.open();
                                }
                            }
                        }}
                    />
                    :
                    <></>
                :
                <>
                    <View style={{ position: 'absolute', top: screenHeight / 20, start: '6%', backgroundColor: White, width: screenWidth / 10, zIndex: 1000, alignSelf: 'center', height: screenWidth / 10, borderRadius: screenWidth / 20, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                        <TouchableOpacity
                            onPress={() => { props.navigation.navigate('Home', {}); }}
                            style={{ width: screenWidth / 8, alignItems: 'center', justifyContent: 'center', height: '100%', }}>
                            <Image source={require('../images/filter.png')} style={{ height: '50%', resizeMode: 'contain', tintColor: Black, }} />
                        </TouchableOpacity>
                    </View>
                </>
            }



            <View style={styles.container}>
                <MapView
                    region={region}
                    // onRegionChange={onRegionChange}
                    // onRegionChangeComplete={onRegionChangeComplete.bind(this)}
                    // moveOnMarkerPress={true}
                    // onMarkerPress={() => { setMapp(true) }}
                    // height: driver || details ? screenHeight / 1.66 : screenHeight / 1.95,
                    // onRegionChangeComplete={dectecting ? null : onRegionChangeComplete.bind(this)}
                    // onRegionChangeComplete={null}
                    // onRegionChange={dectecting ? null : handleRegionChange}
                    onRegionChangeComplete={dectecting ? null : handleRegionChangeComplete.bind(this)}
                    style={{ height: Platform.OS == 'ios' ? screenHeight / 2 : screenHeight / 2, width: screenWidth, zIndex: 1 }}
                    // style={{ height: screenHeight / 1.8, width: screenWidth, zIndex: 1 }}
                    pitchEnabled={dectecting ? false : true}
                    rotateEnabled={dectecting ? false : true}
                    scrollEnabled={dectecting ? false : true}
                    zoomEnabled={true}
                    // minZoomLevel={17}
                    // cameraZoomRange={200}
                    // onLayout={handleMapLayout}
                    onMapReady={async () => setUserInteracted(true)}
                // showsUserLocation={true}
                // showsMyLocationButton={true}
                // showsUserLocation={true}
                // ref={ref => this.map = ref}
                >

                    {working_cities.map((city) => {
                        return (
                            <Polygon
                                // coordinates={closedAreas}
                                fillColor='rgba(119, 119, 119,0.2)'
                                strokeWidth={2}
                                strokeColor={DarkBlue}
                                // style={{ width: 100, height: 100, }}
                                coordinates={city.coordinates}
                            />
                        );
                    }
                    )}


                    {trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lat && trip.driver_offer.driver.current_lng
                        &&
                        <Marker
                            coordinate={{
                                latitude: trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lat,
                                longitude: trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lng
                            }}
                            style={{ width: 20, height: 20 }}
                            resizeMode="contain"
                            image={require('../images/carxx.png')}
                        />
                    }
                    {(trip.from_address_id || trip.from_lat) && (trip.from_address_id || trip.from_lng)
                        &&
                        <Marker
                            coordinate={{
                                latitude: trip.from_address_id ? trip.address_from.lat : trip.from_lat,
                                longitude: trip.from_address_id ? trip.address_from.lng : trip.from_lng
                            }}
                            style={{ width: 20, height: 20 }}
                            resizeMode="contain"
                            image={require('../images/Group-288.png')}
                        />
                    }
                    {(trip.to_address_id || trip.to_lat) && (trip.to_address_id || trip.to_lng)
                        &&
                        <Marker
                            coordinate={{
                                latitude: trip.to_address_id ? trip.address_to.lat : trip.to_lat,
                                longitude: trip.to_address_id ? trip.address_to.lng : trip.to_lng
                            }}
                            style={{ width: 20, height: 20 }}
                            image={require('../images/Group-277.png')}
                        />
                    }
                    {trip && trip.status != "started" && trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lat && trip.driver_offer.driver.current_lng && (trip.from_address_id || trip.from_lat) && (trip.from_address_id || trip.from_lng)
                        &&
                        <MapViewDirections
                            origin={{ latitude: trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lat, longitude: trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lng }}
                            destination={{ latitude: trip.from_address_id ? trip.address_from.lat : trip.from_lat, longitude: trip.from_address_id ? trip.address_from.lng : trip.from_lng }}
                            // origin={origin}
                            // destination={destination}
                            apikey={'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'}
                            strokeWidth={3}
                            strokeColor={DarkBlue}
                        />
                    }

                    {trip && (trip.to_address_id || trip.to_lat) && (trip.to_address_id || trip.to_lng) && (trip.from_address_id || trip.from_lat) && (trip.from_address_id || trip.from_lng)
                        &&
                        <MapViewDirections
                            origin={{ latitude: trip.from_address_id ? trip.address_from.lat : trip.from_lat, longitude: trip.from_address_id ? trip.address_from.lng : trip.from_lng }}
                            destination={{ latitude: trip.to_address_id ? trip.address_to.lat : trip.to_lat, longitude: trip.to_address_id ? trip.address_to.lng : trip.to_lng }}
                            // origin={origin}
                            // destination={destination}
                            apikey={'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'}
                            strokeWidth={3}
                            strokeColor={DarkYellow}
                        />
                    }

                    {viewId != 3
                        &&
                        nearbyDrivers.map((driver) => {
                            return (
                                <Marker
                                    coordinate={{
                                        latitude: driver.current_lat,
                                        longitude: driver.current_lng
                                    }}
                                    style={{ width: 20, height: 20 }}
                                    resizeMode="contain"
                                    image={require('../images/carxx.png')}
                                />
                            );
                        })
                    }

                    {/* <Marker
                    anchor={{ x: 0.5, y: 1 }} // Center top anchor
                    coordinate={{ latitude: region.latitude + region.latitudeDelta / 4, longitude: region.longitude }}
                    title="Marker Title"
                    description="Marker Description"
                >
                    <Callout>
                        <View style={{ zIndex: 10000000000 }}>
                            <Text style={{ color: 'red' }}>Custom View on Top of Marker</Text>
                        </View>
                    </Callout>
                </Marker> */}
                </MapView>

            </View>

            {viewId == 1 &&
                <View style={{ zIndex: 1000000, width: screenWidth / 10, height: screenWidth / 10, position: "absolute", top: screenHeight / 9, left: '6%', borderRadius: screenWidth / 20, elevation: 20, shadowColor: DarkGrey, shadowOpacity: 1, shadowOffset: { width: 1, height: 1 }, backgroundColor: "white", alignItems: 'center', justifyContent: 'center' }} >
                    <TouchableOpacity
                        onPress={() => {
                            get_Lat_User()
                        }}
                    >
                        <Image
                            style={{ width: 25, height: 25, resizeMode: 'contain', tintColor: DarkBlue }}
                            source={require('../images/mylocation.png')}
                        />
                    </TouchableOpacity>
                </View>
            }
            {/* Primary Modalize */}
            {viewId == 1
                &&
                // <Animated.View
                //     style={[
                //         styles.bottomView,
                //         {
                //             transform: [{ translateY }],
                //             opacity: isMapMoving ? 0 : 1,
                //             height: (gender == 'انثي' || gender == 'female') ? (fromLat && fromLng && toLat && toLng)
                //                 ? screenHeight / 1.4 : screenHeight / 1.55 : (fromLat && fromLng && toLat && toLng) ?
                //                 screenHeight / 1.55 : screenHeight / 1.8,
                //             // height: (gender == 'انثي' || gender == 'female') ? screenHeight / 1.8 : screenHeight / 1.5,
                //             backgroundColor: WhiteGery,
                //             width: screenWidth,
                //             zIndex: 10000000
                //         },
                //     ]}
                // >
                <View
                    style={[
                        styles.bottomView,
                        {
                            // transform: [{ translateY }],
                            opacity: isMapMoving ? 0 : 1,
                            height: (gender == 'انثي' || gender == 'female') ? (fromLat && fromLng && toLat && toLng)
                                ? screenHeight / 1.4 : screenHeight / 1.55 : (fromLat && fromLng && toLat && toLng) ?
                                screenHeight / 1.55 : screenHeight / 1.8,
                            // height: (gender == 'انثي' || gender == 'female') ? screenHeight / 1.8 : screenHeight / 1.5,
                            backgroundColor: WhiteGery,
                            width: screenWidth,
                            zIndex: 10000000
                        },
                    ]}
                >
                    <KeyboardAwareScrollView
                        style={{ width: '100%', alignSelf: 'center', }}
                        showsVerticalScrollIndicator={false}
                    >
                        {loadingAddress
                            ?
                            <View style={{ backgroundColor: MediumGrey, opacity: .8, position: "absolute", zIndex: 10000, alignSelf: 'center', alignItems: 'center', width: screenWidth, height: 2 * screenHeight, }}>
                                <ActivityIndicator size={Platform.OS == 'ios' ? 100 : 50} color={DarkBlue} style={{ marginTop: screenHeight / 5 }} />
                            </View>
                            :
                            <></>}
                        <View
                            style={{ height: screenHeight / 8, width: screenWidth, paddingHorizontal: '1%', alignSelf: 'center', justifyContent: 'space-around', flexDirection: 'row', marginTop: '2%' }} >
                            <View style={carID == null ? styles.carsContainer : styles.carsContainer1}>
                                <Pressable onPress={() => { setCarID(null); }} style={{ alignItems: 'center', justifyContent: 'center' }} >
                                    <View style={styles.carsImageContainer}>
                                        <Image source={require('../images/all.png')} style={styles.carsImage} />
                                    </View>
                                    <Text numberOfLines={1} style={styles.lightTextBlack}>{strings('lang.All')}</Text>
                                </Pressable>
                            </View>
                            {cars.map((item, index) => {
                                return (
                                    <View style={item.id == carID ? styles.carsContainer : styles.carsContainer1}>
                                        <Pressable onPress={() => { setCarID(item.id); }} style={{ alignItems: 'center', justifyContent: 'center' }} >
                                            <View style={styles.carsImageContainer}>
                                                <Image source={{ uri: item.image }} style={styles.carsImage} />
                                            </View>
                                            <Text numberOfLines={1} style={styles.lightTextBlack}>{item.name}</Text>
                                        </Pressable>
                                    </View>
                                )
                            })}
                        </View>
                        <View style={{ flexDirection: 'row', width: '96%', alignSelf: 'center' }}>

                            <View style={{ width: '100%', alignSelf: 'flex-end', height: '100%', }}>
                                <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 7.5, justifyContent: 'space-between' }}>
                                    <View style={[styles.iconConatiner2, { paddingVertical: '5%' }]}>
                                        <Image source={require('../images/bluelocation.png')} style={styles.locationImageSmall} />

                                        <View style={{ height: '60%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, zIndex: 0, }}></View>

                                        <Image source={require('../images/yallowlocation.png')} style={styles.locationImageSmall} />
                                    </View>
                                    <View style={{ width: '90%' }}>
                                        <Pressable
                                            onPress={() => { props.navigation.navigate('Search', { screen: 'MapAddress', source: true }) }}
                                            style={styles.contentContainer2}>

                                            <View style={styles.textInput}>
                                                <Text numberOfLines={1} style={{
                                                    color: source ? Black : MediumGrey, textAlignVertical: 'center',
                                                    fontFamily: appFont,
                                                    fontSize: screenWidth / 32,
                                                    textAlign: I18nManager.isRTL ? 'left' : 'left', width: '80%'
                                                }}>
                                                    {
                                                        sourceLabel
                                                            ?
                                                            sourceLabel
                                                            :
                                                            source
                                                                ?
                                                                source
                                                                :
                                                                strings('lang.Yourlocationis')
                                                    }
                                                </Text>
                                                <Pressable onPress={() => { props.navigation.navigate('Destenation', { screen: 'MapAddress', source: true }) }}>
                                                    <Text style={{
                                                        color: DarkBlue, textAlignVertical: 'center',
                                                        fontFamily: appFont,
                                                        fontSize: screenWidth / 36,
                                                        alignSelf: 'flex-start',
                                                        marginStart: '2%', textDecorationStyle: 'solid', textDecorationLine: 'underline'
                                                    }}>
                                                        {strings('lang.Locate')}
                                                    </Text>
                                                </Pressable>
                                            </View>
                                        </Pressable>

                                        <Pressable
                                            onPress={() => { props.navigation.navigate('Search', { screen: 'MapAddress', source: false }) }}
                                            style={styles.contentContainer2}>

                                            <View style={styles.textInput}>
                                                <Text numberOfLines={1} style={{
                                                    color: destination ? Black : MediumGrey, textAlignVertical: 'center',
                                                    fontFamily: appFont,
                                                    fontSize: screenWidth / 32,
                                                    textAlign: I18nManager.isRTL ? 'left' : 'left',
                                                    marginStart: '2%', width: '80%'
                                                }}>
                                                    {
                                                        destinationLabel ?
                                                            destinationLabel :
                                                            destination
                                                                ?
                                                                destination
                                                                :
                                                                strings('lang.destination')
                                                    }
                                                </Text>
                                                <Pressable onPress={() => { props.navigation.navigate('Destenation', { screen: 'MapAddress', source: false }) }}>
                                                    <Text style={{
                                                        color: DarkBlue, textAlignVertical: 'center',
                                                        fontFamily: appFont,
                                                        fontSize: screenWidth / 36,
                                                        textAlign: I18nManager.isRTL ? 'right' : 'left',
                                                        marginStart: '2%', textDecorationStyle: 'solid', textDecorationLine: 'underline'
                                                    }}>
                                                        {strings('lang.Locate')}
                                                    </Text>
                                                </Pressable>
                                            </View>
                                        </Pressable>
                                    </View>
                                </View>

                                {fromLat && fromLng && toLat && toLng
                                    ?
                                    <View style={styles.contentContainer}>
                                        <View style={styles.iconConatiner2}>
                                        </View>
                                        <View style={[styles.textInput, { backgroundColor: WhiteGery, borderWidth: 0, borderRadius: 0 }]}>
                                            <Text style={{ color: DarkGrey, textAlignVertical: 'center', fontFamily: appFont, fontSize: screenWidth / 32, }}>
                                                {strings('lang.Recommendedprice')} {originalPrice} {strings('lang.SR')}
                                            </Text>
                                            <View style={{ height: '70%', width: 1, marginHorizontal: 5, backgroundColor: MediumGrey }}></View>
                                            <Text style={{ color: DarkGrey, textAlignVertical: 'center', fontFamily: appFont, fontSize: screenWidth / 32, }}>
                                                {strings('lang.destination')} {duration} {strings('lang.minute')}
                                            </Text>
                                        </View>
                                    </View>
                                    :
                                    <></>}


                                <View
                                    style={styles.contentContainer}>
                                    <View style={styles.iconConatiner2}>
                                        <Image source={require('../images/Group9877.png')} style={styles.locationImage} />
                                    </View>
                                    <View style={[styles.textInput, { borderColor: priceLimit ? Red : MediumGrey, flexDirection: 'row', justifyContent: 'space-between', paddingHorizontal: 0, paddingEnd: 10 }]}>
                                        <TextInput
                                            onChangeText={text => {
                                                if (text) {
                                                    if (minPrice != 0) {
                                                        // if (JSON.parse(text) > maxPrice) {
                                                        //     console.log('Max');
                                                        //     setPrice(text);
                                                        //     setNewPrice(text)
                                                        //     setPriceLimit(true)
                                                        // }
                                                        if (Number(text) < minPrice) {
                                                            setPrice(text);
                                                            setNewPrice(text)
                                                            setPriceLimit(true)
                                                            console.log('Min');
                                                        }
                                                        else {
                                                            setPrice(text);
                                                            setNewPrice(text)
                                                            setPriceLimit(false)
                                                            console.log('Moderate');
                                                        }
                                                    }
                                                    else {
                                                        setPrice(text);
                                                        setNewPrice(text)
                                                        setPriceLimit(false)
                                                    }
                                                }
                                                else {
                                                    setPrice(text);
                                                    setNewPrice(text)
                                                    setPriceLimit(false)
                                                }

                                            }}
                                            value={price}
                                            keyboardType={'number-pad'}
                                            placeholderTextColor={MediumGrey}
                                            placeholder={strings('lang.Showyourquote')}
                                            style={[styles.textInput, { width: '40%', color: priceLimit ? Red : Black, borderWidth: 0 }]}
                                        />
                                        <Text style={{ color: priceLimit ? Red : DarkGrey, textAlignVertical: 'center', fontFamily: appFont, fontSize: screenWidth / 40, }}>
                                            {priceLimit ? `${strings('lang.message16')} ${minPrice} ${strings('lang.SR')}` : strings('lang.message12')}
                                        </Text>
                                    </View>
                                </View>

                                {gender == 'انثي' || gender == 'female'
                                    ?
                                    <View style={styles.contentContainer}>
                                        <View style={styles.iconConatiner2}>
                                            <Image source={require('../images/Group10631.png')} style={styles.locationImage} />
                                        </View>
                                        <View style={[styles.textInput, { backgroundColor: WhiteGery }]}>
                                            <Pressable onPress={() => { setGenderId('all') }} style={styles.genderContainer}>
                                                <View style={styles.gendercircleConatiner}>
                                                    <View style={[styles.gendercircle, { backgroundColor: genderId == 'all' ? DarkBlue : White }]}>
                                                    </View>
                                                </View>
                                                <Text style={styles.genderText}>{strings('lang.All')}</Text>
                                            </Pressable>
                                            <Pressable onPress={() => { setGenderId('male') }} style={styles.genderContainer}>
                                                <View style={styles.gendercircleConatiner}>
                                                    <View style={[styles.gendercircle, { backgroundColor: genderId == 'male' ? DarkBlue : White }]}>
                                                    </View>
                                                </View>
                                                <Text style={styles.genderText}>{strings('lang.male')}</Text>
                                            </Pressable>
                                            <Pressable onPress={() => { setGenderId('female') }} style={styles.genderContainer}>
                                                <View style={styles.gendercircleConatiner}>
                                                    <View style={[styles.gendercircle, { backgroundColor: genderId == 'female' ? DarkBlue : White }]}>
                                                    </View>
                                                </View>
                                                <Text style={styles.genderText}>{strings('lang.female')}</Text>
                                            </Pressable>
                                        </View>
                                    </View>
                                    :
                                    <></>}


                                <View style={styles.contentContainer}>
                                    <View style={styles.iconConatiner2}>
                                        <Image source={require('../images/Group9872.png')} style={styles.locationImage} />
                                    </View>
                                    <TextInput
                                        onChangeText={text => setComment(text)}
                                        value={comment}
                                        placeholderTextColor={MediumGrey}
                                        placeholder={strings('lang.comments')}
                                        style={styles.textInput}
                                        // multiline={true}
                                        onSubmitEditing={Keyboard.dismiss}
                                    />
                                </View>

                            </View>
                        </View>

                        <View
                            style={{
                                width: '100%',
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignSelf: 'flex-end',
                                marginTop: screenHeight / 80,
                            }}
                        >
                            <Button
                                disabled={!isPointInPolygon}
                                onPress={() => {
                                    if (user.temp_stop == true) {
                                        Toaster(
                                            'top',
                                            'danger',
                                            Red,
                                            strings('lang.Youraccountistemporarilysuspended'),
                                            White,
                                            1500,
                                            screenHeight / 15,
                                        );
                                    } else {
                                        createNewTrip()
                                    }
                                }}
                                // onPress={() => { modalizeRef1.current.open(); modalizeRef.current.close(); }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    alignSelf: 'center',
                                    width: '95%',
                                    height: screenHeight / 12,
                                    backgroundColor: isPointInPolygon ? DarkBlue : Red,
                                    borderRadius: 50,

                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: isPointInPolygon ? screenWidth / 20 : screenWidth / 28,
                                        color: White,
                                    }}
                                >
                                    {isPointInPolygon ? strings('lang.Sendorder') : strings('lang.message23')}
                                </Text>
                            </Button>
                        </View>


                    </KeyboardAwareScrollView>
                </View>
            }
            <>
                {/* <Modalize
                ref={modalizeRef}
                contentRef={contentRef}
                disableScrollIfPossible={true}
                modalHeight={screenHeight / 1.6}
                alwaysOpen={screenHeight / 1.6}
                handlePosition={'inside'}
                useNativeDriver={false}
                panGestureEnabled={false}
                keyboardAvoidingBehavior={'padding'}
                dragToss={0.05}
                threshold={150}
                velocity={2800}
                withReactModal={false}
                withOverlay={false}
                withHandle={true}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
            >
                <ScrollView
                    style={{ width: '100%', alignSelf: 'center', paddingTop: '3%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}
                        contentContainerStyle={{ flexGrow: 1 }}
                        style={{ height: screenHeight / 12, width: screenWidth, paddingHorizontal: '1%', alignSelf: 'center', flexDirection: 'row', }} >
                        {cars.map((item, index) => {
                            return (
                                <View style={item.id == carID ? styles.carsContainer : styles.carsContainer1}>
                                    <Pressable onPress={() => { setCarID(item.id) }} style={{ alignItems: 'center', justifyContent: 'center' }} >
                                        <View style={styles.carsImageContainer}>
                                            <Image source={{ uri: item.image }} style={styles.carsImage} />
                                        </View>
                                        <Text numberOfLines={1} style={styles.lightTextBlack}>{item.name}</Text>
                                    </Pressable>
                                </View>
                            )
                        })}
                    </ScrollView>
                    {
                        carID == 0
                            ?
                           
                            <></>
                            :
                            <>
                                <View style={{ flexDirection: 'row', width: '96%', alignSelf: 'center' }}>

                                    <View style={{ width: '100%', alignSelf: 'flex-end', height: '100%', }}>
                                        <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 7.5, justifyContent: 'space-between' }}>
                                            <View style={[styles.iconConatiner2, { paddingVertical: '5%' }]}>
                                                <Image source={require('../images/bluelocation.png')} style={styles.locationImageSmall} />

                                                <View style={{ height: '60%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, zIndex: 0, }}></View>

                                                <Image source={require('../images/yallowlocation.png')} style={styles.locationImageSmall} />
                                            </View>
                                            <View style={{ width: '90%' }}>
                                                <Pressable
                                                    onPress={() => { props.navigation.navigate('Search', { screen: 'MapAddress', source: true }) }}
                                                    style={styles.contentContainer2}>

                                                    <View style={styles.textInput}>
                                                        <Text numberOfLines={1} style={{
                                                            color: source ? Black : MediumGrey, textAlignVertical: 'center',
                                                            fontFamily: appFont,
                                                            fontSize: screenWidth / 32,
                                                            textAlign: I18nManager.isRTL ? 'left' : 'left', width: '80%'
                                                        }}>
                                                            {
                                                                sourceLabel
                                                                    ? sourceLabel
                                                                    :
                                                                    source
                                                                        ?
                                                                        source
                                                                        :
                                                                        strings('lang.Yourlocationis')
                                                            }
                                                        </Text>
                                                        <Pressable onPress={() => { props.navigation.navigate('Destenation', { screen: 'MapAddress', source: true }) }}>
                                                            <Text style={{
                                                                color: DarkBlue, textAlignVertical: 'center',
                                                                fontFamily: appFont,
                                                                fontSize: screenWidth / 36,
                                                                alignSelf: 'flex-start',
                                                                marginStart: '2%', textDecorationStyle: 'solid', textDecorationLine: 'underline'
                                                            }}>
                                                                {strings('lang.Locate')}
                                                            </Text>
                                                        </Pressable>
                                                    </View>
                                                </Pressable>

                                                <Pressable
                                                    onPress={() => { props.navigation.navigate('Search', { screen: 'MapAddress', source: false }) }}
                                                    style={styles.contentContainer2}>

                                                    <View style={styles.textInput}>
                                                        <Text numberOfLines={1} style={{
                                                            color: destination ? Black : MediumGrey, textAlignVertical: 'center',
                                                            fontFamily: appFont,
                                                            fontSize: screenWidth / 32,
                                                            textAlign: I18nManager.isRTL ? 'left' : 'left',
                                                            marginStart: '2%', width: '80%'
                                                        }}>
                                                            {
                                                                destinationLabel ?
                                                                    destinationLabel :
                                                                    destination
                                                                        ?
                                                                        destination
                                                                        :
                                                                        strings('lang.destination')
                                                            }
                                                        </Text>
                                                        <Pressable onPress={() => { props.navigation.navigate('Destenation', { screen: 'MapAddress', source: false }) }}>
                                                            <Text style={{
                                                                color: DarkBlue, textAlignVertical: 'center',
                                                                fontFamily: appFont,
                                                                fontSize: screenWidth / 36,
                                                                textAlign: I18nManager.isRTL ? 'right' : 'left',
                                                                marginStart: '2%', textDecorationStyle: 'solid', textDecorationLine: 'underline'
                                                            }}>
                                                                {strings('lang.Locate')}
                                                            </Text>
                                                        </Pressable>
                                                    </View>
                                                </Pressable>
                                            </View>
                                        </View>

                                        {fromLat && fromLng && toLat && toLng
                                            ?
                                            <View style={styles.contentContainer}>
                                                <View style={styles.iconConatiner2}>
                                                </View>
                                                <View style={[styles.textInput, { backgroundColor: WhiteGery, borderWidth: 0, borderRadius: 0 }]}>
                                                    <Text style={{ color: DarkGrey, textAlignVertical: 'center', fontFamily: appFont, fontSize: screenWidth / 32, }}>
                                                        {strings('lang.Recommendedprice')} {price} {strings('lang.SR')}
                                                    </Text>
                                                    <View style={{ height: '70%', width: 1, marginHorizontal: 5, backgroundColor: MediumGrey }}></View>
                                                    <Text style={{ color: DarkGrey, textAlignVertical: 'center', fontFamily: appFont, fontSize: screenWidth / 32, }}>
                                                        {strings('lang.destination')} {duration} {strings('lang.minute')}
                                                    </Text>
                                                </View>
                                            </View>
                                            :
                                            <></>}


                                        <View
                                            style={styles.contentContainer}>
                                            <View style={styles.iconConatiner2}>
                                                <Image source={require('../images/Group9877.png')} style={styles.locationImage} />
                                            </View>
                                            <View style={[styles.textInput, { flexDirection: 'row', justifyContent: 'space-between', paddingHorizontal: 0, paddingEnd: 10 }]}>
                                                <TextInput
                                                    onChangeText={text => { setPrice(text); setNewPrice(text) }}
                                                    value={price}
                                                    keyboardType={'number-pad'}
                                                    placeholderTextColor={MediumGrey}
                                                    placeholder={strings('lang.Showyourquote')}
                                                    style={[styles.textInput, { width: '40%', borderWidth: 0 }]}
                                                />
                                                <Text style={{ color: DarkGrey, textAlignVertical: 'center', fontFamily: appFont, fontSize: screenWidth / 40, }}>
                                                    {strings('lang.message12')}
                                                </Text>
                                            </View>
                                        </View>

                                        {gender == 'انثي' || gender == 'female'
                                            ?
                                            <View style={styles.contentContainer}>
                                                <View style={styles.iconConatiner2}>
                                                    <Image source={require('../images/Group10631.png')} style={styles.locationImage} />
                                                </View>
                                                <View style={[styles.textInput, { backgroundColor: WhiteGery }]}>
                                                    <Pressable onPress={() => { setGenderId('all') }} style={styles.genderContainer}>
                                                        <View style={styles.gendercircleConatiner}>
                                                            <View style={[styles.gendercircle, { backgroundColor: genderId == 'all' ? DarkBlue : White }]}>
                                                            </View>
                                                        </View>
                                                        <Text style={styles.genderText}>{strings('lang.All')}</Text>
                                                    </Pressable>
                                                    <Pressable onPress={() => { setGenderId('male') }} style={styles.genderContainer}>
                                                        <View style={styles.gendercircleConatiner}>
                                                            <View style={[styles.gendercircle, { backgroundColor: genderId == 'male' ? DarkBlue : White }]}>
                                                            </View>
                                                        </View>
                                                        <Text style={styles.genderText}>{strings('lang.male')}</Text>
                                                    </Pressable>
                                                    <Pressable onPress={() => { setGenderId('female') }} style={styles.genderContainer}>
                                                        <View style={styles.gendercircleConatiner}>
                                                            <View style={[styles.gendercircle, { backgroundColor: genderId == 'female' ? DarkBlue : White }]}>
                                                            </View>
                                                        </View>
                                                        <Text style={styles.genderText}>{strings('lang.female')}</Text>
                                                    </Pressable>
                                                </View>
                                            </View>
                                            :
                                            <></>}


                                        <View style={styles.contentContainer}>
                                            <View style={styles.iconConatiner2}>
                                                <Image source={require('../images/Group9872.png')} style={styles.locationImage} />
                                            </View>
                                            <TextInput
                                                onChangeText={text => setComment(text)}
                                                value={comment}
                                                placeholderTextColor={MediumGrey}
                                                placeholder={strings('lang.comments')}
                                                style={styles.textInput}
                                                // multiline={true}
                                                onSubmitEditing={Keyboard.dismiss}
                                            />
                                        </View>

                                    </View>
                                </View>

                                <View
                                    style={{
                                        width: '100%',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                        alignSelf: 'flex-end',
                                        marginTop: screenHeight / 80,
                                    }}
                                >
                                    <Button
                                        onPress={() => { modalizeRef1.current.open(); modalizeRef.current.close(); }}
                                        style={{
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                            width: '95%',
                                            height: screenHeight / 20,
                                            backgroundColor: DarkBlue,
                                            borderRadius: 100,

                                        }}
                                    >
                                        <Text
                                            style={{
                                                fontFamily: appFontBold,
                                                fontSize: screenWidth / 28,
                                                color: White,
                                            }}
                                        >
                                            {strings('lang.Sendorder')}
                                        </Text>
                                    </Button>
                                </View>
                            </>
                    }

                </ScrollView>
            </Modalize> */}
            </>

            <>
                {/* Price Modalize */}
                {/* <Modalize
                ref={modalizeRef1}
                contentRef={contentRef1}
                disableScrollIfPossible={true}
                modalHeight={screenHeight / 1.8}
                // modalTopOffset={screenHeight / 1}
                // alwaysOpen={screenHeight / 2}
                // snapPoint={100}
                handlePosition={'inside'}
                // adjustToContentHeight={true}
                // useNativeDriver={false}
                panGestureEnabled={false}
                keyboardAvoidingBehavior={'padding'}
                // dragToss={0.05}
                // threshold={150}
                // velocity={2800}
                // withReactModal={false}
                withOverlay={false}
                withHandle={true}
                scrollViewProps={screenHeight}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
            // closeOnOverlayTap={true}
            // tapGestureEnabled={false}
            // panGestureComponentEnabled={true}
            // closeSnapPointStraightEnabled={false}
            >
                <ScrollView
                    style={{ width: '100%', alignSelf: 'center', paddingVertical: '3%' }}
                    showsVerticalScrollIndicator={false}
                >


                    <View style={{ width: '95%', alignSelf: 'center', height: screenHeight / 2, marginTop: screenHeight / 60 }}>

                        <View style={styles.contentContainer}>

                            <View style={[styles.textInput, { height: screenHeight / 14 }]}>
                                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', width: '40%' }}>
                                    <TextInput
                                        autoFocus={true}
                                        onChangeText={text => { setPrice(text); setNewPrice(text) }}
                                        value={price}
                                        keyboardType={'number-pad'}
                                        placeholderTextColor={Black}
                                        placeholder={price}
                                        style={{
                                            alignItems: 'center', justifyContent: 'center', textAlignVertical: 'center',
                                            fontFamily: appFont,
                                            fontSize: screenWidth / 20,
                                            textAlign: I18nManager.isRTL ? 'right' : 'left', color: Black, width: '60%',
                                        }}
                                    />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 20, color: Black }} > {'SR'} </Text>
                                </View>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: DarkGrey, textAlign: 'right' }} > {strings('lang.message12')} </Text>
                            </View>
                         
                        </View>

                        <View style={{ height: 1, width: '100%', backgroundColor: MediumGrey, marginVertical: screenHeight / 50 }}></View>
                        <View
                            style={{
                                width: '100%',
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignSelf: 'center',
                                flexDirection: 'row'
                            }}
                        >
                            <Button
                                onPress={() => { createNewTrip() }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '60%',
                                    height: screenHeight / 18,
                                    backgroundColor: DarkBlue,
                                    borderRadius: 100,
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 28,
                                        color: White,
                                    }}
                                >
                                    {strings('lang.Confirm')}
                                </Text>
                            </Button>
                            <Button
                                onPress={() => { modalizeRef.current.open(); modalizeRef1.current.close() }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '30%',
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 100,
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 28,
                                        color: Black,
                                    }}
                                >
                                    {strings('lang.Close')}
                                </Text>
                            </Button>
                        </View>
                    </View>

                </ScrollView>

            </Modalize> */}
            </>

            {/* Waiting For Offers Modalize */}
            {viewId == 2
                &&
                <View
                    style={[
                        styles.bottomView,
                        {
                            height: screenHeight / 2.4,
                            backgroundColor: WhiteGery,
                            width: screenWidth,
                            zIndex: 10000000
                        },
                    ]}
                >
                    <ScrollView
                        style={{ width: '100%', alignSelf: 'center', paddingTop: '3%' }}
                        showsVerticalScrollIndicator={false}
                    >



                        <View style={{ flexDirection: 'row', width: '100%', height: screenHeight / 5, }}>

                            <View style={{ width: '100%', alignSelf: 'center', height: '85%', marginBottom: '2%', alignItems: 'flex-start', justifyContent: 'space-between', }}>
                                <View
                                    style={[styles.contentContainer, { justifyContent: 'flex-start', height: screenHeight / 15, marginVertical: 0 }]}>
                                    <View style={{ height: '100%', width: screenHeight / 40, alignItems: 'center', justifyContent: 'center' }}>
                                        <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                                    </View>
                                    <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginHorizontal: '1%', alignSelf: 'center', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.from_address_id ? trip.address_from.address : trip.from_address}</Text>
                                </View>
                                <View
                                    style={[styles.contentContainer, { justifyContent: 'flex-start', height: screenHeight / 15, marginVertical: 0 }]}>
                                    <View style={{ height: '100%', width: screenHeight / 40, alignItems: 'center', justifyContent: 'center' }}>
                                        <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                                    </View>
                                    <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginHorizontal: '1%', alignSelf: 'center', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.to_address_id ? trip.address_to.address : trip.to_address}</Text>
                                </View>
                                <View
                                    style={[styles.contentContainer, { justifyContent: 'flex-start', height: screenHeight / 30, marginVertical: 0 }]}>
                                    <View style={{ height: '100%', width: screenHeight / 40, alignItems: 'center', justifyContent: 'center' }}>
                                        <Image source={require('../images/Group9877.png')} style={styles.locationImage} />
                                    </View>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', alignSelf: 'center' }}>{trip.client_price} {strings('lang.SR')}</Text>
                                </View>

                            </View>

                        </View>

                        <View transparent style={{ flexDirection: 'row', width: '90%', height: screenHeight / 18, alignSelf: 'center', justifyContent: 'space-between', paddingHorizontal: '2%', alignItems: 'center', borderRadius: 25, }}>
                            <Button
                                onPress={() => {
                                    if (price != 1) {
                                        if (Number(price) > trip.min_price) {
                                            setPrice(Number(price) - 1)
                                        }
                                        else {
                                            Toaster(
                                                'top',
                                                'danger',
                                                Red,
                                                strings('lang.message18'),
                                                White,
                                                1500,
                                                screenHeight / 50,
                                            );
                                        }
                                    }
                                }}
                                transparent
                                style={{ width: screenHeight / 10, height: screenHeight / 18, alignSelf: 'center', justifyContent: 'center', alignItems: 'center', backgroundColor: WhiteGery, borderRadius: 100, flexDirection: 'row' }}
                            >
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black }}>-1</Text>
                            </Button>
                            <View style={{}}>
                                <Text numberOfLines={1} style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 16, }}>{Number(price)} {' '} {strings('lang.SR')}</Text>
                            </View>
                            <Button
                                onPress={() => {
                                    // if (Number(price) < trip.max_price) {
                                    setPrice(Number(price) + 1)
                                    // }
                                    // else {
                                    //     Toaster(
                                    //         'top',
                                    //         'danger',
                                    //         Red,
                                    //         strings('lang.message19'),
                                    //         White,
                                    //         1500,
                                    //         screenHeight / 50,
                                    //     );
                                    // }
                                }}
                                transparent
                                style={{ width: screenHeight / 10, height: screenHeight / 18, alignSelf: 'center', justifyContent: 'center', alignItems: 'center', backgroundColor: DarkYellow, borderRadius: 100, }}
                            >
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, }}>+1</Text>
                            </Button>
                        </View>

                        <View
                            style={{
                                width: '100%',
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignSelf: 'center',
                                marginTop: screenHeight / 60,
                            }}
                        >
                            <Button
                                disabled={price && trip.client_price ? Number(price) == trip.client_price : false}
                                onPress={() => {
                                    updatePrice()
                                    // setModalVisible(true); 
                                }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '95%',
                                    height: screenHeight / 12,
                                    backgroundColor: price && trip.client_price ? Number(price) == trip.client_price ? MediumGrey : DarkBlue : DarkBlue,
                                    opacity: null,
                                    borderRadius: 100,
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 20,
                                        color: White,
                                    }}
                                >
                                    {strings('lang.Raisetheprice')}
                                </Text>
                            </Button>
                        </View>

                    </ScrollView>
                </View>
            }
            <>
                {/* <Modalize
                ref={modalizeRef2}
                contentRef={contentRef2}
                disableScrollIfPossible={true}
                modalHeight={screenHeight / 2.6}
                // modalTopOffset={screenHeight / 1}
                // alwaysOpen={screenHeight / 2}
                // snapPoint={100}
                handlePosition={'inside'}
                // adjustToContentHeight={true}
                // useNativeDriver={false}
                panGestureEnabled={false}
                keyboardAvoidingBehavior={'padding'}
                // dragToss={0.05}
                // threshold={150}
                // velocity={2800}
                // withReactModal={false}
                withOverlay={false}
                withHandle={true}
                scrollViewProps={screenHeight}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
            // closeOnOverlayTap={true}
            // tapGestureEnabled={false}
            // panGestureComponentEnabled={true}
            // closeSnapPointStraightEnabled={false}
            >
                <ScrollView
                    style={{ width: '100%', alignSelf: 'center', paddingTop: '3%' }}
                    showsVerticalScrollIndicator={false}
                >

                

                    <View style={{ flexDirection: 'row', width: '100%', height: screenHeight / 5, }}>
                        
                        <View style={{ width: '100%', alignSelf: 'center', height: '85%', marginBottom: '2%', alignItems: 'flex-start', justifyContent: 'space-between', }}>
                            <View
                                style={[styles.contentContainer, { justifyContent: 'flex-start', height: screenHeight / 15, marginVertical: 0 }]}>
                                <View style={{ height: '100%', width: screenHeight / 40, alignItems: 'center', justifyContent: 'center' }}>
                                    <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                                </View>
                                <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginHorizontal: '1%', alignSelf: 'center', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.from_address_id ? trip.address_from.address : trip.from_address}</Text>
                            </View>
                            <View
                                style={[styles.contentContainer, { justifyContent: 'flex-start', height: screenHeight / 15, marginVertical: 0 }]}>
                                <View style={{ height: '100%', width: screenHeight / 40, alignItems: 'center', justifyContent: 'center' }}>
                                    <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                                </View>
                                <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginHorizontal: '1%', alignSelf: 'center', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.to_address_id ? trip.address_to.address : trip.to_address}</Text>
                            </View>
                            <View
                                style={[styles.contentContainer, { justifyContent: 'flex-start', height: screenHeight / 30, marginVertical: 0 }]}>
                                <View style={{ height: '100%', width: screenHeight / 40, alignItems: 'center', justifyContent: 'center' }}>
                                    <Image source={require('../images/Group9877.png')} style={styles.locationImage} />
                                </View>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', alignSelf: 'center' }}>{trip.client_price} {strings('lang.SR')}</Text>
                            </View>

                        </View>

                    </View>

                    <View transparent style={{ flexDirection: 'row', width: '90%', height: screenHeight / 18, backgroundColor: White, alignSelf: 'center', justifyContent: 'space-between', paddingHorizontal: '2%', alignItems: 'center', borderRadius: 25, }}>
                        <Button onPress={() => { if (price != 1) { setPrice(JSON.parse(price) - 1) } }} transparent style={{ width: screenHeight / 10, height: screenHeight / 18, alignSelf: 'center', justifyContent: 'center', alignItems: 'center', backgroundColor: WhiteGery, borderRadius: 100, flexDirection: 'row' }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black }}>-1</Text>
                        </Button>
                        <View style={{}}>
                            <Text numberOfLines={1} style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 20, }}>{price} {' '} {strings('lang.SR')}</Text>
                        </View>
                        <Button onPress={() => { setPrice(JSON.parse(price) + 1) }} transparent style={{ width: screenHeight / 10, height: screenHeight / 18, alignSelf: 'center', justifyContent: 'center', alignItems: 'center', backgroundColor: DarkYellow, borderRadius: 100, }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, }}>+1</Text>
                        </Button>
                    </View>

                    <View
                        style={{
                            width: '100%',
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignSelf: 'center',
                            marginTop: screenHeight / 60,
                        }}
                    >
                        <Button
                            // disabled={!(JSON.parse(newPrice) >= JSON.stringify(price))}
                            onPress={() => {
                                updatePrice()
                                // setModalVisible(true); 
                            }}
                            style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '95%',
                                height: screenHeight / 18,
                                backgroundColor: DarkBlue,
                                opacity: null,
                                borderRadius: 100,
                            }}
                        >
                            <Text
                                style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 28,
                                    color: White,
                                }}
                            >
                                {strings('lang.Raisetheprice')}
                            </Text>
                        </Button>
                    </View>

                </ScrollView>

            </Modalize> */}
            </>

            {/* Trip Modalize */}
            {viewId == 3
                &&
                <View
                    style={[
                        styles.bottomView,
                        {
                            height: screenHeight / 1.8,
                            backgroundColor: WhiteGery,
                            width: screenWidth,
                            zIndex: 10000000
                        },
                    ]}
                >
                    <ScrollView
                        style={{ width: '100%', alignSelf: 'center', paddingVertical: '3%', }}
                        showsVerticalScrollIndicator={false}
                    >
                        {
                            trip.driver_offer && trip.driver_offer.driver
                                ?
                                <View style={{ width: '90%', alignSelf: 'center', flexDirection: 'row', justifyContent: 'center', paddingTop: screenHeight / 100, }}>
                                    <View style={{ width: screenWidth / 8, height: screenWidth / 8, marginEnd: '2%' }}>
                                        <Image source={trip.driver_offer.driver.image ? { uri: trip.driver_offer.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '100%' }} />
                                    </View>
                                    <View style={{ width: '80%', alignItems: 'flex-start', }}>
                                        {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text> */}
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_offer.driver.name}</Text>
                                        {trip.driver_offer.driver.vehicle &&
                                            <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 25, alignItems: 'center' }}>
                                                {trip.driver_offer.driver.vehicle.car_brand ?
                                                    <Image source={{ uri: trip.driver_offer.driver.vehicle.car_brand.image }} style={{ marginEnd: 5, height: screenWidth / 15, resizeMode: 'contain', width: screenWidth / 15 }} />
                                                    :
                                                    <></>
                                                }
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', lineHeight: screenHeight / 35 }}> - {trip.driver_offer.driver.vehicle.car_model && trip.driver_offer.driver.vehicle.car_model.name} - {trip.driver_offer.driver.vehicle.year && trip.driver_offer.driver.vehicle.year}</Text>
                                                <View style={{ width: screenWidth / 22, height: screenWidth / 22, borderRadius: screenWidth / 24, borderWidth: .8, borderColor: MediumGrey, backgroundColor: trip.driver_offer.driver.vehicle.color && trip.driver_offer.driver.vehicle.color, marginStart: 5 }}></View>
                                            </View>}
                                        <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 25, alignItems: 'center' }}>

                                            {trip.driver_offer.driver.vehicle &&
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{trip.driver_offer.driver.vehicle.plate_number + ' ' + trip.driver_offer.driver.vehicle.plate_letter_right + ' ' + trip.driver_offer.driver.vehicle.plate_letter_middle + ' ' + trip.driver_offer.driver.vehicle.plate_letter_left}</Text>
                                            }

                                            {trip.driver_offer.driver.avg_rating &&
                                                <>
                                                    <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '5%', marginEnd: 5, alignSelf: 'center' }} />
                                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {trip.driver_offer.driver.avg_rating}</Text>
                                                </>
                                            }

                                        </View>
                                        <View style={{ width: '100%', height: screenHeight / 20, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-around', }}>
                                            <Button onPress={() => {
                                                Linking.openURL(`tel:${'+966'}${trip.driver_offer.driver.mobile_number.startsWith("0") ?
                                                    trip.driver_offer.driver.mobile_number.slice("0".length)
                                                    :
                                                    trip.driver_offer.driver.mobile_number
                                                    }`)
                                            }} transparent style={{ backgroundColor: LightGreen, width: '40%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                                <Image source={require('../images/call.png')} style={{ resizeMode: 'contain', width: '10%', marginHorizontal: '2%' }} />
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, }}>{strings('lang.Call')}</Text>
                                            </Button>
                                            <Button onPress={() => {
                                                // Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${trip.driver_offer.driver.mobile_number.startsWith("0") ?
                                                //     trip.driver_offer.driver.mobile_number.slice("0".length)
                                                //     :
                                                //     trip.driver_offer.driver.mobile_number
                                                //     }`)
                                                setNewMessgae(false)
                                                props.navigation.navigate('Chat', { receiver_type: 'driver', receiver_id: trip.driver_offer.driver.id, subject_id: trip.id, subject_type: 'DailyTrip', screen: 'MapAddress', chat: true, trip: trip })
                                            }} transparent style={{ backgroundColor: White, borderWidth: 1, borderColor: MediumGrey, width: '40%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                                {newMessgae
                                                    ?
                                                    <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: Red, position: 'absolute', top: -2, end: 0 }}></View>
                                                    :
                                                    <></>
                                                }
                                                <Image source={require('../images/Group98722.png')} style={{ resizeMode: 'contain', width: '12%', marginHorizontal: '4%' }} />
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, }}>{strings('lang.Chatt')}</Text>
                                            </Button>
                                        </View>
                                    </View>
                                </View>
                                :

                                <></>
                        }
                        <View style={{ width: '90%', height: .8, backgroundColor: MediumGrey, alignSelf: 'center', marginVertical: 5 }}></View>

                        <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'space-between', alignSelf: 'center' }}>
                            <View
                                style={[styles.contentContainer, { width: '60%', justifyContent: 'flex-start', height: screenHeight / 24, marginVertical: 0 }]}>
                                <View style={{ height: '100%', width: '15%', alignItems: 'center', justifyContent: 'center' }}>
                                    <Image source={require('../images/Group9877.png')} style={styles.locationImage} />
                                </View>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 20, color: Black, marginStart: '1%', alignSelf: 'center' }}>{trip.final_price} {strings('lang.SR')}</Text>
                            </View>
                            {trip.is_paid == false
                                ?
                                <View
                                    style={{
                                        width: '40%',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                        alignSelf: 'center',
                                        alignItems: 'center'
                                    }}
                                >
                                    <Button
                                        onPress={() => { props.navigation.navigate('PaymentMethods', { subscription: false, type: 'dailytTrip', item: trip }) }}
                                        style={{
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            width: '95%',
                                            height: screenHeight / 17,
                                            backgroundColor: DarkBlue,
                                            // opacity: .5,
                                            borderRadius: 100,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                fontFamily: appFontBold,
                                                fontSize: screenWidth / 30,
                                                color: White,
                                            }}
                                        >
                                            {strings('lang.ConfirmAPay')}
                                        </Text>
                                    </Button>
                                </View>
                                :
                                <View
                                    style={{
                                        width: '30%',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                        alignSelf: 'center',
                                        alignItems: 'center'
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontFamily: appFontBold,
                                            fontSize: screenWidth / 35,
                                            color: DarkBlue,
                                        }}
                                    >
                                        {strings('lang.paid')}
                                        {/* {strings('lang.paid')} {trip.payment_method && trip.payment_method.name} */}
                                    </Text>
                                </View>
                            }
                        </View>

                        <View style={{ width: '90%', height: .8, backgroundColor: MediumGrey, alignSelf: 'center', marginVertical: 5 }}></View>


                        <View
                            style={[styles.contentContainer, { width: '95%', justifyContent: 'flex-start', alignItems: 'center', marginVertical: 0 }]}>
                            <View style={{ height: screenHeight / 25, width: '8%', alignItems: 'center', justifyContent: 'flex-start' }}>
                                <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                            </View>
                            <View style={{ width: '92%' }}>
                                <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginHorizontal: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.from_address_id ? trip.address_from.address : trip.from_address}</Text>
                            </View>
                        </View>
                        <View
                            style={[styles.contentContainer, { width: '95%', justifyContent: 'flex-start', alignItems: 'center', marginTop: screenHeight / 100 }]}>
                            <View style={{ height: screenHeight / 25, width: '8%', alignItems: 'center', justifyContent: 'flex-start' }}>
                                <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                            </View>
                            <View style={{ width: '92%' }}>
                                <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginHorizontal: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.to_address_id ? trip.address_to.address : trip.to_address}</Text>
                            </View>
                        </View>

                        <Text style={{
                            fontFamily: appFontBold,
                            fontSize: screenWidth / 33,
                            color: Red,
                            alignSelf: 'center',
                        }}>{strings('lang.Pleasecheckyourpersonalaccessories')}</Text>
                        {five_offer
                            ?
                            <Text style={{
                                fontFamily: appFontBold,
                                fontSize: screenWidth / 33,
                                color: DarkBlue,
                                alignSelf: 'center',
                            }}>{strings('lang.message24')}</Text>
                            :
                            <></>
                        }

                        <View
                            style={{
                                width: '100%',
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignSelf: 'center',
                                marginTop: screenHeight / 50,
                                alignItems: 'center'
                            }}
                        >
                            <Button
                                onPress={() => { refRBSheetReasons.current.open(); }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '95%',
                                    height: screenHeight / 18,
                                    backgroundColor: appColor1,
                                    // opacity: .5,
                                    borderRadius: 100,

                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 25,
                                        color: White,
                                    }}
                                >
                                    {strings('lang.Tripcancellation')}
                                </Text>
                            </Button>
                        </View>
                        <View style={{ height: screenHeight / 20 }}></View>
                    </ScrollView>
                </View>
            }
            <>
                {/* <Modalize
                ref={modalizeRef3}
                contentRef={contentRef3}
                disableScrollIfPossible={true}
                modalHeight={screenHeight / 1.8}
                // modalTopOffset={screenHeight / 1}
                // alwaysOpen={screenHeight / 2}
                // snapPoint={100}
                handlePosition={'inside'}
                // adjustToContentHeight={true}
                // useNativeDriver={false}
                panGestureEnabled={false}
                keyboardAvoidingBehavior={'padding'}
                // dragToss={0.05}
                // threshold={150}
                // velocity={2800}
                // withReactModal={false}
                withOverlay={false}
                withHandle={true}
                // scrollViewProps={screenHeight}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
                closeOnOverlayTap={true}
                tapGestureEnabled={false}
                panGestureComponentEnabled={true}
                closeSnapPointStraightEnabled={false}
            >
                <ScrollView
                    style={{ width: '100%', alignSelf: 'center', paddingVertical: '3%', }}
                    showsVerticalScrollIndicator={false}
                >
                    {
                        trip.driver_offer && trip.driver_offer.driver
                            ?
                            <View style={{ width: '90%', alignSelf: 'center', flexDirection: 'row', justifyContent: 'center', paddingTop: screenHeight / 100, }}>
                                <View style={{ width: '20%', height: screenHeight / 15, }}>
                                    <Image source={trip.driver_offer.driver.image ? { uri: trip.driver_offer.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '90%' }} />
                                </View>
                                <View style={{ width: '80%', alignItems: 'flex-start', }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_offer.driver.name}</Text>
                                    <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 25, alignItems: 'center' }}>
                                        {trip.driver_offer.driver.vehicle &&
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{trip.driver_offer.driver.vehicle.plate_number + ' ' + trip.driver_offer.driver.vehicle.plate_letter_right + ' ' + trip.driver_offer.driver.vehicle.plate_letter_middle + ' ' + trip.driver_offer.driver.vehicle.plate_letter_left}</Text>
                                        }
                                        {trip.driver_offer.driver.avg_rating &&
                                            <>
                                                <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '10%', alignSelf: 'center' }} />
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {trip.driver_offer.driver.avg_rating}</Text>
                                            </>
                                        }
                                    </View>
                                    <View style={{ width: '100%', height: screenHeight / 20, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-around', }}>
                                        <Button onPress={() => { Linking.openURL(`tel:${'+966'}${trip.driver_offer.driver.mobile_number}`) }} transparent style={{ backgroundColor: LightGreen, width: '40%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                            <Image source={require('../images/call.png')} style={{ resizeMode: 'contain', width: '10%', marginHorizontal: '2%' }} />
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, }}>{strings('lang.Call')}</Text>
                                        </Button>
                                        <Button onPress={() => { Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${trip.driver_offer.driver.formatted_phone}`) }} transparent style={{ backgroundColor: White, borderWidth: 1, borderColor: MediumGrey, width: '40%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                            <Image source={require('../images/whatssapp.png')} style={{ resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, }}>{strings('lang.Chatt')}</Text>
                                        </Button>
                                    </View>
                                </View>
                            </View>
                            :

                            <></>
                    }
                    <View style={{ width: '90%', height: .8, backgroundColor: MediumGrey, alignSelf: 'center', marginVertical: 5 }}></View>

                    <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'space-between', alignSelf: 'center' }}>
                        <View
                            style={[styles.contentContainer, { width: '70%', justifyContent: 'flex-start', height: screenHeight / 24, marginVertical: 0 }]}>
                            <View style={{ height: '100%', width: '8%', alignItems: 'center', justifyContent: 'center' }}>
                                <Image source={require('../images/Group9877.png')} style={styles.locationImage} />
                            </View>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', alignSelf: 'center' }}>{trip.price} {strings('lang.SR')}</Text>
                        </View>
                        {trip.is_paid == false
                            ?
                            <View
                                style={{
                                    width: '30%',
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    alignSelf: 'center',
                                    alignItems: 'center'
                                }}
                            >
                                <Button
                                    onPress={() => { props.navigation.navigate('PaymentMethods', { subscription: false, type: 'dailytTrip', item: trip }) }}
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: '95%',
                                        height: screenHeight / 26,
                                        backgroundColor: DarkBlue,
                                        // opacity: .5,
                                        borderRadius: 100,
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontFamily: appFontBold,
                                            fontSize: screenWidth / 35,
                                            color: White,
                                        }}
                                    >
                                        {strings('lang.ConfirmAPay')}
                                    </Text>
                                </Button>
                            </View>
                            :
                            <View
                                style={{
                                    width: '30%',
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    alignSelf: 'center',
                                    alignItems: 'center'
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 35,
                                        color: DarkBlue,
                                    }}
                                >
                                    {strings('lang.paid')}
                                </Text>
                            </View>
                        }
                    </View>

                    <View style={{ width: '90%', height: .8, backgroundColor: MediumGrey, alignSelf: 'center', marginVertical: 5 }}></View>


                    <View
                        style={[styles.contentContainer, { width: '95%', justifyContent: 'flex-start', alignItems: 'center', marginVertical: 0 }]}>
                        <View style={{ height: screenHeight / 25, width: '8%', alignItems: 'center', justifyContent: 'flex-start' }}>
                            <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                        </View>
                        <View style={{ width: '92%' }}>
                            <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginHorizontal: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.from_address_id ? trip.address_from.address : trip.from_address}</Text>
                        </View>
                    </View>
                    <View
                        style={[styles.contentContainer, { width: '95%', justifyContent: 'flex-start', alignItems: 'center', marginTop: screenHeight / 100 }]}>
                        <View style={{ height: screenHeight / 25, width: '8%', alignItems: 'center', justifyContent: 'flex-start' }}>
                            <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                        </View>
                        <View style={{ width: '92%' }}>
                            <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginHorizontal: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.to_address_id ? trip.address_to.address : trip.to_address}</Text>
                        </View>
                    </View>

                    <Text style={{
                        fontFamily: appFontBold,
                        fontSize: screenWidth / 33,
                        color: Red,
                        alignSelf: 'center',
                    }}>{strings('lang.Pleasecheckyourpersonalaccessories')}</Text>


                    <View
                        style={{
                            width: '100%',
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignSelf: 'center',
                            marginTop: screenHeight / 50,
                            alignItems: 'center'
                        }}
                    >
                        <Button
                            onPress={() => { refRBSheetReasons.current.open(); }}
                            style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '95%',
                                height: screenHeight / 18,
                                backgroundColor: appColor1,
                                // opacity: .5,
                                borderRadius: 100,

                            }}
                        >
                            <Text
                                style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 25,
                                    color: White,
                                }}
                            >
                                {strings('lang.Tripcancellation')}
                            </Text>
                        </Button>
                    </View>
                    <View style={{ height: screenHeight / 20 }}></View>
                </ScrollView>
            </Modalize> */}
            </>

            {/* rating  */}
            <Modalize
                ref={modalizeRefRat}
                contentRef={contentRefRat}
                disableScrollIfPossible={true}
                // modalHeight={screenHeight / 1.2}
                // modalTopOffset={screenHeight / 1}
                // alwaysOpen={screenHeight / 2.2}
                // snapPoint={100}
                handlePosition={'inside'}
                adjustToContentHeight={true}
                // useNativeDriver={false}
                panGestureEnabled={false}
                keyboardAvoidingBehavior={'padding'}
                // dragToss={0.05}
                // threshold={150}
                // velocity={2800}
                // withReactModal={false}
                withOverlay={false}
                withHandle={true}
                // scrollViewProps={screenHeight}
                modalStyle={styles.modalize__content}
                handleStyle={styles.handle__shape}
                closeOnOverlayTap={true}
                tapGestureEnabled={false}
                panGestureComponentEnabled={true}
                closeSnapPointStraightEnabled={false}
            >
                <ScrollView
                    style={{ width: '100%', alignSelf: 'center', paddingBottom: '3%' }}
                    showsVerticalScrollIndicator={false}
                >



                    <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 16, marginTop: '5%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', }}>

                        <Pressable
                            onPress={() => {
                                modalizeRefRat.current.close();
                                setSource('')
                                setSourceId(0)
                                setSourceLabel('')
                                setDuration('')
                                setDistance('')
                                setPrice('')
                                setDestination('')
                                setDestinationId(0)
                                setDestinationLabel('')
                                setComment('')
                                setFromLat('')
                                setFromLng('')
                                setToLat('')
                                setTolng('')
                                setTrip({})
                                setTripBegin(false)
                                setDriveArrived(false)
                                setDetecting(false)
                                setDriverOffers([])
                                setModalVisible(false)
                                setViewId(1);
                            }}>
                            <Image source={require('../images/xx.png')} style={{ width: 10, height: 10, resizeMode: 'contain' }} />
                        </Pressable>
                    </View>

                    {
                        trip.driver_offer && trip.driver_offer.driver
                            ?
                            <View style={{ width: '90%', alignSelf: 'center', flexDirection: 'row', justifyContent: 'center', paddingTop: screenHeight / 100, borderBottomWidth: .8, borderColor: MediumGrey }}>
                                <View style={{ width: '20%', height: screenHeight / 15, }}>
                                    <Image source={trip.driver_offer.driver.image ? { uri: trip.driver_offer.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '90%' }} />
                                </View>
                                <View style={{ width: '80%', alignItems: 'flex-start', }}>
                                    {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text> */}
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_offer.driver.name}</Text>
                                    <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 25, alignItems: 'center' }}>
                                        {trip.driver_offer.driver.vehicle &&
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{trip.driver_offer.driver.vehicle.plate_number + ' ' + trip.driver_offer.driver.vehicle.plate_letter_right + ' ' + trip.driver_offer.driver.vehicle.plate_letter_middle + ' ' + trip.driver_offer.driver.vehicle.plate_letter_left}</Text>
                                        }
                                        {trip.driver_offer.driver.avg_rating &&
                                            <>
                                                <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '10%', alignSelf: 'center' }} />
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {trip.driver_offer.driver.avg_rating}</Text>
                                            </>
                                        }
                                    </View>
                                    {/* <View style={{ width: '100%', height: screenHeight / 20, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-around', }}>
                                        <Button onPress={() => { Linking.openURL(`tel:${'+966'}${trip.driver_offer.driver.formatted_phone}`) }} transparent style={{ backgroundColor: LightGreen, width: '40%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                            <Image source={require('../images/call.png')} style={{ resizeMode: 'contain', width: '10%', marginHorizontal: '2%' }} />
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, }}>{strings('lang.Call')}</Text>
                                        </Button>
                                        <Button onPress={() => { Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${trip.driver_offer.driver.formatted_phone}`) }} transparent style={{ backgroundColor: White, borderWidth: 1, borderColor: MediumGrey, width: '40%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                            <Image source={require('../images/whatssapp.png')} style={{ resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, }}>{strings('lang.Chatt')}</Text>
                                        </Button>
                                    </View> */}
                                </View>
                            </View>
                            :

                            <></>
                    }
                    <Text style={{ fontFamily: appFont, fontSize: screenWidth / 25, color: Black, alignSelf: 'center', marginTop: '5%' }}>{strings('lang.Giveyourratingtothedriver')}</Text>
                    <View style={{ width: '95%', alignItems: 'center', justifyContent: 'center' }}>
                        <StarRating
                            disabled={false}
                            maxStars={5}
                            starSize={screenHeight / 25}
                            starStyle={{ marginEnd: 2, }}
                            rating={starCount}
                            containerStyle={{ alignContent: 'flex-start', }}
                            buttonStyle={{ flexDirection: 'row', left: 'auto' }}
                            // emptyStar={'ios-star-outline'}
                            // fullStar={'ios-star'}
                            // iconSet={'Ionicons'}
                            emptyStarColor={DarkYellow}
                            fullStarColor={DarkYellow}
                            selectedStar={(rating) => setStarCount(rating)}
                        />
                    </View>

                    <View style={styles.input1}>
                        <Textarea
                            onChangeText={text => setReview(text)}
                            value={review}
                            placeholder={strings('lang.Enternotesaboutthedriver')}
                            placeholderTextColor={MediumGrey}
                            style={{
                                width: '100%',
                                alignSelf: 'center',
                                // borderRadius: 15,
                                paddingHorizontal: '5%',
                                borderColor: MediumGrey,
                                color: review ? Black : DarkGrey,
                                // borderWidth: 1,
                                fontFamily: appFontBold,
                                height: '100%',
                                textAlignVertical: 'top',
                                fontSize: screenWidth / 30,
                                textAlign: I18nManager.isRTL ? 'right' : 'left',
                            }}
                        />
                    </View>
                    <View
                        style={{
                            width: '95%',
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignSelf: 'center',
                            marginVertical: screenHeight / 40,
                            flexDirection: 'row'
                        }}
                    >
                        <Button
                            transparent
                            disabled={starCount == 0 ? true : false}
                            onPress={() => {
                                rateDriver()
                            }}
                            style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '100%',
                                height: screenHeight / 22,
                                backgroundColor: DarkBlue,
                                borderRadius: 20,
                                opacity: starCount == 0 ? .5 : 1
                            }}
                        >
                            <Text
                                style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 28,
                                    color: White,
                                }}
                            >
                                {strings('lang.send')}
                            </Text>
                        </Button>

                    </View>
                </ScrollView>
            </Modalize>

            {/* Offers Modal */}
            <Modal
                transparent={true}
                animationType="fade"
                visible={modalVisible}
                propagateSwipe={true}
            >
                {/* <View style={{ zIndex: 1000000, width: screenWidth, height: screenHeight, }}> */}

                <View style={{ height: screenHeight / 1, width: screenWidth, zIndex: 1000000, backgroundColor: 'rgba(119, 119, 119,0.5)' }}>

                    {/* <View style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight / 14 + 2 * screenHeight / 5.5, minHeight: screenHeight }} > */}


                    <FlatList
                        data={DriverOffers}
                        showsVerticalScrollIndicator={false}
                        renderItem={({ item, index }) =>
                            // <View style={[styles.modal, { zIndex: 10000000 }]}>
                            //     <View style={{ width: '100%', height: '40%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', }}>
                            //         <View style={{ width: '20%', height: '100%' }}>
                            //             <Image source={item.driver.image ? { uri: item.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '80%' }} />
                            //         </View>


                            //         <View style={{ width: '60%', height: '90%', alignItems: 'flex-start', }}>
                            //             {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', }}>{'Chevorlet Corvette'}</Text> */}
                            //             <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', }}>{item.driver.name}</Text>
                            //             {item.driver.vehicle
                            //                 &&
                            //                 <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', lineHeight: screenHeight / 35 }}>{item.driver.vehicle.plate_number + ' ' + item.driver.vehicle.plate_letter_right + ' ' + item.driver.vehicle.plate_letter_middle + ' ' + item.driver.vehicle.plate_letter_left}</Text>
                            //             }

                            //         </View>
                            //         <View style={{ width: '20%', height: '90%', alignItems: 'flex-end', justifyContent: 'center', }}>
                            //             <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 26, color: MediumGreen, marginStart: '1%' }}>{item.price} {strings('lang.SR')}</Text>
                            //         </View>
                            //     </View>

                            //     <View style={{ width: '85%', height: '20%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', alignSelf: 'flex-end', }}>
                            //         <View style={{ width: '25%', height: '90%', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                            //             <Image source={require('../images/star.png')} style={{ height: '60%', resizeMode: 'contain', width: '25%' }} />
                            //             <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', }}> {item.driver.avg_rating}</Text>
                            //         </View>
                            //         {/* {
                            //             item.duration != null
                            //             &&
                            //             <> */}
                            //         <View style={{ width: 1, height: '50%', backgroundColor: MediumGrey, }}></View>

                            //         <View style={{ height: '90%', width: '25%', alignItems: 'center', justifyContent: 'center' }}>
                            //             <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', }}>{calculateOfferData(item.driver.current_lat, item.driver.current_lng)[1]} {strings('lang.minute')}</Text>
                            //         </View>
                            //         {/* </>
                            //         }
                            //         {
                            //             item.distance != null
                            //             &&
                            //             <> */}
                            //         <View style={{ width: 1, height: '50%', backgroundColor: MediumGrey, }}></View>

                            //         <View style={{ height: '90%', width: '25%', alignItems: 'flex-start', justifyContent: 'center' }}>
                            //             <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}>{calculateOfferData(item.driver.current_lat, item.driver.current_lng)[0]} {strings('lang.km')}</Text>
                            //         </View>
                            //         {/* </>
                            //         } */}

                            //     </View>

                            //     <View style={{ height: screenHeight / 20, flexDirection: 'row-reverse', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '100%', }}>
                            //         <Button transparent style={styles.modalbuttonContainer2} onPress={() => { changeOfferStatus('rejected', item.id) }}>
                            //             <Text style={styles.modalbuttonText}>{strings('lang.Refusal')}</Text>
                            //         </Button>
                            //         <Button transparent style={styles.modalbuttonContainer} onPress={() => { changeOfferStatus('accepted', item.id) }}>
                            //             <Text style={styles.modalbuttonText}>{strings('lang.Acceptance')}</Text>
                            //         </Button>
                            //     </View>
                            // </View>
                            <UserOffer item={item} trip={trip} onReject={() => { changeOfferStatus('rejected', item.id) }} onAccept={() => { changeOfferStatus('accepted', item.id) }} />
                        }
                        keyExtractor={item => item.id}
                        style={{ alignSelf: "center", overflow: "hidden", width: screenWidth, marginTop: screenHeight / 18, zIndex: 10000000, }}
                    />

                    <View
                        style={{
                            width: '100%',
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignSelf: 'center',
                            paddingBottom: screenHeight / 60,
                            paddingTop: 10,
                            alignItems: 'center',
                            // backgroundColor: 'rgba(119, 119, 119,0.5)',
                            // position: 'absolute',
                            bottom: 0,
                            marginBottom: screenHeight / 50,
                            zIndex: 100000000
                            // backgroundColor: '#000', opacity: 0.5,
                        }}
                    >
                        <Button
                            onPress={() => {
                                setModalVisible(false);
                                if (viewId == 2) {
                                    cancelTrip()
                                }
                                else {
                                    refRBSheetReasons.current.open();
                                }
                            }}
                            style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '95%',
                                height: screenHeight / 18,
                                backgroundColor: appColor1,
                                borderRadius: 100,
                            }}
                        >
                            <Text
                                style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 25,
                                    color: White,
                                }}
                            >
                                {strings('lang.Tripcancellation')}
                            </Text>
                        </Button>
                    </View>

                    {/* <View style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight }} >
                    </View> */}
                    {/* 
                        {DriverOffers.map((item, index) => {
                            return <View style={[styles.modal, { top: screenHeight / 14 + index * screenHeight / 5.5, }]}>
                                <View style={{ width: '100%', height: '40%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', }}>
                                    <View style={{ width: '20%', height: '100%' }}>
                                        <Image source={{ uri: item.driver.image }} style={{ height: '100%', resizeMode: 'contain', width: '80%' }} />
                                    </View>

                                    <View style={{ width: '60%', height: '90%', alignItems: 'flex-start', }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', }}>{'Chevorlet Corvette'}</Text>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: MediumGrey, marginStart: '1%', lineHeight: screenHeight / 35 }}>{item.driver.name}</Text>
                                    </View>

                                    <View style={{ width: '20%', height: '90%', alignItems: 'flex-end', justifyContent: 'center', }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 26, color: MediumGreen, marginStart: '1%' }}>{item.price} {strings('lang.SR')}</Text>
                                    </View>
                                </View>

                                <View style={{ width: '85%', height: '20%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', alignSelf: 'flex-end', }}>
                                    <View style={{ width: '25%', height: '90%', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                                        <Image source={require('../images/star.png')} style={{ height: '60%', resizeMode: 'contain', width: '25%' }} />
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', }}> {'4.5'}</Text>
                                    </View>

                                    <View style={{ width: 1, height: '50%', backgroundColor: MediumGrey, }}></View>

                                    <View style={{ height: '90%', width: '25%', alignItems: 'center', justifyContent: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', }}>{'6'} {strings('lang.minute')}</Text>
                                    </View>

                                    <View style={{ width: 1, height: '50%', backgroundColor: MediumGrey, }}></View>

                                    <View style={{ height: '90%', width: '25%', alignItems: 'flex-start', justifyContent: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}>{'12'} {strings('lang.km')}</Text>
                                    </View>
                                </View>

                                <View style={{ height: screenHeight / 20, flexDirection: 'row-reverse', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '100%', }}>
                                    <Button transparent style={styles.modalbuttonContainer2} onPress={() => { changeOfferStatus('rejected', item.id) }}>
                                        <Text style={styles.modalbuttonText}>{strings('lang.Refusal')}</Text>
                                    </Button>
                                    <Button transparent style={styles.modalbuttonContainer} onPress={() => { changeOfferStatus('accepted', item.id) }}>
                                        <Text style={styles.modalbuttonText}>{strings('lang.Acceptance')}</Text>
                                    </Button>
                                </View>
                            </View>
                        })} */}






                </View>



                {/* </View> */}


            </Modal>

            <RBSheet
                ref={refRBSheetReasons}
                height={screenHeight / 1.3}
                openDuration={280}
                customStyles={{
                    container: {
                        // justifyContent: "center",
                        alignItems: "center",
                        width: '100%',

                        alignSelf: 'center',
                        borderTopStartRadius: 15,
                        borderTopEndRadius: 15
                    },
                    wrapper: {

                    },
                    draggableIcon: {

                    }
                }}
            // onClose={() => { props.navigation.navigate('Home') }}
            // closeOnDragDown={true}
            >
                <ScrollView
                    style={{ alignSelf: 'center', width: '100%', paddingVertical: screenHeight / 50 }}
                    showsVerticalScrollIndicator={false}
                >
                    {/* <View style={{ alignItems: 'center', alignSelf: 'center', justifyContent: 'flex-start', width: '100%', paddingVertical: '3%', }}> */}
                    {/* <Image source={require('../images/modrek/animal.png')} style={{ resizeMode: 'contain', width: '60%', height: '30%', alignItems: 'center', tintColor: Red, marginTop: '20%', marginBottom: '10%' }} /> */}
                    <View style={{ height: screenHeight / 25, width: '60%', alignItems: 'center', alignSelf: 'center', justifyContent: 'center', }}>
                        <Text style={{ fontSize: screenWidth / 25, fontFamily: appFontBold, color: DarkGrey, }}>{strings('lang.Whatisthereasonforcancelingthetrip')}</Text>
                    </View>
                    {reasons.map((item, index) => {
                        return <Pressable
                            onPress={() => { setReasonId(item.id) }}
                            style={[styles.reasonContainer, {
                                backgroundColor: item.id == reasonsId ? DarkBlue : WhiteGery,
                                borderColor: item.id == reasonsId ? DarkBlue : MediumGrey,
                            }]}>
                            <Text style={{
                                fontSize: screenWidth / 30,
                                fontFamily: appFontBold,
                                color: item.id == reasonsId ? White : Black,
                            }}>{item.reason}</Text>
                        </Pressable>
                    })}


                    <View style={{ width: '95%', height: screenHeight / 7, alignSelf: 'center', marginVertical: '2.5%' }}>
                        {/* <Text style={{ fontSize: screenWidth / 26, fontFamily: appFont, color: Black, alignSelf: 'flex-start' }}>{strings('lang.Pleasewritethereason')}</Text> */}
                        <Textarea
                            placeholder={strings('lang.Pleasewritethereason')}
                            onChangeText={text => setReasonText(text)}
                            value={reasonText}
                            style={{
                                width: '100%',
                                alignSelf: 'center',
                                borderRadius: 10,
                                paddingHorizontal: '5%',
                                borderColor: MediumGrey,
                                color: DarkGrey,
                                borderWidth: 1,
                                fontFamily: appFontBold,
                                height: '100%',
                                textAlignVertical: 'top',
                                fontSize: screenWidth / 30,
                                textAlign: I18nManager.isRTL ? 'right' : 'left',
                            }}
                        />
                    </View>
                    <View style={{ height: screenHeight / 20 }}></View>
                    {/* </View> */}
                </ScrollView>
                <View style={{ alignItems: "center", justifyContent: 'space-between', width: '95%', alignSelf: "center", height: screenHeight / 20, flexDirection: 'row', }}>
                    <Button onPress={() => {
                        cancelTrip()
                    }} style={{ width: '45%', alignSelf: "center", height: '100%', backgroundColor: DarkBlue, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                        <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.Confirm')}</Text>
                    </Button>
                    <Button onPress={() => { refRBSheetReasons.current.close(); }}
                        style={{ width: '45%', alignSelf: "center", height: '100%', backgroundColor: WhiteGery, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                        <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Black, }}>{strings('lang.back')}</Text>
                    </Button>
                </View>
                <View style={{ height: screenHeight / 20 }}></View>

            </RBSheet>


        </View>
    )
}


const styles = StyleSheet.create({
    handle__shape: {
        alignSelf: 'center',
        width: screenWidth / 6,
        height: 6,
        borderRadius: 10,
        // backgroundColor: Black,
    },
    input1: {
        height: screenHeight / 10,
        borderRadius: 20,
        width: '95%',
        backgroundColor: White,
        borderWidth: .8,
        flexDirection: 'row',
        paddingStart: '2%',
        borderColor: MediumGrey,
        marginVertical: '5%',
        alignSelf: 'center'
    },
    modalize__content: {
        marginTop: 'auto',
        borderTopLeftRadius: 30,
        borderTopRightRadius: 30,
        zIndex: 10000000000000, shadowColor: '#000000', shadowOffset: { width: 5, height: 5 }, shadowRadius: 8, shadowOpacity: 0.3,
        elevation: 5, paddingBottom: screenHeight / 10,
    },
    modal: {
        alignItems: 'center',
        width: screenWidth / 1.1,
        height: screenHeight / 6,
        // position: 'absolute',
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 20,
        paddingVertical: '2%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%',
        marginBottom: 30
    },
    modalbuttonContainer: {
        height: screenHeight / 25,
        width: '60%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Green2,
        alignSelf: 'center',
        borderRadius: 100
    },
    modalbuttonContainer2: {
        height: screenHeight / 25,
        width: '35%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: appColor1,
        alignSelf: 'center',
        borderRadius: 100
    },
    modalbuttonText: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 36, color: White
    },

    contentContainer2: {
        width: '100%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2%',
        height: screenHeight / 20,
        borderRadius: 5,
        paddingHorizontal: 0,
        flexDirection: 'row',
    },
    contentContainer: {
        width: '90%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2%',
        height: screenHeight / 17,
        borderRadius: 5,
        paddingHorizontal: 0,
        flexDirection: 'row',
    },
    textInput: {
        width: '100%',
        flexDirection: 'row',
        alignSelf: 'center',
        borderRadius: 100,
        borderColor: MediumGrey,
        borderWidth: 1,
        height: '100%',
        color: Black,
        alignItems: 'center',
        justifyContent: 'space-between',
        textAlignVertical: 'center',
        fontFamily: appFont,
        fontSize: screenWidth / 32,
        textAlign: I18nManager.isRTL ? 'right' : 'left',
        paddingHorizontal: 10, overflow: 'hidden'
    },
    locationContainer: {
        width: '100%',
        // flexDirection: 'row',
        alignSelf: 'center',
        height: '100%',
        alignItems: 'flex-start',
        justifyContent: 'center',

    },
    priceContainer: {
        width: '100%',
        flexDirection: 'row',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-around',
        borderRadius: 5,
        backgroundColor: MediumGrey,
        height: '100%',
    },
    carsContainer: {
        width: screenWidth / 5,
        height: '85%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: White,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: Gold,
        margin: 10
    },
    carsContainer1: {
        width: screenWidth / 5,
        height: '85%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: WhiteGery,
        borderRadius: 5,
        margin: 10
    },
    carsImageContainer: {
        width: screenWidth / 6,
        height: screenHeight / 20,
        alignSelf: 'center',
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center'
    },
    carsImage: {
        width: '100%',
        height: '100%',
        resizeMode: 'contain',
    },
    carsImage1: {
        width: '70%',
        height: '70%',
        resizeMode: 'contain'
    },
    lightTextBlack: {
        color: Black,
        fontFamily: appFont,
        fontSize: screenWidth / 40,
    },
    locationImage: {
        resizeMode: 'contain',
        width: '50%',
        height: '50%'
    },
    locationImageSmall: {
        resizeMode: 'contain',
        width: '100%',
        height: '20%'
    },
    profileModal: {
        flexDirection: 'row',
        height: screenHeight / 14,
        width: '95%',
        marginVertical: '5%',
        alignItems: 'center',
        alignSelf: 'center',
        marginBottom: screenHeight / 5,
        position: 'absolute',
        top: '2%', start: '2%'
    },
    iconModal: {
        height: '100%',
        width: '80%',
        resizeMode: 'contain',
    },
    textModal: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 25,
        color: Black, marginHorizontal: '3%'
    },
    optionContainer: {
        alignSelf: 'center',
        width: '95%',
        height: 33,
        marginVertical: '.5%',
    },
    optionSubContainer: {
        alignSelf: 'center',
        width: '95%',
        height: '100%',
        flexDirection: 'row',
        alignItems: 'center',
    },
    iconContainer: {
        width: screenHeight / 30,
        height: screenHeight / 30,
        borderRadius: screenHeight / 60,
        marginHorizontal: '5%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: WhiteGery
    },
    iconModal1: {
        width: '65%',
        height: '65%',
        resizeMode: 'contain',
        tintColor: Black,
    },
    label: { color: DarkGrey, fontSize: screenWidth / 30, fontFamily: appFontBold, alignSelf: 'flex-start' },
    labelContainer: { width: '85%', height: '100%', justifyContent: 'center' },
    iconContainer1: { width: '30%', height: '100%', borderRadius: 10, alignItems: 'center', justifyContent: 'center', backgroundColor: WhiteGery },
    touchableText: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 25, marginStart: 10, alignSelf: 'flex-start' },
    touchableContainer: { borderBottomWidth: 1, borderTopWidth: 1, borderColor: MediumGrey, width: screenWidth / 1.1, height: screenHeight / 15, alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
    icon: { width: '4%', height: '80%', resizeMode: 'contain', tintColor: Black, transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] },
    icon1: { width: '60%', height: '60%', resizeMode: 'contain', tintColor: appColor1, },
    iconConatiner2: { height: '100%', width: '10%', alignItems: 'center', justifyContent: 'center', },
    genderContainer: { width: screenWidth / 4, height: screenHeight / 22, alignItems: 'center', justifyContent: 'center', flexDirection: 'row', paddingHorizontal: '5%' },
    gendercircleConatiner: { width: screenHeight / 50, height: screenHeight / 50, borderRadius: screenHeight / 100, backgroundColor: White, borderWidth: .5, borderColor: MediumGrey, alignItems: 'center', justifyContent: 'center' },
    gendercircle: { width: screenHeight / 75, height: screenHeight / 75, borderRadius: screenHeight / 150, backgroundColor: White, },
    genderText: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 33, marginStart: 5 },
    reasonContainer: {
        height: screenHeight / 18,
        borderRadius: 20,
        width: '95%',
        backgroundColor: WhiteGery,
        borderWidth: .8,
        borderColor: MediumGrey,
        marginVertical: '2%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
    },
    bottomView: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        width: '100%',
        backgroundColor: 'white',
        // paddingVertical: '2%',
        borderTopWidth: 1,
        borderTopColor: MediumGrey,
    },
});

export default MapAddress;
