import React, { Component, useEffect, useRef, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    appColor1,
    MediumGreen,
    DarkYellow,
    DarkBlue,
} from '../components/Styles';
import { Button } from 'native-base';
import Header from '../components/Header';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import RBSheet from 'react-native-raw-bottom-sheet';
import TripsContainer from '../components/TripsContainer';


const TripLog = props => {
    const dispatch = useDispatch();
    const [termsAndConditionData, setTermsAndConditionData] = useState('');
    const [loading, setLoading] = useState(false);
    const [tirpId, setTirpId] = useState(0);
    const [tirps, setTirps] = useState([
        {
            id: 0,
            name: 'موقعك الحالى',
            title: 'مسجد قباء',
            price: '30',
            text: 'ملغى',
            image: '../images/Group191.png'
        },
        {
            id: 1,
            name: 'موقعك الحالى',
            title: 'مسجد قباء',
            price: '30',
            text: 'تمت',
            image: '../images/Group191.png'
        },

    ]);
    const refRBSheet = useRef();
    const [trips, setTrips] = useState([{}, {}]);


    useEffect(() => {
    }, []);

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Triplog')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Triplog')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />
                {/* 
                <ScrollView
                    style={{ width: '95%', height: '100%', alignSelf: 'center', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                > */}

                <FlatList
                    data={trips}
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item, key }) =>
                        <TripsContainer item={item} RBSheetPrees={() => { refRBSheet.current.open() }} />
                    }
                    keyExtractor={item => item.id}
                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth, backgroundColor: White, }}

                />

                {/* {tirps.map((item, index) => {
                        return (
                            <TouchableOpacity onPress={() => { setTirpId(item.id); refRBSheet.current.open() }} style={styles.paymentUnactive}>
                                <View>
                                    <View style={{ flexDirection: 'row', maxWidth: '90%', height: '30%', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                                        <View style={styles.iconContainer}>
                                            <Image style={styles.icon1} source={require('../images/bluelocation.png')} />
                                        </View>
                                        <Text style={styles.text}>{item.name}</Text>
                                    </View>
                                    <View style={{ flexDirection: 'row', maxWidth: '90%', height: '30%', alignItems: 'flex-start' }}>
                                        <View style={styles.iconContainer}>
                                            <Image style={styles.icon1} source={require('../images/yallowlocation.png')} />
                                        </View>
                                        <Text style={styles.text}>{item.title}</Text>
                                    </View>
                                    <View style={{ flexDirection: 'row', width: '100%', height: screenHeight / 20, alignItems: 'center', position: 'absolute', bottom: '0%', backgroundColor: WhiteGery }}>

                                        <Text style={styles.text}>{item.price} {strings('lang.SR')}-{strings('lang.cashMoney')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                        )
                    })} */}


                {/* </ScrollView> */}


                <RBSheet
                    ref={refRBSheet}
                    height={screenHeight / 2.8}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            width: '100%',
                            alignSelf: 'center',
                            borderRadius: 20

                        },
                        wrapper: {

                        },
                        draggableIcon: {
                            // width: '25%',
                            // backgroundColor: DarkGrey
                        }
                    }}
                // onClose={() => { props.navigation.navigate('Home') }}
                // closeOnDragDown={true}
                >
                    <View style={{ alignItems: 'center', alignSelf: 'center', justifyContent: 'center', width: '100%', paddingVertical: '3%', }}>
                        <Button onPress={() => { refRBSheet.current.close(); props.navigation.navigate("DetailsTrip") }} style={{ width: '90%', alignSelf: "center", height: screenHeight / 22, backgroundColor: DarkBlue, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 20, marginVertical: 5 }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.detailsofthetrip')}</Text>
                        </Button>
                        <Button onPress={() => { refRBSheet.current.close(); props.navigation.navigate("") }} style={{ width: '90%', alignSelf: "center", height: screenHeight / 22, backgroundColor: DarkBlue, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 20, marginVertical: 5 }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.repeatrequest')}</Text>
                        </Button>
                        <Button onPress={() => { refRBSheet.current.close(); props.navigation.navigate("") }} style={{ width: '90%', alignSelf: "center", height: screenHeight / 22, backgroundColor: DarkBlue, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 20, marginVertical: 5 }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.returnpath')}</Text>
                        </Button>
                        <Button onPress={() => { refRBSheet.current.close(); props.navigation.navigate("") }} style={{ width: '90%', alignSelf: "center", height: screenHeight / 22, backgroundColor: appColor1, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 20, marginVertical: 5 }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.Cancellationoftheriderequest')}</Text>
                        </Button>
                        <Button onPress={() => { refRBSheet.current.close() }} style={{ width: '90%', alignSelf: "center", height: screenHeight / 22, backgroundColor: WhiteGery, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 20, marginVertical: 5 }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Black, }}>{strings('lang.Close')}</Text>
                        </Button>
                    </View>
                </RBSheet>

                <View style={{ height: 10 }}></View>

            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white',
    },
    section: { width: '90%', alignSelf: 'center' },
    labelContanier: {
        width: '100%',
        marginVertical: '3%',
        alignItems: 'flex-start',
    },
    textContanier: {
        width: '100%',
        marginVertical: '2%',
        alignItems: 'flex-start',
    },
    label: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 20, marginTop: screenHeight / 50, alignSelf: 'flex-start' },
    body: {
        fontFamily: appFontBold,
        color: DarkGrey,
        fontSize: screenWidth / 30,
        alignSelf: I18nManager.isRTL ? 'flex-start' : 'flex-end',
    },
    paymentActive: {
        width: '98%',
        height: 70,
        alignSelf: 'center',
        borderColor: MediumGreen,
        borderWidth: 1,
        borderRadius: 5,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        marginVertical: 8
    },
    paymentUnactive: {
        width: '98%',
        alignSelf: 'center',
        borderColor: MediumGrey,
        borderWidth: 1,
        height: screenHeight / 8,
        borderRadius: 20,
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        marginVertical: 8,
        overflow: 'hidden'
    },
    text: { fontFamily: appFont, color: Black, fontSize: screenWidth / 28, marginStart: 10 },
    textActive: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 28, },
    textunactive: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, },
    icon1: { width: '60%', height: '60%', resizeMode: 'contain', },
    iconContainer: { width: '30%', height: '100%', alignItems: 'center', justifyContent: 'center', },
    checkContainer: { width: 25, height: 25, borderRadius: 15, alignItems: 'center', justifyContent: 'center', borderColor: MediumGrey, borderWidth: 1 },
    check: { width: 15, height: 15, borderRadius: 10, backgroundColor: MediumGreen, },
});

export default TripLog;
