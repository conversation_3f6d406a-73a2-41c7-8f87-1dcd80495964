import React, { Component, useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  StyleSheet,
  I18nManager,
  TouchableOpacity,
  Platform,
  Pressable,
  PermissionsAndroid,
  Linking,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {
  appFont,
  appFontBold,
  Green,
  WhiteGreen,
  screenWidth,
  White,
  DarkGreen,
  Blue,
  MediumGrey,
  DarkGrey,
  WhiteGery,
  Black,
  screenHeight,
  Red,
  appColor1,
  DarkYellow,
  Grey,
  DarkBlue,
} from '../components/Styles';
import { Button } from 'native-base';
import Header from '../components/Header';
import { FlatList, ScrollView, TextInput } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import * as generalAction from '../../Store/Actions/addresses';
import Toaster from '../components/Toaster';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import Carousel, { Pagination, ParallaxImage } from 'react-native-snap-carousel';
import ProductContainer from '../components/ProductContainer';
import Geolocation from 'react-native-geolocation-service';
import MapView, { Marker, Polygon } from 'react-native-maps';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import Geocoder from 'react-native-geocoding';
import * as geolib from 'geolib';
import Loading from '../components/Loading';

const ASPECT_RATIO = screenWidth / screenHeight

Geocoder.init("AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I"); // use a valid API key

const Search = props => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const ref = useRef(null);
  const [isFav, setIsFav] = useState(false);
  const [search, setSearch] = useState('');
  const [region2, SetRegion2] = useState({})
  const [region, SetRegion] = useState({
    latitude: 37.42209348000618,
    longitude: -122.0839218981564,
    longitudeDelta: 0.01 * ASPECT_RATIO,
    latitudeDelta: 0.01
  })
  const [latStore, SetLatStore] = useState(0)
  const [lngStore, SetLngStore] = useState(0)
  const [lat, SetLat] = useState(0)
  const [lng, SetLng] = useState(0)
  const [lat1, SetLat1] = useState(37.42209348000618)
  const [lng1, SetLng1] = useState(-122.0839218981564)
  const [addressDesription, setAddressDesription] = useState('');
  const working_cities = useSelector(state => state.general.working_cities);
  const [isPointInPolygon, setIsPointInPolygon] = useState(false);


  useEffect(() => {

    const onRegionChangeComplete = (region) => {

      var initial_Region = {
        longitudeDelta: region.longitudeDelta,
        latitudeDelta: region.latitudeDelta,
        longitude: region.longitude,
        latitude: region.latitude
      }

      onRegionChangeComplete.bind(initial_Region)

      console.log('region', region);

    }

    const requestLocationPermission = async () => {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            'title': 'Location Permission',
            'message': 'MyMapApp needs access to your location'
          }
        )

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          get_Lat_User()
          // Alert.alert("Please allow location")
        } else {
          console.log("Location permission denied")
          setLoading(false)

        }
      } catch (err) {
        console.warn(err)
        setLoading(false)
      }
    }

    const RequestIosPermissio = () => {
      Geolocation.requestAuthorization('always').then((res) => {
        console.log('res', res);
        if (res == 'denied') {
          //  Alert.alert("Please allow location to continuo")/
          // Linking.openURL('app-settings:');
          showCustomAlert()
        }
      });
      setLoading(false)
    }

    const showCustomAlert = () => {
      Alert.alert(
        'Permission Alert',
        'The app will not work properly without this permission ',
        [
          {
            text: 'Access permission from settings',
            onPress: () => {
              console.log('start permission');
              Linking.openURL('app-settings:');
              // get_Lat_User()
            },
          },
          {
            text: 'Cancel',
            onPress: () => {
              console.log('exit');
              props.navigation.navigate('Home')
            },
          },
        ],
        { cancelable: false }
      );
    };

    const get_Lat_User = async () => {
      // Geolocation.requestAuthorization();
      // Geolocation.setRNConfiguration({
      //   skipPermissionRequests: false,
      //   authorizationLevel: 'whenInUse',
      // });
      if (Platform.OS == 'ios') {
        RequestIosPermissio()
      }

      Geolocation.getCurrentPosition(
        (position) => {
          console.log('position', position);
          var lat = parseFloat(position.coords.latitude)
          var longi = parseFloat(position.coords.longitude)
          var initialRegion = {
            latitude: lat,
            longitude: longi,
            longitudeDelta: 0.01 * ASPECT_RATIO,
            latitudeDelta: 0.01
          }
          SetRegion(initialRegion)
          SetLat(lat)
          SetLng(longi)
          go(lat, longi)
        },
        (error) => {
          console.log('error', error);

        },
        { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 1000 },
      );
      setLoading(false)
    }

    if (Platform.OS == 'ios') {
      get_Lat_User()
    }
    else {
      requestLocationPermission();
    }

  }, []);

  const RequestIosPermissio = () => {
    Geolocation.requestAuthorization('always').then((res) => {
      console.log('res', res);
      if (res == 'denied') {
        //  Alert.alert("Please allow location to continuo")/
        // Linking.openURL('app-settings:');
        props.navigation.navigate('Home')
      }
    });
    setLoading(false)
  }

  const get_Lat_User = async () => {
    SetRegion(region2)

    // Geolocation.requestAuthorization();
    // Geolocation.setRNConfiguration({
    //   skipPermissionRequests: false,
    //   authorizationLevel: 'whenInUse',
    // });
    if (Platform.OS == 'ios') {
      RequestIosPermissio()

    }
    // console.log('SetRegion', region);
    Geolocation.getCurrentPosition(
      async (position) => {
        console.log('position', position);
        var lat = parseFloat(position.coords.latitude)
        var longi = parseFloat(position.coords.longitude)
        var initialRegion = {
          latitude: lat,
          longitude: longi,
          longitudeDelta: 0.01 * ASPECT_RATIO,
          latitudeDelta: 0.01
        }
        SetRegion(initialRegion)
        SetLat(lat)
        SetLng(longi)
        console.log('lat', lat, longi);
        go(lat, longi)
        // Geocoder.init("AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I");
        // await Geocoder.from(lat, longi, 'ar')
        //   .then(json => {
        //     var addressComponent = json.results[0].formatted_address;
        //     console.log(addressComponent);
        //     setAddressDesription(addressComponent)
        //   })
        //   .catch(error => console.log('error', error));

      },
      (error) => {
        console.log('error', error);

      },
      { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 1000 },
    );
    setLoading(false)
  }
  const [isFirstTime, setIsFirstTime] = useState(true)

  const onRegionChangeComplete = (newRegion) => {
    console.log('newRegion', newRegion);

    if (Platform.OS == 'android') {
      if (loadingMore) {
        return;
      }
      if (isFirstTime) {
        setLoadingMore(true)

        var initial_Region = {
          longitudeDelta: newRegion.longitudeDelta,
          latitudeDelta: newRegion.latitudeDelta,
          longitude: newRegion.longitude,
          latitude: newRegion.latitude
        }

        SetRegion(initial_Region)
        SetLat(newRegion.latitude)
        SetLng(newRegion.longitude)
        SetRegion2(newRegion)
        go(newRegion.latitude, newRegion.longitude)
        console.log('region', newRegion);
        setLoadingMore(false)
        setIsFirstTime(false)
      }

    }

    setLoadingMore(true)

    // var initial_Region = {
    //   longitudeDelta: newRegion.longitudeDelta,
    //   latitudeDelta: newRegion.latitudeDelta,
    //   longitude: newRegion.longitude,
    //   latitude: newRegion.latitude
    // }
    SetRegion2(newRegion)

    // SetRegion(newRegion)
    SetLat(newRegion.latitude)
    SetLng(newRegion.longitude)

    go(newRegion.latitude, newRegion.longitude)
    console.log('region', newRegion);
    setLoadingMore(false)
    // // for (let city of working_cities) {
    // //   let isPointInPolygon = geolib.isPointInPolygon(region, city.coordinates)
    // //   setIsPointInPolygon(isPointInPolygon)
    // //   if (isPointInPolygon) {
    // //     return;
    // //   }
    // // }
  }

  const [userInteracted, setUserInteracted] = useState(false);

  const HandleSearch = (data, details) => {

    setLoadingMore(true)

    console.log("data", data)
    console.log("details", details)
    let initial_Region = {
      longitudeDelta: 0.01 * ASPECT_RATIO,
      latitudeDelta: 0.01,
      longitude: details.geometry.location.lng,
      latitude: details.geometry.location.lat,
    }
    ChangeLocation(initial_Region)
  }

  const ChangeLocation = (region) => {
    var initial_Region = {
      longitudeDelta: 0.01 * ASPECT_RATIO,
      latitudeDelta: 0.01,
      longitude: region.longitude,
      latitude: region.latitude
    }
    SetRegion(initial_Region)
    SetLat(region.latitude)
    SetLng(region.longitude)
    go(region.latitude, region.longitude)
  }

  const go = async (latitude, longitude) => {
    if (latitude && longitude) {
      Geocoder.init("AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I");
      await Geocoder.from(latitude, longitude, 'ar')
        .then(json => {
          var addressComponent = json.results[0].formatted_address;
          console.log(addressComponent);
          setAddressDesription(addressComponent)
        })
        .catch(error => console.log('error', error));
    }
    for (let city of working_cities) {
      let isPointInPolygon = geolib.isPointInPolygon({ latitude: latitude, longitude: longitude }, city.coordinates)
      setIsPointInPolygon(isPointInPolygon)
      if (isPointInPolygon) {
        setLoadingMore(false)
        return;
      }
    }
    setLoadingMore(false)
  }


  if (loading) {
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.Search')}
          drawerPress={() => {
            props.navigation.navigate('More');
          }}
          backPress={() => {
            props.navigation.goBack();
          }}
          HideMore
        />

        <ScrollView
          style={{ width: '100%', height: '100%', marginBottom: '2%' }}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.section}>
            <View style={styles.textContanier}>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
            </View>
          </View>
          <View style={{ height: screenHeight / 10 }}></View>
        </ScrollView>
        <Footer current={'Search'} navigation={props.navigation} />
      </View>
    );

  }
  else {
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.Search')}
          // drawerPress={() => {
          //   props.navigation.navigate('More');
          // }}
          backPress={() => {
            props.navigation.goBack();
          }}
        />
        <View style={{
          zIndex: 2, position: 'absolute', top: '45%', padding: '3%',
          width: '50%',
          backgroundColor: DarkBlue,
          borderRadius: 15
        }}>
          {/* {loadingAddress ? <Loading /> : */}
          <Text
            numberOfLines={2}
            style={{
              color: White, textAlign: I18nManager.isRTL ? 'left' : 'right',
              fontFamily: appFontBold, fontSize: screenWidth / 33
            }}>
            {addressDesription}
          </Text>
          {/* } */}
        </View>
        <Image source={require('../images/darklocation.png')} style={{ resizeMode: "contain", width: '7%', height: '7%', zIndex: 1000000, position: 'absolute', start: '46%', top: '52%' }} />


        {loadingMore
          ?
          <View style={{ backgroundColor: MediumGrey, opacity: .8, position: "absolute", zIndex: 10000000000, alignSelf: 'center', alignItems: 'center', width: screenWidth, height: screenHeight, }}>
            <ActivityIndicator size={Platform.OS == 'ios' ? 100 : 50} color={DarkBlue} style={{ marginTop: screenHeight / 5 }} />
          </View>
          :
          <></>}
        {/* <View style={{ height: screenHeight / 20, alignItems: 'center', justifyContent: 'center', position: 'absolute', top: screenHeight / 9, overflow: 'hidden', backgroundColor: WhiteGery, zIndex: 100, width: '90%', marginBottom: '5%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, borderRadius: 25, alignSelf: 'center' }}> */}
        {/* <Image source={require('../images/Group-58.png')} style={{ height: '40%', width: '10%', resizeMode: 'contain', tintColor: DarkYellow }} /> */}
        {/* <TextInput
            placeholderTextColor={'#707070'}
            placeholder={strings('lang.Search')}
            style={styles.inputText}
            value={search}
            onChangeText={text => {
              setSearch(text);
            }}
          /> */}
        <View style={{ overflow: 'hidden', zIndex: 100000000, position: 'absolute', top: screenHeight / 9, height: screenHeight / 4.2, borderRadius: 10 }}>
          <GooglePlacesAutocomplete
            placeholder={strings('lang.Search')}
            minLength={2}
            fetchDetails={true}
            styles={{
              // textInputContainer: { position: 'absolute', top: 20, height: 50, width: '80%', alignSelf: 'center', borderRadius: 10,borderColor: MediumGrey, borderWidth: 1, overflow: 'hidden' },
              textInputContainer: { height: screenHeight / 18, width: screenWidth / 1.1, alignSelf: 'center', borderRadius: 100, borderColor: DarkBlue, borderWidth: 1, overflow: 'hidden', },
              textInput: { height: '100%', color: Black, fontSize: 16, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left' },
              predefinedPlacesDescription: { color: Black, },
              poweredContainer: { width: 0, height: 0 },
              powered: { width: 0, height: 0 },
              description: { color: Black },
              listView: { position: 'absolute', width: '100%', top: screenHeight / 18, alignSelf: 'center', zIndex: 100000000, height: screenHeight / 2, borderRadius: 10 },
            }}
            onPress={(data, details) => { HandleSearch(data, details) }}
            query={{
              key: 'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I',
              language: 'ar',
            }}
            onFail={error => console.error(error)}
            listUnderlayColor={Black}
          // nearbyPlacesAPI='GooglePlacesSearch'
          // debounce={200}
          />
        </View>
        {/* </View> */}

        <MapView
          region={region}
          // initialRegion={region}
          // showsUserLocation={true}
          // showsMyLocationButton={false}
          // followsUserLocation={true}
          // showsCompass={true}
          scrollEnabled={true}
          zoomEnabled={true}
          // minZoomLevel={17}()
          pitchEnabled={true}
          rotateEnabled={true}
          // moveOnMarkerPress={true}
          // onMarkerPress={() => { setMapp(true) }}
          // height: driver || details ? screenHeight / 1.66 : screenHeight / 1.95,
          onRegionChangeComplete={onRegionChangeComplete.bind(this)}
          // onMapReady={async () => setUserInteracted(true)}
          style={{ height: screenHeight / 1, width: screenWidth / 1, zIndex: 1 }}
        >
          {working_cities.map((city) => {
            return (
              <Polygon
                // coordinates={closedAreas}
                fillColor='rgba(119, 119, 119,0.2)'
                strokeWidth={2}
                strokeColor={DarkBlue}
                // style={{ width: 100, height: 100, }}
                coordinates={city.coordinates}
              />
            );
          }
          )}
        </MapView>

        <View style={{ zIndex: 1000000, width: screenWidth / 8, height: screenWidth / 8, position: "absolute", bottom: '15%', left: '7%', borderRadius: screenWidth / 16, elevation: 20, shadowColor: DarkGrey, shadowOpacity: 1, shadowOffset: { width: 1, height: 1 }, backgroundColor: "white", alignItems: 'center', justifyContent: 'center' }} >
          <TouchableOpacity
            onPress={() => {
              get_Lat_User()
            }}
          >
            <Image
              style={{ width: 25, height: 25, resizeMode: 'contain', tintColor: DarkBlue }}
              source={require('../images/mylocation.png')}
            />
          </TouchableOpacity>
        </View>


        <View style={styles.btnContainer}>
          <Button disabled={!isPointInPolygon} style={[styles.buttonContainer, { backgroundColor: isPointInPolygon ? DarkBlue : Red }]} onPress={() => props.navigation.navigate(props.route.params.screen, { source: props.route.params.source, addressDesription: addressDesription, lat: lat, lng: lng, region: region, addressLabel: '', addressId: 0, pending: false, pendingTrip: null })}>
            <Text style={[styles.buttonText, { color: White }]}>{isPointInPolygon ? strings('lang.Locate') : strings('lang.message23')}</Text>
          </Button>
        </View>
        <View style={{ height: screenHeight / 15 }}></View>

        {/* <Footer current=s{'Search'} navigation={props.navigation} /> */}


      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: 'white',
  },

  section: { width: '90%', alignSelf: 'center' },
  textContanier: {
    width: '100%',
    marginVertical: '2%',
    alignItems: 'flex-start',
  },
  textActive: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 28, },
  textblack: { fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, alignSelf: 'flex-start' },
  textblack1: { fontFamily: appFontBold, fontSize: screenWidth / 25, color: Black },
  textgrey: { fontFamily: appFontBold, fontSize: screenWidth / 25, color: MediumGrey },
  text: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 30, alignSelf: 'center' },
  image: {
    resizeMode: 'contain',
    width: '60%',
    height: '50%',
    // tintColor: Green,
    alignSelf: 'flex-end',

  },
  inputText: {
    color: Black,
    width: '85%',
    fontFamily: appFontBold,
    fontSize: screenWidth / 30,
    textAlign: Platform.OS == 'ios' ? I18nManager.isRTL ? 'right' : 'left' : null,
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    paddingHorizontal: 5,
  },
  btnContainer: {
    zIndex: 2, width: "90%", alignSelf: "center", justifyContent: "center", marginBottom: screenHeight / 20, position: 'absolute', bottom: 0
  },
  buttonContainer: {
    marginVertical: '2%',
    width: '100%',
    backgroundColor: DarkBlue,
    alignSelf: 'center',
    marginHorizontal: 5,
    justifyContent: 'center',
    borderRadius: 100,
    alignItems: 'center',
    padding: 12,
    marginBottom: 0,
    zIndex: 3,
    height: screenHeight / 20
  },
  buttonText: {
    color: White,
    fontFamily: appFontBold,
    fontSize: screenWidth / 28
  },
});

export default Search;
