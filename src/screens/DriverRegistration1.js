import React, { Component, useEffect, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    PermissionsAndroid,
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    MediumGreen,
    appColor1,
    DarkBlue,
    DarkYellow,
} from '../components/Styles';
import { Button, Input } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { Slider } from '@miblanchard/react-native-slider';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
// import RNFS from 'react-native-fs';

const DriverRegistration1 = props => {
    const dispatch = useDispatch();
    const [termsAndConditionData, setTermsAndConditionData] = useState('');
    const [loading, setLoading] = useState(false);
    const [low, setLow] = useState(0);
    const [high, setHigh] = useState(0);
    const [minValue, setMinValue] = useState(0);
    const [maxValue, setMaxValue] = useState(0);
    const [error_userName, seterror_userName] = useState('')
    const [userName, setuserName] = useState('')
    const [email, setEmail] = useState('')
    const [error_email, seterror_email] = useState('')
    const [phone, setPhone] = useState('')
    const [error_phone, seterror_phone] = useState('')
    const [nationalAddress, setNationalAddress] = useState('')
    const [error_nationalAddress, seterror_nationalAddress] = useState('')
    const [base64Image, setBase64Image] = useState('');
    const [cover, setCover] = useState();
    const [carFront, setCarFront] = useState();
    const [carBack, setCarBack] = useState();
    const [carRight, setCarRight] = useState();
    const [carLeft, setCarLeft] = useState();
    const [carInside, setCarInside] = useState();


    console.log(I18nManager.isRTL);
    useEffect(() => {

    }, []);

    const requestCameraPermission = async () => {
        try {
            const granted = await PermissionsAndroid.request(
                PermissionsAndroid.PERMISSIONS.CAMERA,
                {
                    title: "App Camera Permission",
                    message: "App needs access to your camera ",
                    buttonNeutral: "Ask Me Later",
                    buttonNegative: "Cancel",
                    buttonPositive: "OK"
                }
            );
            if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                console.log("Camera permission given");
            } else {
                console.log("Camera permission denied");
            }
        } catch (err) {
            console.warn(err);
        }
    };

    const chooseImages = async (type) => {

        let options = {
            quality: 0.5,
            maxWidth: 500,
            maxheight: 400,
            title: 'Select Image',
            customButtons: [
                {
                    name: 'customOptionKey',
                    title: 'Choose Photo from Custom Option',
                },
            ],
            storageOptions: {
                skipBackup: true,
                path: 'images',
            },
        };
        launchImageLibrary(options, response => {
            console.log('Response = ', response);

            if (response.didCancel) {
                console.log('User cancelled image picker');
            } else if (response.error) {
                console.log('ImagePicker Error: ', response.error);
            } else if (response.customButton) {
                console.log('User tapped custom button: ', response.customButton);
                alert(response.customButton);
            } else {
                // let cover = response.assets[0];
                // You can also display the image using data:
                // let source = {
                //   uri: 'data:image/jpeg;base64,' + response.data
                // };
                let cover = {
                    uri: response.assets[0].uri,
                    type: response.assets[0].type,
                    name: response.assets[0].fileName,
                    link: '',
                };
                switch (type) {
                    case 'Front':
                        setCarFront(cover);
                        break;
                    case 'Back':
                        setCarBack(cover);
                        break;
                    case 'Right':
                        setCarRight(cover);
                        break;
                    case 'Left':
                        setCarLeft(cover);
                        break;
                    case 'Inside':
                        setCarInside(cover);
                        break;
                    default:
                        break;
                }
            }
        });
    };

    if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.driverregistration')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    } else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.driverregistration')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={{ width: '95%', alignSelf: 'center', }}>
                        {/* <Slider
                            value={[low, high]}
                            maximumValue={minValue}
                            minimumValue={maxValue}
                            // onValueChange={(value) => setValue(value)}
                            onValueChange={([low, high]) => {
                                setLow(low);
                                setHigh(high);
                            }}
                            thumbTintColor={MediumGreen}
                            maximumTrackTintColor={MediumGrey}
                            minimumTrackTintColor={MediumGreen}
                            disableRange={true}
                            floatingLabel={false}
                            allowLabelOverflow={false}
                        /> */}
                        <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 25, alignItems: 'center', justifyContent: 'center' }}>
                            <View style={{ width: 25, height: 25, borderRadius: 50, backgroundColor: DarkYellow, }} ></View>
                            <View style={{ height: 3, width: '80%', backgroundColor: DarkYellow }}></View>
                            <View style={{ width: 25, height: 25, borderRadius: 50, backgroundColor: White, borderColor: DarkYellow, borderWidth: 1, }} ></View>
                        </View>

                        <View style={{ justifyContent: 'space-between', alignItems: 'center', width: '100%', flexDirection: 'row' }}>
                            <Text style={styles.textImg}>{strings('lang.driverdata')}</Text>
                            <Text style={styles.textImg}>{strings('lang.cardata')}</Text>
                        </View>

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.carType')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={userName} onChangeText={(text) => {
                                    setuserName(text);
                                    seterror_userName('')
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.carType')}
                            />
                        </View>
                        {error_userName ?
                            <Text style={styles.loginError}>{error_userName}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        }

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.Model')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={phone} onChangeText={(text) => {
                                    setPhone(text);
                                    seterror_phone('')
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.Model')}
                            />
                        </View>
                        {error_phone ?
                            <Text style={styles.loginError}>{error_phone}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        }

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.manufacturingyear')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={email} onChangeText={(text) => {
                                    setEmail(text);
                                    seterror_email('')
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.manufacturingyear')}
                            />
                        </View>
                        {error_email ?
                            <Text style={styles.loginError}>{error_email}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        }

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.carcolor')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={nationalAddress} onChangeText={(text) => {
                                    setNationalAddress(text);
                                    seterror_nationalAddress('')
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.carcolor')}
                            />
                        </View>
                        {error_nationalAddress ?
                            <Text style={styles.loginError}>{error_nationalAddress}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        }

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.carplatenumber')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={nationalAddress} onChangeText={(text) => {
                                    setNationalAddress(text);
                                    seterror_nationalAddress('')
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.carplatenumber')}
                            />
                        </View>
                        {error_nationalAddress ?
                            <Text style={styles.loginError}>{error_nationalAddress}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        }

                        <Text style={styles.textImg}>{strings('lang.imageofthecarfromthefront')}</Text>
                        {carFront
                            ?
                            (
                                <View style={styles.imageContainer}>
                                    <Button transparent block
                                        onPress={() => {
                                            setCarFront(null)
                                            setBase64Image('')
                                        }}
                                        style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: 5, backgroundColor: MediumGrey, position: "absolute", zIndex: 100, alignSelf: "flex-start", justifyContent: "center", end: 0, top: 0 }}>
                                        <Image source={require('../images/trash.png')} style={{ width: '130%', height: '130%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />
                                    </Button>
                                    <Image
                                        source={{ uri: carFront.uri }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: '100%',
                                            height: '100%'
                                        }}
                                    />
                                </View>
                            )
                            :
                            (
                                <TouchableOpacity onPress={() => { chooseImages('Front') }}>
                                    <View style={[styles.imageContainer, { borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, }]}>
                                        <Image
                                            source={require('../images/img.png')}
                                            style={{ width: screenWidth / 4, height: screenHeight / 8, resizeMode: 'contain', tintColor: DarkGrey }}
                                        />
                                    </View>
                                </TouchableOpacity>
                            )
                        }

                        <Text style={styles.textImg}>{strings('lang.imageofthecarfromtheback')}</Text>
                        {carBack
                            ?
                            (
                                <View style={styles.imageContainer}>
                                    <Button transparent block
                                        onPress={() => {
                                            setCarBack(null)
                                        }}
                                        style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: 5, backgroundColor: MediumGrey, position: "absolute", zIndex: 100, alignSelf: "flex-start", justifyContent: "center", end: 0, top: 0 }}>
                                        <Image source={require('../images/trash.png')} style={{ width: '130%', height: '130%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />
                                    </Button>
                                    <Image
                                        source={{ uri: carBack.uri }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: '100%',
                                            height: '100%'
                                        }}
                                    />
                                </View>
                            )
                            :
                            (
                                <TouchableOpacity onPress={() => { chooseImages('Back') }}>
                                    <View style={[styles.imageContainer, { borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, }]}>
                                        <Image
                                            source={require('../images/img.png')}
                                            style={{ width: screenWidth / 4, height: screenHeight / 8, resizeMode: 'contain', tintColor: DarkGrey }}
                                        />
                                    </View>
                                </TouchableOpacity>
                            )
                        }

                        <Text style={styles.textImg}>{strings('lang.message2')}</Text>
                        {carRight
                            ?
                            (
                                <View style={styles.imageContainer}>
                                    <Button transparent block
                                        onPress={() => {
                                            setCarRight(null)
                                        }}
                                        style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: 5, backgroundColor: MediumGrey, position: "absolute", zIndex: 100, alignSelf: "flex-start", justifyContent: "center", end: 0, top: 0 }}>
                                        <Image source={require('../images/trash.png')} style={{ width: '130%', height: '130%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />
                                    </Button>
                                    <Image
                                        source={{ uri: carRight.uri }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: '100%',
                                            height: '100%'
                                        }}
                                    />
                                </View>
                            )
                            :
                            (
                                <TouchableOpacity onPress={() => { chooseImages('Right') }}>
                                    <View style={[styles.imageContainer, { borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, }]}>
                                        <Image
                                            source={require('../images/img.png')}
                                            style={{ width: screenWidth / 4, height: screenHeight / 8, resizeMode: 'contain', tintColor: DarkGrey }}
                                        />
                                    </View>
                                </TouchableOpacity>
                            )
                        }

                        <Text style={styles.textImg}>{strings('lang.message3')}</Text>
                        {carLeft
                            ?
                            (
                                <View style={styles.imageContainer}>
                                    <Button transparent block
                                        onPress={() => {
                                            setCarLeft(null)
                                        }}
                                        style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: 5, backgroundColor: MediumGrey, position: "absolute", zIndex: 100, alignSelf: "flex-start", justifyContent: "center", end: 0, top: 0 }}>
                                        <Image source={require('../images/trash.png')} style={{ width: '130%', height: '130%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />
                                    </Button>
                                    <Image
                                        source={{ uri: carLeft.uri }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: '100%',
                                            height: '100%'
                                        }}
                                    />
                                </View>
                            )
                            :
                            (
                                <TouchableOpacity onPress={() => { chooseImages('Left') }}>
                                    <View style={[styles.imageContainer, { borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, }]}>
                                        <Image
                                            source={require('../images/img.png')}
                                            style={{ width: screenWidth / 4, height: screenHeight / 8, resizeMode: 'contain', tintColor: DarkGrey }}
                                        />
                                    </View>
                                </TouchableOpacity>
                            )
                        }

                        <Text style={styles.textImg}>{strings('lang.message4')}</Text>
                        {carInside
                            ?
                            (
                                <View style={styles.imageContainer}>
                                    <Button transparent block
                                        onPress={() => {
                                            setCarInside(null)
                                        }}
                                        style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: 5, backgroundColor: MediumGrey, position: "absolute", zIndex: 100, alignSelf: "flex-start", justifyContent: "center", end: 0, top: 0 }}>
                                        <Image source={require('../images/trash.png')} style={{ width: '130%', height: '130%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />
                                    </Button>
                                    <Image
                                        source={{ uri: carInside.uri }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: '100%',
                                            height: '100%'
                                        }}
                                    />
                                </View>
                            )
                            :
                            (
                                <TouchableOpacity onPress={() => { chooseImages('Inside') }}>
                                    <View style={[styles.imageContainer, { borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, }]}>
                                        <Image
                                            source={require('../images/img.png')}
                                            style={{ width: screenWidth / 4, height: screenHeight / 8, resizeMode: 'contain', tintColor: DarkGrey }}
                                        />
                                    </View>
                                </TouchableOpacity>
                            )
                        }

                        <View style={{ width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', marginTop: 30, flexDirection: 'row' }}>
                            <Button
                                transparent
                                onPress={() => {
                                    props.navigation.navigate('DriverRequests');
                                }}
                                style={{ backgroundColor: DarkBlue, width: '68%', height: screenHeight / 18, alignItems: 'center', justifyContent: 'center', borderRadius: 100 }}
                            >
                                <Text style={{ color: White, fontSize: screenWidth / 25, fontFamily: appFont }}>
                                    {strings('lang.Register')}
                                </Text>
                            </Button>

                            <Button
                                transparent
                                onPress={() => { props.navigation.goBack() }}
                                style={{ backgroundColor: WhiteGery, height: screenHeight / 18, width: '28%', height: 50, alignItems: 'center', justifyContent: 'center', borderRadius: 100 }}
                            >
                                <Text style={{ color: Black, fontSize: screenWidth / 25, fontFamily: appFont }}>
                                    {strings('lang.back')}
                                </Text>
                            </Button>
                        </View>
                    </View>
                    <View style={{ height: screenHeight / 20 }}></View>
                </ScrollView>

            </View>
        );
    }
};

const styles = StyleSheet.create({
    labelContainer: { flexDirection: 'row', alignItems: "center", marginBottom: 0, marginTop: screenHeight / 100 },

    text: {
        fontSize: screenWidth / 30,
        fontFamily: appFont,
        // alignSelf: 'flex-start',
        // marginHorizontal: '2.5%',
        marginBottom: 5,
        color: Black,
    },
    star: {
        fontFamily: appFont,
        fontSize: screenWidth / 20,
        color: Red
    },
    input: {
        height: screenHeight / 20, borderWidth: 1, borderRadius: 100, borderColor: MediumGrey, flexDirection: 'row', justifyContent: 'center', paddingStart: '2.5%',
        width: "100%", fontSize: screenWidth / 33, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left'
    },

    loginError: {
        color: Red, fontFamily: appFont, alignSelf: 'flex-start', fontSize: screenWidth / 33
    },
    textImg: {
        fontSize: screenWidth / 30,
        fontFamily: appFont,
        color: Black,
        marginTop: '2%',
        alignSelf: 'flex-start'
    },
    imageContainer: {
        flexDirection: 'row',
        width: '100%',
        alignSelf: 'center',
        height: screenHeight / 6,
        marginBottom: 10,
        borderWidth: 1,
        borderColor: MediumGrey,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
});

export default DriverRegistration1;
