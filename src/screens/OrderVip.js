import React, { Component, useEffect, useRef, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    TextInput,
    BackHandler,
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    appColor1,
    MediumGreen,
    DarkYellow,
    DarkBlue,
    Gold,
    WhiteBlue,
} from '../components/Styles';
import { Button, Input, Textarea } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import RBSheet from 'react-native-raw-bottom-sheet';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import SelectDropdown from 'react-native-select-dropdown';
import RNDateTimePicker from '@react-native-community/datetimepicker';
import moment from 'moment';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import * as vipTripsActions from '../../Store/Actions/vipTrips';
import Loading from '../components/Loading';
import DateTimePicker from 'react-native-modal-datetime-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';


const OrderVip = props => {
    const dispatch = useDispatch();
    const [termsAndConditionData, setTermsAndConditionData] = useState('');
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);

    const [code, setCode] = useState('');
    const [activeText, setActiveText] = useState(0);

    const [review, setReview] = useState('');

    const [dateDisplay, setDateDisplay] = useState(false);
    const [date, setDate] = useState('');
    const [showDate, setShowDate] = useState('');
    const [timeDisplay, setTimeDisplay] = useState(false);
    const [time, setTime] = useState('');
    const [showTime, setShowTime] = useState('');
    const [numOfPeople, setNumOfPeople] = useState(0);
    const [numOfSeats, setNumOfSeats] = useState(0);

    const [source, setSource] = useState('');
    const [sourceId, setSourceId] = useState(0);
    const [sourceLabel, setSourceLabel] = useState('');
    const [fromLat, setFromLat] = useState('');
    const [fromLng, setFromLng] = useState('');
    const [destination, setDestination] = useState('');
    const [destinationId, setDestinationId] = useState(0);
    const [destinationLabel, setDestinationLabel] = useState('');
    const [toLat, setToLat] = useState('');
    const [toLng, setTolng] = useState('');
    const [token, setToken] = useState('');

    const IsFocused = useIsFocused();


    const loginfirst = async () => {
        Toaster(
            'top',
            'danger',
            Red,
            strings('lang.mustLogin'),
            White,
            1500,
            screenHeight / 15,
        );
    }

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                props.navigation.goBack()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    useEffect(() => {
        setLoading(true)
        async function GetInitalData() {
            let token = await AsyncStorage.getItem('token')
            setToken(token)
        }
        GetInitalData()

        console.log(props.route.params);

        if (props.route.params.source == true) {
            if (props.route.params.addressDesription) {
                setSource(props.route.params.addressDesription);
                setSourceId(props.route.params.addressId);
                setSourceLabel(props.route.params.addressLabel);
                setFromLat(props.route.params.lat);
                setFromLng(props.route.params.lng);
            }
        }
        if (props.route.params.source == false) {
            if (props.route.params.addressDesription) {
                setDestination(props.route.params.addressDesription);
                setDestinationId(props.route.params.addressId);
                setDestinationLabel(props.route.params.addressLabel);
                setToLat(props.route.params.lat);
                setTolng(props.route.params.lng);
            }
        }
        setLoading(false)

    }, [props, IsFocused]);

    const createNewTrip = async () => {
        console.log({
            sourceId: sourceId,
            fromLat: fromLat,
            fromLng: fromLng,
            source: source,
            destinationId: destinationId,
            toLat: toLat,
            toLng: toLng,
            destination: destination,
            date: date.slice(0, 10),
            time: moment(time, ["h:mm A"]).format("HH:mm"),
            numOfPeople: numOfPeople,
            numOfSeats: numOfSeats,
        });

        if (!token) {
            loginfirst()
        }
        else if ((sourceId == 0 && (fromLat == '' || fromLng == '' || source == '')) || (destinationId == 0 && (toLat == '' || toLng == '' || destination == '')) || time == '' || date == '' || numOfPeople == 0 || numOfSeats == 0) {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.pleaseCompleteData'),
                White,
                1500,
                screenHeight / 50,
            );
        }
        else {
            try {
                setLoadingMore(true)
                let response = await dispatch(vipTripsActions.createNewTrip(
                    sourceId == 0 ? fromLat : null,
                    sourceId == 0 ? fromLng : null,
                    sourceId == 0 ? source : null,
                    destinationId == 0 ? toLat : null,
                    destinationId == 0 ? toLng : null,
                    destinationId == 0 ? destination : null,
                    review,
                    date.slice(0, 10),
                    moment(time, ["h:mm A"]).format("HH:mm"),
                    numOfPeople,
                    numOfSeats,
                    sourceId != 0 ? sourceId : null,
                    destinationId != 0 ? destinationId : null
                ));

                if (response.success == true) {
                    Toaster(
                        'top',
                        'success',
                        DarkGreen,
                        strings('lang.Orderconfirmed'),
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    setLoadingMore(false)
                    props.navigation.navigate('Home')
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false)
                }
            } catch (err) {
                console.log(err);
                setLoadingMore(false)
            }
        }
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.ordervip')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.ordervip')}
                    // drawerPress={() => {
                    //     props.navigation.navigate('More');
                    // }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                {loadingMore
                    ?
                    <Loading />
                    :
                    <></>}

                <ScrollView
                    style={{ width: '95%', height: '100%', alignSelf: 'center', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >

                    <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 7, justifyContent: 'space-between' }}>
                        <View style={[styles.iconConatiner2, { paddingVertical: '5%' }]}>
                            <Image source={require('../images/bluelocation.png')} style={styles.locationImageSmall} />

                            <View style={{ height: '60%', width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, zIndex: 0, }}></View>

                            <Image source={require('../images/yallowlocation.png')} style={styles.locationImageSmall} />
                        </View>
                        <View style={{ width: '90%' }}>
                            <Pressable
                                onPress={() => { props.navigation.navigate('Search', { screen: 'OrderVip', source: true }) }}
                                style={styles.contentContainer2}>

                                <View style={styles.textInput}>
                                    <Text numberOfLines={1} style={{
                                        color: source ? Black : MediumGrey, textAlignVertical: 'center',
                                        fontFamily: appFont,
                                        fontSize: screenWidth / 32,
                                        textAlign: I18nManager.isRTL ? 'left' : 'left', width: '80%'
                                    }}>
                                        {
                                            sourceLabel
                                                ? sourceLabel
                                                :
                                                source
                                                    ?
                                                    source
                                                    :
                                                    strings('lang.Yourlocationis')
                                        }
                                    </Text>
                                    <Pressable onPress={() => { props.navigation.navigate('Destenation', { screen: 'OrderVip', source: true }) }}>
                                        <Text style={{
                                            color: DarkBlue, textAlignVertical: 'center',
                                            fontFamily: appFont,
                                            fontSize: screenWidth / 36,
                                            textAlign: I18nManager.isRTL ? 'right' : 'left',
                                            marginStart: '2%', textDecorationStyle: 'solid', textDecorationLine: 'underline'
                                        }}>
                                            {strings('lang.Locate')}
                                        </Text>
                                    </Pressable>
                                </View>
                            </Pressable>

                            <Pressable
                                onPress={() => { props.navigation.navigate('Search', { screen: 'OrderVip', source: false }) }}
                                style={styles.contentContainer2}>

                                <View style={styles.textInput}>
                                    <Text numberOfLines={1} style={{
                                        color: destination ? Black : MediumGrey, textAlignVertical: 'center',
                                        fontFamily: appFont,
                                        fontSize: screenWidth / 32,
                                        textAlign: I18nManager.isRTL ? 'left' : 'left',
                                        marginStart: '2%', width: '80%'
                                    }}>
                                        {
                                            destinationLabel ?
                                                destinationLabel :
                                                destination
                                                    ?
                                                    destination
                                                    :
                                                    strings('lang.destination')
                                        }
                                    </Text>
                                    <Pressable onPress={() => { props.navigation.navigate('Destenation', { screen: 'OrderVip', source: false }) }}>
                                        <Text style={{
                                            color: DarkBlue, textAlignVertical: 'center',
                                            fontFamily: appFont,
                                            fontSize: screenWidth / 36,
                                            textAlign: I18nManager.isRTL ? 'right' : 'left',
                                            marginStart: '2%', textDecorationStyle: 'solid', textDecorationLine: 'underline'
                                        }}>
                                            {strings('lang.Locate')}
                                        </Text>
                                    </Pressable>
                                </View>
                            </Pressable>
                        </View>
                    </View>

                    <View style={[styles.container, { marginVertical: '2%', marginTop: '6%' }]}>
                        <View style={styles.imageContainer}>
                            <View style={styles.image}>
                                <Image source={require('../images/Group10477.png')} style={styles.locationImage} />
                            </View>
                            <View style={styles.image}>
                                <Image source={require('../images/Group10517.png')} style={styles.locationImage} />
                            </View>
                        </View>

                        <View style={styles.inputContainer}>
                            <View style={styles.input}>
                                <View style={{ width: '80%', height: '100%', alignItems: 'center', flexDirection: 'row' }}>
                                    <Text style={{ fontFamily: appFontBold, color: MediumGrey, fontSize: screenWidth / 33, marginEnd: 5 }}>{strings('lang.Date')}</Text>
                                    <Text style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 33, marginEnd: 5 }}>{date.slice(0, 10)}</Text>
                                    <DateTimePickerModal
                                        isVisible={showDate}
                                        mode="date"
                                        onConfirm={
                                            (selectedDate) => {
                                                console.log('selectedDate', selectedDate)
                                                const currentDate = selectedDate || date;
                                                // setDate(currentDate);
                                                console.log('currentDateTime', moment(currentDate).format())
                                                setShowDate(false)
                                                setDate(moment(currentDate).format());
                                            }
                                        }
                                        onCancel={() => { setShowDate(false) }}
                                    />
                                    {/* {showDate
                                        ?
                                        <RNDateTimePicker
                                            testID="dateTimePicker"
                                            value={date}
                                            mode={'date'}
                                            display='compact'
                                            onChange={
                                                (event, selectedDate) => {
                                                    const currentDate = selectedDate || date;
                                                    setDate(currentDate);
                                                    console.log('currentDateTime', moment(currentDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", true).format().slice(11, 30))
                                                    if (Platform.OS != 'ios') {
                                                        setShowDate(false)
                                                        setDate(moment(currentDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", true).format().slice(11, 30));
                                                        setDateDisplay(moment(currentDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", true).format('MMMM Do YYYY, h:mm:ss a').split(',')[1]);
                                                    }
                                                    else {
                                                        // this.setState({ Date: moment(currentDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", true).format() });
                                                    }
                                                }
                                            }
                                            style={{ width: '100%', alignSelf: 'center' }}
                                        />
                                        :
                                        <></>
                                    } */}
                                </View>
                                <Pressable onPress={() => { setShowDate(true) }} style={styles.selectContainer}>
                                    <Text style={styles.text}>
                                        تحديد
                                    </Text>
                                </Pressable>
                            </View>
                            <View style={styles.input}>
                                <View style={{ width: '80%', height: '100%', alignItems: 'center', flexDirection: 'row' }}>
                                    <Text style={{ fontFamily: appFontBold, marginEnd: 5, color: MediumGrey, fontSize: screenWidth / 33 }}>{strings('lang.Time')}</Text>
                                    <Text style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 33, marginEnd: 5 }}>{time}</Text>
                                    <DateTimePicker
                                        isVisible={showTime}
                                        mode="time"
                                        timePickerModeAndroid='clock'
                                        onConfirm={
                                            (selectedDate) => {
                                                console.log('selectedDate', selectedDate)
                                                const currentDate = selectedDate || time;
                                                // setTime(currentDate);
                                                console.log('currentDateTime', moment(currentDate).format())
                                                setShowTime(false)
                                                setTime(moment(currentDate).format('LT'));
                                            }
                                        }
                                        onCancel={() => { setShowTime(false) }}
                                    />
                                    {/* {showTime
                                        ?
                                        <RNDateTimePicker
                                            testID="dateTimePicker"
                                            value={time}
                                            mode={'time'}
                                            display='compact'
                                            onChange={
                                                (event, selectedDate) => {
                                                    const currentDate = selectedDate || time;
                                                    setTime(currentDate);
                                                    console.log('currentDateTime', moment(currentDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", true).format().slice(11, 30))
                                                    if (Platform.OS != 'ios') {
                                                        setShowTime(false)
                                                        setTime(moment(currentDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", true).format().slice(11, 30));
                                                        setTimeDisplay(moment(currentDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", true).format('MMMM Do YYYY, h:mm:ss a').split(',')[1]);
                                                    }
                                                    else {
                                                        // this.setState({ Date: moment(currentDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", true).format() });
                                                    }
                                                }
                                            }
                                            style={{ width: '100%', alignSelf: 'center' }}
                                        />
                                        :
                                        <></>
                                    } */}
                                </View>
                                <Pressable onPress={() => { setShowTime(true) }} style={styles.selectContainer}>
                                    <Text style={styles.text}>
                                        تحديد
                                    </Text>
                                </Pressable>
                            </View>
                        </View>
                    </View>

                    <View style={styles.contentContainer}>
                        <View style={styles.iconConatiner2}>
                            <Image source={require('../images/66.png')} style={[styles.locationImage, { width: '50%', height: '50%' }]} />
                        </View>
                        <SelectDropdown
                            renderDropdownIcon={() => {
                                return (
                                    // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                                    <Image
                                        source={require('../images/Group9817.png')}
                                        style={styles.icon}
                                    />
                                );
                            }}
                            buttonTextAfterSelection={selectedItem => {
                                return selectedItem.num;
                            }}
                            dropdownIconPosition={'left'}
                            dropdownStyle={{ borderRadius: 5 }}
                            defaultButtonText={strings('lang.Numberofindividuals')}
                            buttonTextStyle={{
                                color: numOfPeople ? Black : MediumGrey,
                                fontFamily: appFont,
                                fontSize: screenWidth / 28,
                                textAlign: 'left',
                            }}
                            rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                            buttonStyle={{
                                width: '100%',
                                alignSelf: 'center',
                                textAlign: 'left',
                                fontFamily: appFont,
                                backgroundColor: White,
                                borderRadius: 20,
                                height: screenHeight / 18,
                                marginVertical: '2%',
                                borderWidth: 1,
                                borderColor: MediumGrey,
                                flexDirection: 'row',
                            }}
                            data={[{ num: 1 }, { num: 2 }, { num: 3 }, { num: 4 }, { num: 5 }, { num: 6 }, { num: 7 }]}
                            onSelect={selectedItem => {
                                setNumOfPeople(selectedItem.num)
                                // setError_areaId('');
                            }}
                            rowTextForSelection={item => {
                                return item.num;
                            }}
                        />
                    </View>

                    <View style={styles.contentContainer}>
                        <View style={styles.iconConatiner2}>
                            <Image source={require('../images/55.png')} style={[styles.locationImage, { width: '50%', height: '50%' }]} />
                        </View>
                        <SelectDropdown
                            renderDropdownIcon={() => {
                                return (
                                    // <FontAwesome name="chevron-down" color={"#444"} size={16} />
                                    <Image
                                        source={require('../images/Group9817.png')}
                                        style={styles.icon}
                                    />
                                );
                            }}
                            buttonTextAfterSelection={selectedItem => {
                                return selectedItem.num;
                            }}
                            dropdownIconPosition={'left'}
                            dropdownStyle={{ borderRadius: 5 }}
                            defaultButtonText={strings('lang.Thenumberofseatsinthecar')}
                            buttonTextStyle={{
                                color: numOfSeats ? Black : MediumGrey,
                                fontFamily: appFont,
                                fontSize: screenWidth / 28,
                                textAlign: 'left',
                            }}
                            rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                            buttonStyle={{
                                width: '100%',
                                alignSelf: 'center',
                                textAlign: 'left',
                                fontFamily: appFont,
                                backgroundColor: White,
                                borderRadius: 20,
                                height: screenHeight / 18,
                                marginVertical: '2%',
                                borderWidth: 1,
                                borderColor: MediumGrey,
                                flexDirection: 'row',
                            }}
                            data={[{ num: 1 }, { num: 2 }, { num: 3 }, { num: 4 }, { num: 7 }, { num: 6 }, { num: 7 }]}
                            onSelect={selectedItem => {
                                setNumOfSeats(selectedItem.num)
                                // setError_areaId('');
                            }}
                            rowTextForSelection={item => {
                                return item.num;
                            }}
                        />
                    </View>

                    <View style={styles.input1}>
                        <Textarea
                            onChangeText={text => setReview(text)}
                            value={review}
                            placeholder={strings('lang.Notesaboutthetripandthecar')}
                            placeholderTextColor={MediumGrey}
                            style={{
                                width: '100%',
                                alignSelf: 'center',
                                // borderRadius: 15,
                                paddingHorizontal: '5%',
                                borderColor: MediumGrey,
                                color: review ? Black : DarkGrey,
                                // borderWidth: 1,
                                fontFamily: appFontBold,
                                height: '100%',
                                textAlignVertical: 'top',
                                fontSize: screenWidth / 30,
                                textAlign: I18nManager.isRTL ? 'right' : 'left',
                            }}
                        />
                    </View>

                </ScrollView>

                <Button onPress={() => { createNewTrip() }} transparent
                    style={{
                        width: '90%', height: screenHeight / 20,
                        alignSelf: 'center', justifyContent: 'center', alignItems: 'center',
                        backgroundColor: DarkBlue, borderRadius: 25,
                    }}>
                    <Text style={{ fontFamily: appFontBold, color: White, fontSize: screenWidth / 25, }}>{strings('lang.Confirm').toUpperCase()}</Text>
                </Button>



                <View style={{ height: screenHeight / 15 }}></View>

            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        height: screenHeight / 9,
        width: '100%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row',
        marginVertical: '5%',
        // backgroundColor: Red
    },
    imageContainer: {
        height: '80%',
        width: '10%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    image: {
        height: '25%',
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
    },
    locationImage: {
        height: '100%',
        width: '90%',
        resizeMode: 'contain'
    },
    inputContainer: {
        height: '100%',
        width: '90%',
        // alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    input: {
        height: screenHeight / 20,
        borderRadius: 20,
        width: '100%',
        backgroundColor: White,
        borderWidth: .8,
        flexDirection: 'row',
        paddingStart: '2%',
        borderColor: MediumGrey,
    },
    activeInput: {
        borderColor: DarkBlue,
        borderWidth: .8,
        height: screenHeight / 20,
        borderRadius: 20,
        width: '100%',
        flexDirection: 'row',
        paddingStart: '2%',
        backgroundColor: White,
    },
    input1: {
        height: screenHeight / 5,
        borderRadius: 20,
        width: '90%',
        backgroundColor: White,
        borderWidth: .8,
        flexDirection: 'row',
        paddingStart: '2%',
        borderColor: MediumGrey,
        marginVertical: '5%',
        alignSelf: 'flex-end'
    },
    activeInput1: {
        borderColor: DarkBlue,
        borderWidth: .8,
        height: screenHeight / 5,
        borderRadius: 20,
        width: '90%',
        flexDirection: 'row',
        paddingStart: '2%',
        backgroundColor: White,
        marginVertical: '5%',
        alignSelf: 'flex-end'
    },
    inputText: {
        color: Black,
        width: '80%',
        fontFamily: appFontBold,
        fontSize: screenWidth / 33,
        alignSelf: 'center',
        textAlign: I18nManager.isRTL ? 'right' : 'left',
    },
    selectContainer: {
        // backgroundColor: White,
        width: screenWidth / 8,
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        end: 0
    },
    icon: {
        width: '5%',
        height: '40%',
        tintColor: MediumGrey,
        resizeMode: 'contain',
        marginHorizontal: '7%',
    },
    text: { fontFamily: appFontBold, color: DarkBlue, fontSize: screenWidth / 40, textDecorationLine: 'underline' },
    labelBlack: { fontFamily: appFont, color: Black, fontSize: screenWidth / 33, },
    contentContainer: {
        width: '90%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2%',
        height: screenHeight / 18,
        borderRadius: 5,
        paddingHorizontal: 0,
        flexDirection: 'row',
    },
    iconConatiner2: { height: '100%', width: '10%', alignItems: 'center', justifyContent: 'center', },
    contentContainer2: {
        width: '100%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2%',
        height: screenHeight / 18,
        borderRadius: 5,
        paddingHorizontal: 0,
        flexDirection: 'row',
    },
    locationImageSmall: {
        resizeMode: 'contain',
        width: '100%',
        height: '20%'
    },
    textInput: {
        width: '100%',
        flexDirection: 'row',
        alignSelf: 'center',
        borderRadius: 100,
        borderColor: MediumGrey,
        borderWidth: 1,
        height: '100%',
        alignItems: 'center',
        justifyContent: 'space-between',
        textAlignVertical: 'center',
        fontFamily: appFont,
        fontSize: screenWidth / 32,
        textAlign: I18nManager.isRTL ? 'right' : 'left',
        paddingHorizontal: 10, overflow: 'hidden'
    },
});

export default OrderVip;
