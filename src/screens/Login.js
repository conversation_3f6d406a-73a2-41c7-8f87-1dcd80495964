import { But<PERSON>, Container } from 'native-base';
import React, { useState } from 'react';
import {
  BackHandler,
  I18nManager,
  Image,
  ImageBackground,
  Keyboard,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
  appColor1,
  appFont,
  appFontBold,
  Black,
  DarkBlue,
  DarkGreen,
  Gold,
  MediumGrey,
  Red,
  screenHeight,
  screenWidth,
  <PERSON>,
  WhiteGery
} from '../components/Styles';
import { strings } from './i18n';

import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import RNRestart from 'react-native-restart';
import { useDispatch, useSelector } from 'react-redux';
import * as authActions from '../../Store/Actions/auth';
import Loading from '../components/Loading';
import Toaster from '../components/Toaster';
import I18n from './i18n';

const Login = props => {
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [activeText, setActiveText] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [phoneError, setPhoneError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const dispatch = useDispatch();
  const force_update = useSelector(state => state.general.force_update);

  const handleSubmit = async () => {
    if (!phone || phone.length != 10 || !phone.match(/^05/) || !password || password.length < 6) {
      if (!phone) {
        setPhoneError(strings('lang.Please_insert_field'));
      }
      else if (phone.length != 10) {
        setPhoneError(strings('lang.Phone_must_be_10_characters'));
      }
      else if (!phone.match(/^05/)) {
        setPhoneError(strings('lang.validationPhone'))
      }
      if (!password) {
        setPasswordError(strings('lang.Please_insert_password'));
      }
      else if (password.length < 6) {
        setPasswordError(
          strings('lang.Password_confirmation_must_be_more_6_characters'),
        );
      }
    }
    else {
      Keyboard.dismiss();
      setLoading(true);
      try {
        let response = await dispatch(
          authActions.login(
            // formState.inputValues.email,
            // formState.inputValues.password
            phone,
            password,
          ),
        );
        if (response.status == 201 || response.status == 202) {
          if (response.status == 201) {
            Toaster(
              'top',
              'success',
              DarkGreen,
              strings('lang.CodeSentSuccessfully'),
              White,
              1500,
              screenHeight / 50,
            );
            props.navigation.navigate('ActiveAccount', { forget: false, phone: phone })
          }
          else if (response.status == 202) {
            Toaster(
              'top',
              'danger',
              Red,
              response.message,
              White,
              1500,
              screenHeight / 50,
            );
            props.navigation.navigate('ActiveAccount', { forget: false, phone: phone })
          }
          setLoading(false);
        }
        else {
          if (response.token) {
            Toaster(
              'top',
              'success',
              DarkGreen,
              strings('lang.Login_Successfuly'),
              White,
              1500,
              screenHeight / 50,
            );
            await AsyncStorage.setItem('token', JSON.stringify(response.token));
            props.navigation.push('Home', {});
            setLoading(false);
          }
          else {
            if (response.message) {
              Toaster(
                'top',
                'danger',
                Red,
                response.message,
                White,
                1500,
                screenHeight / 50,
              );
              setLoading(false);
            }
          }
        }

      } catch (err) {
        setLoading(false);
      }
    }
  };

  const set_en = async () => {
    let lan = await AsyncStorage.getItem("lan")
    console.log('kk', lan);
    if (lan == 'ar') {
      // this.setState({
      //   lan: 'en',
      // })
      await AsyncStorage.setItem('lan', 'en')
      await AsyncStorage.setItem('stat', '1')
      I18n.locale = "en";
      await I18nManager.forceRTL(false);
      RNRestart.Restart();
    }
    else {
      await AsyncStorage.setItem('lan', 'ar')
      await AsyncStorage.setItem('stat', '1')
      I18n.locale = "ar";
      await I18nManager.forceRTL(true);
      RNRestart.Restart();
    }
  }
  const set_ar = async () => {
    let lan = await AsyncStorage.getItem("lan")
    console.log('kk', lan);
    if (lan == 'en') {
      // this.setState({
      //   lan: 'ar',
      //   modalDeleteVisible: false
      // })
      await AsyncStorage.setItem('lan', 'ar')
      await AsyncStorage.setItem('stat', '1')
      I18n.locale = "ar";
      await I18nManager.forceRTL(true);
      RNRestart.Restart();
    }
    else {
      await AsyncStorage.setItem('lan', 'en')
      await AsyncStorage.setItem('stat', '1')
      I18n.locale = "en";
      await I18nManager.forceRTL(false);
      RNRestart.Restart();
    }
  }

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // BackHandler.exitApp()
        return true;
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );

  return (
    <Container>
      {loading ? <Loading /> : <View></View>}
      <ImageBackground source={require('../images/MaskGroup22.png')} style={{ width: screenWidth, height: '100%', resizeMode: 'cover', alignSelf: 'center', }} >
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false} style={{}}>
          <View
            style={{
              width: '100%',
              height: screenHeight / 3.2,
              alignSelf: 'center',
              justifyContent: 'center',
              marginTop: screenHeight / 8,
            }}
          >
            <Image
              source={require('../images/Group10544.png')}
              style={{
                width: '80%',
                height: '80%',
                resizeMode: 'contain',
                alignSelf: 'center',
              }}
            />
          </View>
          <View style={{ height: screenHeight / 1.8, width: '100%', alignItems: 'center', justifyContent: 'center', }}>
            <View
              style={{
                width: '90%',
                alignSelf: 'center',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                marginBottom: '0%',

              }}
            >
              <Text style={styles.label}>{strings('lang.youwelcomein')}</Text>

              <Text style={styles.label1}>{'STEP'}</Text>

              <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Button onPress={() => { props.navigation.navigate('Login1', { userType: '1' }) }} style={{ ...styles.buttonContainer, ...{ width: Platform.OS == 'ios' ? force_update ? '47.5%' : '100%' : '47.5%' } }}>
                  <Text style={[styles.buttonText, { color: White }]}>{strings('lang.Login1')}</Text>
                </Button>
                {Platform.OS == 'ios'
                  ?
                  force_update
                    ?
                    <Button onPress={() => { props.navigation.navigate('Login1', { userType: '2' }) }} style={{ ...styles.buttonContainer, ...{ width: '47.5%' } }}>
                      <Text style={[styles.buttonText, { color: White }]}>{strings('lang.Login2')}</Text>
                    </Button>
                    :
                    <></>
                  :
                  <Button onPress={() => { props.navigation.navigate('Login1', { userType: '2' }) }} style={{ ...styles.buttonContainer, ...{ width: '47.5%' } }}>
                    <Text style={[styles.buttonText, { color: White }]}>{strings('lang.Login2')}</Text>
                  </Button>
                }

              </View>

              <Button onPress={() => { props.navigation.navigate('Test2', {}) }} style={styles.buttonContainer1}>
                <Text style={styles.buttonText}>{strings('lang.Loginasaguest')}</Text>
              </Button>

            </View>
          </View>

          <TouchableOpacity
            style={{ width: screenWidth / 15, height: screenHeight / 15, position: 'absolute', top: Platform.OS == 'ios' ? '5%' : '5%', end: '5%' }}
            onPress={() => { I18nManager.isRTL ? set_en() : set_ar() }}
          >
            <Image
              style={{ width: '100%', height: '100%', resizeMode: 'contain', tintColor: Gold }}
              source={require('../images/earth.png')}
            />
          </TouchableOpacity>

          <View style={{ height: screenHeight / 15 }}></View>
        </KeyboardAwareScrollView>


      </ImageBackground>

    </Container>
  );
};
const styles = StyleSheet.create({
  registerationContainer: {
    flexDirection: 'row',
    marginVertical: screenHeight / 50
  },
  registerationText: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 28,
    color: Black,
  },
  registerationText1: {
    color: appColor1,
    marginHorizontal: 5,
    fontFamily: appFontBold,
    fontSize: screenWidth / 28,
  },
  label: {
    fontSize: screenWidth / 20,
    fontFamily: appFontBold,
    alignSelf: 'flex-start',
    marginHorizontal: '2.5%',
    marginBottom: 5,
    color: Black,
  },
  label1: {
    fontSize: screenWidth / 15,
    fontFamily: appFontBold,
    alignSelf: 'flex-start',
    marginHorizontal: '2.5%',
    marginBottom: 5,
    color: Black,
  },
  buttonContainer: {
    backgroundColor: DarkBlue,
    width: '95%',
    height: screenHeight / 18,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    borderRadius: 100,
    marginTop: screenHeight / 60,
  },
  buttonContainer1: {
    backgroundColor: Gold,
    width: '95%',
    height: screenHeight / 18,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    borderRadius: 100,
    marginTop: screenHeight / 60,
  },
  buttonText: {
    color: Black,
    fontFamily: appFontBold,
    fontSize: screenWidth / 28,
  },
  skipContainer: {
    marginVertical: screenHeight / 20,
    flexDirection: 'row',
  },
  skipText: {
    fontSize: screenWidth / 25,
    fontFamily: appFontBold,
    color: '#000',

    alignSelf: 'center',
    marginHorizontal: 5,
  },
  forgetTextcontiner: {
    marginVertical: 5,
    // width: screenWidth / 2,
    alignSelf: 'flex-start',
    marginHorizontal: '2.5%',
  },
  forgetText: {
    alignSelf: 'flex-start',
    fontSize: screenWidth / 30,
    fontFamily: appFont,
    color: Red,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    height: screenHeight,
  },
  input: {
    height: 45,
    borderRadius: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '95%',
    marginBottom: 10,
    backgroundColor: White,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: MediumGrey,
    alignSelf: 'center',
  },
  activeInput: {
    borderColor: appColor1,
    borderWidth: 1,
    height: 45,
    borderRadius: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '95%',
    marginBottom: 10,
    backgroundColor: White,
    paddingHorizontal: 10,
    alignSelf: 'center',
  },
  inputText: {
    color: White,
    width: '100%',
    fontFamily: appFontBold,
    fontSize: screenWidth / 33,
    alignSelf: 'center',
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  eyeContainer: {
    width: screenWidth / 10,
    alignItems: 'center',
    justifyContent: 'center',

  },
  eye: {
    width: '100%',
    resizeMode: 'contain',
    tintColor: MediumGrey,
  },
  loginOptionContainer: { width: screenWidth / 4, height: screenHeight / 18, backgroundColor: WhiteGery, borderRadius: 10, alignItems: 'center', justifyContent: 'center' },
  loginOptionImage: { width: '60%', height: '60%', resizeMode: 'contain' },
});

export default Login;
