import React, { Component, useEffect, useState } from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  StyleSheet,
  I18nManager,
  Touchable,
  TouchableOpacity,
  Platform,
  BackHandler,
} from 'react-native';
import {
  appFont,
  appFontBold,
  Green,
  WhiteGreen,
  screenWidth,
  White,
  DarkGreen,
  Blue,
  MediumGrey,
  DarkGrey,
  WhiteGery,
  Black,
  screenHeight,
  Red,
  DarkBlue,
  MediumYellow,
} from '../components/Styles';
import { Button, Textarea } from 'native-base';
import Header from '../components/Header';
import { ScrollView, TextInput } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import KeyboardAvoidingView from 'react-native/Libraries/Components/Keyboard/KeyboardAvoidingView';
import { FlashList } from '@shopify/flash-list';
import MessageBubble from '../components/MessageBubble';
import * as chatActions from '../../Store/Actions/chat';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Loading from '../components/Loading';
import { useFocusEffect } from '@react-navigation/native';

export const CLEARCHAT = 'CLEARCHAT';


const Chat = props => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [extraData, setExtraData] = useState(false);
  const [page, setPage] = useState(1);
  const [userId, setUserId] = useState('');
  const [me, setMe] = useState({});
  const [him, setHim] = useState({});
  const newMessage = useSelector((state) => state.message);

  // let messages = [
  //   {
  //     id: 1,
  //     message: 'lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem ',
  //     time: '8.07'
  //   },
  //   {
  //     id: 1,
  //     message: 'lorem lorem ',
  //     time: '8.07'
  //   }
  // ]

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        if (props.route.params.screen == 'MapAddress') {
          props.navigation.navigate(props.route.params.screen, { screen: 'Home', cityTrip: false, pending: true, pendingTrip: props.route.params.trip });
        }
        else {
          props.navigation.goBack();
        }
        return true;
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );

  useEffect(() => {
    const getChat = async () => {
      let userId = await AsyncStorage.getItem('id');
      setUserId(userId)
      setLoading(true)
      try {
        let response = await dispatch(chatActions.getChat(props.route.params.receiver_id, props.route.params.receiver_type, 1, props.route.params.subject_id, props.route.params.subject_type));
        if (response.success == true) {
          setMessages(response.data.items)
          setHasMorePages(response.data.has_more_pages)
        }
        else {
          if (response.message) {
            Toaster(
              'top',
              'danger',
              Red,
              response.message,
              White,
              1500,
              screenHeight / 15,
            );
          }
        }
        setLoading(false);
      }
      catch (err) {
        console.log('err', err)
        setLoading(false);
      }
    };

    getChat()
  }, []);

  useEffect(() => {
    console.log('Message reducer In Screen', newMessage);
    if (newMessage.message.chat_message) {
      console.log('dsa', [...[newMessage.message.chat_message], ...messages]);
      setMessages([...[newMessage.message.chat_message], ...messages])
      setExtraData(!extraData)
      dispatch({ type: CLEARCHAT });
    }
  }, [newMessage]);

  const LoadMore = async () => {
    if (messages.length != 0 && hasMorePages) {
      try {
        let response = await dispatch(chatActions.getChat(props.route.params.receiver_id, props.route.params.receiver_type, page + 1, props.route.params.subject_id, props.route.params.subject_type));
        if (response.success == true) {
          setMessages([...messages, ...response.data.items])
          setHasMorePages(response.data.has_more_pages);
          setPage(page + 1)
        }
        else {
        }
      } catch (err) {
      }

    }
    else {
      console.log(('end'));
    }
  }

  const renderMessageBubble = (props) => {
    const { item } = props;
    return <MessageBubble message={item} userId={userId} />;
  };

  const sendMessage = async () => {
    if (!message || message == '') {
    }
    else {
      setLoadingMore(true)
      let response = await dispatch(chatActions.sendMessage(props.route.params.receiver_id, props.route.params.receiver_type, message, props.route.params.subject_id, props.route.params.subject_type));;
      console.log('response', response);
      if (response.success == true) {
        console.log('asd', [...[response.data], ...messages]);
        setMessages([...[response.data], ...messages])
        setExtraData(!extraData)
        setMessage('')
        setLoadingMore(false)
      }
      else {
        Toaster(
          'top',
          'danger',
          Red,
          response.message,
          White,
          1500,
          screenHeight / 15,
        );
        setLoadingMore(false)
      }
    }
  };

  if (loading) {
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.Chat')}
          drawerPress={() => {
            props.navigation.navigate('More');
          }}
          backPress={() => {
            if (props.route.params.screen == 'MapAddress') {
              props.navigation.navigate(props.route.params.screen, { screen: 'Home', cityTrip: false, pending: true, pendingTrip: props.route.params.trip });
            }
            else {
              props.navigation.goBack();
            }
          }}
        />

        <ScrollView
          style={{ width: '100%', height: '100%', marginBottom: '2%' }}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.section}>
            <View style={styles.textContanier}>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>



            </View>
          </View>
          <View style={{ height: screenHeight / 8 }}></View>
        </ScrollView>

      </View>
    );

  } else {
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.Chat')}
          drawerPress={() => {
            props.navigation.navigate('More');
          }}
          backPress={() => {
            if (props.route.params.screen == 'MapAddress') {
              props.navigation.navigate(props.route.params.screen, { screen: 'Home', cityTrip: false, pending: true, pendingTrip: props.route.params.trip });
            }
            else {
              // props.navigation.navigate(props.route.params.screen, {
              //   screen: 'DriverRequests', pending: true, driverPendingTrip: props.route.params.trip,
              //   driverPendingOffer: null,
              //   request: props.route.params.subject_type == "DailyTrip" ? 1 : props.route.params.subject_type == "VipTrip" ? 3
              //     : props.route.params.subject_type == "CityTrip" ? 4 : 2,
              //   trip: props.route.params.trip
              // });
              props.navigation.goBack();
            }
            // else if (props.route.params.screen == 'DetailsTrip') {
            //   // if (props.route.params.subject_type == 'DailyTrip') {
            //   //   props.navigation.navigate('MapAddress', { screen: 'Home', cityTrip: false, pending: true, pendingTrip: props.route.params.trip })
            //   // }
            //   // else {
            //   //   props.navigation.navigate(props.route.params.screen, {
            //   //     item: props.route.params.trip,
            //   //     type: props.route.params.subject_type == "VipTrip" ? 'orderVip'
            //   //       : props.route.params.subject_type == "CityTrip" ? 'betweenCities' : 'packages',
            //   //   });
            //   // }
            //   props.navigation.goBack();
            // }
          }}
        />

        {/* {loadingMore
          ?
          <Loading />
          :
          <></>
        } */}

        <KeyboardAvoidingView style={{ flex: 1 }} behavior={Platform.OS == 'ios' ? 'padding' : null} >
          <FlashList
            inverted
            extraData={extraData}
            data={messages}
            // data={[...messages].reverse()}
            renderItem={renderMessageBubble}
            estimatedItemSize={40}
            onEndReached={() => { LoadMore() }} // Trigger load more when reaching the end
            onEndReachedThreshold={0.1} // Define how close to the end the user must get to trigger the event
          />

          <View style={{ width: screenWidth, height: screenHeight / 9, backgroundColor: MediumGrey, alignItems: 'center', justifyContent: 'center' }}>
            <View style={{ overflow: 'hidden', width: screenWidth / 1.1, height: screenHeight / 15, backgroundColor: White, borderRadius: 100, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
              <TextInput
                multiline={true}
                placeholderTextColor={'#707070'}
                placeholder={strings('lang.Write')}
                style={styles.inputText}
                value={message}
                onChangeText={text => {
                  setMessage(text);
                }}
              />
              <TouchableOpacity onPress={() => { sendMessage() }} style={{ width: screenWidth / 11, height: screenHeight / 18, alignItems: 'center', justifyContent: 'center' }}>
                <Image source={require('../images/send.png')} style={{ width: '80%', height: '80%', resizeMode: 'contain', tintColor: DarkBlue, transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>

      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: 'white',
  },
  section: { width: '90%', alignSelf: 'center' },
  labelContanier: {
    width: '100%',
    marginVertical: '3%',
    alignItems: 'flex-start',
  },
  textContanier: {
    width: '100%',
    marginVertical: '2%',
    alignItems: 'flex-start',
  },
  label: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 20, marginTop: screenHeight / 50, alignSelf: 'flex-start' },
  body: {
    fontFamily: appFontBold,
    color: DarkGrey,
    fontSize: screenWidth / 24,
    alignSelf: 'flex-start',
    textAlign: 'left'
  },
  inputText: {
    color: Black,
    width: '88%',
    fontFamily: appFontBold,
    fontSize: screenWidth / 30,
    alignSelf: 'center',
    textAlign: Platform.OS ? I18nManager.isRTL ? 'right' : 'left' : I18nManager.isRTL ? 'left' : 'right',
    // borderTopRightRadius: I18nManager.isRTL ? 0 : 100,
    // borderBottomRightRadius: I18nManager.isRTL ? 0 : 100,
    // height: '50%',
    // flex: 1,
    paddingHorizontal: 10,
    textAlignVertical: 'center'
  },
});

export default Chat;
