import {
    Image,
    View,
    Text,
    StyleSheet,
    I18nManager,
    BackHandler,
    TouchableOpacity,
    Keyboard,
    Platform,
    ImageBackground,
} from 'react-native';
import React, { Component, useEffect, useState } from 'react';
import {
    screenHeight,
    DarkBlue,
    screenWidth,
    DarkGrey,
    White,
    appFont,
    WhiteGery,
    appFontMedium,
    MediumGrey,
    appFontBold,
    DarkGreen,
    Red,
    Green,
    appColor2,
    appColor1,
    Black,
    WhiteGreen,
    DarkYellow,
    MediumGreen,
    Gold,
} from '../components/Styles';
import { Input, Container, Button, Toast } from 'native-base';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { TextInput } from 'react-native-gesture-handler';
import { strings } from './i18n';

import * as authActions from '../../Store/Actions/auth';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Loading from '../components/Loading';
import Header from '../components/Header';
import SelectDropdown from 'react-native-select-dropdown';
import messaging from '@react-native-firebase/messaging';
import * as generalActions from '../../Store/Actions/general';
import RNRestart from 'react-native-restart';

import { launchCamera, launchImageLibrary } from 'react-native-image-picker'; // Migration from 2.x.x to 3.x.x => showImagePicker API is removed.
import { io } from "socket.io-client";
import { connectSocket } from '../../Store/Actions/socket';
const socket = io('http://65.108.33.237', { transports: ['websocket', 'polling'] });

const Registration = props => {
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [genderName, setGenderName] = useState('');
    const [genderId, setGenderId] = useState(0);
    const [loading, setLoading] = useState(false);
    const [nameError, setNameError] = useState('');
    const [emailError, setEmailError] = useState('');
    const [genderError, setGenderError] = useState('');
    const [passwordError, setPasswordError] = useState('');
    const [cover, setCover] = useState(null);
    const [genders, setGenders] = useState([
        {
            id: 'male',
            name: strings('lang.male')
        },
        {
            id: 'female',
            name: strings('lang.female')
        }
    ]);
    const dispatch = useDispatch();

    const register = async () => {
        let data = props.route.params.data

        setLoading(true)
        let register = await dispatch(authActions.register(data.user.id, name, email, genderId, cover));
        console.log('register', register);
        if (register.success == true) {
            await AsyncStorage.setItem("userType", props.route.params.userType);
            await AsyncStorage.setItem("id", JSON.stringify(register.data.id));
            await AsyncStorage.setItem("name", register.data.name ? register.data.name : '');
            await AsyncStorage.setItem("email", register.data.email ? register.data.email : '');
            await AsyncStorage.setItem("image", register.data.image ? register.data.image : '');
            await AsyncStorage.setItem("dial_code", register.data.dial_code ? register.data.dial_code : '');
            await AsyncStorage.setItem("phone", register.data.phone ? register.data.phone : '');
            await AsyncStorage.setItem("gender", register.data.gender ? register.data.gender : '');

            await messaging().registerDeviceForRemoteMessages();
            const FcmToken = await messaging().getToken();
            console.log('getFcmToken', FcmToken);
            await AsyncStorage.setItem("FcmToken", FcmToken ? FcmToken : '');
            let addFcmToken = await dispatch(generalActions.addFcmToken(FcmToken));
            console.log('addFcmToken', addFcmToken);

            setLoading(false)
            Toaster(
                'top',
                'success',
                'green',
                (strings('lang.Login_Successfuly')),
                White,
                1500,
                screenHeight / 15,
            );
            dispatch(connectSocket(JSON.stringify(register.data.id), '1'));
            // RNRestart.Restart();

            props.navigation.push('Home')

            // socket.on('connect', async () => {
            //     console.log(JSON.stringify(register.data.id));
            //     console.log('user');

            //     let s = socket.emit('join', {
            //         userId: JSON.stringify(register.data.id),
            //         userType: 'user'
            //     });
            //     console.log('sockett', s);
            // });
        }
        else {
            setLoading(false)
            Toaster(
                'top',
                'danger',
                Red,
                register.message,
                White,
                1500,
                screenHeight / 15,
            );
        };
    };

    const chooseImage = async () => {
        let options = {
            quality: 0.5,
            maxWidth: 400,
            maxheight: 400,
            title: 'Select Image',
            customButtons: [
                {
                    name: 'customOptionKey',
                    title: 'Choose Photo from Custom Option',
                },
            ],
            storageOptions: {
                skipBackup: true,
                path: 'images',
            },
        };
        launchImageLibrary(options, response => {
            console.log('Response = ', response);

            if (response.didCancel) {
                console.log('User cancelled image picker');
            } else if (response.error) {
                console.log('ImagePicker Error: ', response.error);
            } else if (response.customButton) {
                console.log('User tapped custom button: ', response.customButton);
                alert(response.customButton);
            } else {
                // let cover = response.assets[0];
                // You can also display the image using data:
                // let source = {
                //   uri: 'data:image/jpeg;base64,' + response.data
                // };
                let cover = {
                    uri: response.assets[0].uri,
                    type: response.assets[0].type,
                    name: response.assets[0].fileName,
                    link: '',
                };
                setCover(cover)
            }
        });
    };

    return (
        <Container>
            {loading ? <Loading /> : <View></View>}
            <Header title={strings('lang.Registerion')} backPress={() => { props.navigation.goBack() }} background />
            <KeyboardAwareScrollView showsVerticalScrollIndicator={false} style={{}}>

                <View style={{ height: screenHeight, width: '100%', alignItems: 'center', justifyContent: 'center', backgroundColor: WhiteGery }}>
                    <View
                        style={{
                            width: '90%',
                            alignSelf: 'center',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            flexDirection: 'column',
                            marginBottom: '40%',
                            backgroundColor: White,
                            height: screenHeight / 1.3, borderWidth: .5, borderColor: MediumGrey, borderRadius: 25, paddingVertical: '10%'
                        }}
                    >
                        <View style={styles.input}>
                            <View style={{ flexDirection: 'row' }}>
                                <Text style={styles.label}>{strings('lang.Name')}</Text>
                                <Text style={styles.star}>*</Text>
                            </View>
                            <TextInput
                                placeholderTextColor={'#707070'}
                                placeholder={strings('lang.Name')}
                                style={styles.inputText}
                                value={name}
                                onChangeText={text => {
                                    setName(text);
                                    setNameError('');
                                }}
                            />
                        </View>
                        <View style={styles.input}>
                            <View style={{ flexDirection: 'row' }}>
                                <Text style={styles.label}>{strings('lang.Email')}</Text>
                                <Text style={styles.star}>*</Text>
                            </View>
                            <TextInput
                                placeholderTextColor={'#707070'}
                                placeholder={strings('lang.Email')}
                                style={styles.inputText}
                                keyboardType="email-address"
                                value={email}
                                onChangeText={text => {
                                    setEmail(text);
                                    setEmailError('');
                                }}
                            />
                        </View>
                        <View style={styles.input}>
                            <View style={{ flexDirection: 'row' }}>
                                <Text style={styles.label}>{strings('lang.Gender')}</Text>
                                <Text style={styles.star}>*</Text>
                            </View>
                            <SelectDropdown
                                renderDropdownIcon={() => {
                                    return (
                                        <Image
                                            source={require('../images/Group9817.png')}
                                            style={styles.icon}
                                        />
                                    );
                                }}
                                buttonTextAfterSelection={selectedItem => {
                                    return selectedItem.name;
                                }}
                                dropdownIconPosition={'left'}
                                dropdownStyle={{ borderRadius: 5 }}
                                defaultButtonText={' '}
                                buttonTextStyle={{
                                    color: DarkGrey,
                                    fontFamily: appFont,
                                    fontSize: screenWidth / 28,
                                    textAlign: 'left',
                                }}
                                rowTextStyle={{ color: DarkGrey, fontFamily: appFont }}
                                buttonStyle={{
                                    width: '100%',
                                    alignSelf: 'center',
                                    textAlign: 'left',
                                    fontFamily: appFont,
                                    backgroundColor: White,
                                    borderRadius: 100,
                                    height: screenHeight / 18,
                                    marginBottom: '2%',
                                    borderWidth: 1,
                                    borderColor: MediumGrey,
                                    flexDirection: 'row',
                                }}
                                data={genders}
                                onSelect={async (selectedItem) => {
                                    setGenderName(selectedItem.name);
                                    setGenderId(selectedItem.id);
                                }}
                                rowTextForSelection={item => {
                                    return item.name;
                                }}
                            />
                        </View>
                        <View style={[styles.input, { height: screenHeight / 6 }]}>
                            <Text style={styles.label}>{strings('lang.Please_insert_image')}</Text>
                            <TouchableOpacity onPress={chooseImage} style={{ width: '100%', height: screenHeight / 8, borderRadius: 10, borderStyle: 'dashed', borderColor: MediumGrey, borderWidth: 1, alignItems: 'center', justifyContent: 'center' }}>
                                <Image source={cover ? { uri: cover.uri } : require('../images/img.png')} style={cover ? { width: '100%', height: '100%', resizeMode: 'contain' } : { width: '50%', height: '50%', tintColor: DarkGrey, resizeMode: 'contain' }} />
                            </TouchableOpacity>
                        </View>


                        {/* <Button onPress={() => { props.navigation.navigate('MapAddress', { isPaid: false }) }} style={styles.buttonContainer}> */}
                        <Button onPress={() => { register() }} style={styles.buttonContainer}>
                            <Text style={styles.buttonText}>{strings('lang.Register')}</Text>
                        </Button>

                    </View>
                </View>

                <View style={{ height: screenHeight / 15 }}></View>
            </KeyboardAwareScrollView>
        </Container>
    );
};
const styles = StyleSheet.create({
    registerationContainer: {
        flexDirection: 'row',
        marginVertical: screenHeight / 50
    },
    registerationText: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        color: Black,
    },
    registerationText1: {
        color: appColor1,
        marginHorizontal: 5,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
    },
    label: {
        color: Black, fontFamily: appFontBold, alignSelf: 'flex-start', fontSize: screenWidth / 30, marginBottom: 2,
    },
    label1: {
        fontSize: screenWidth / 15,
        fontFamily: appFontBold,
        alignSelf: 'flex-start',
        marginHorizontal: '2.5%',
        marginBottom: 5,
        color: Black,
    },
    buttonContainer: {
        backgroundColor: DarkBlue,
        width: '95%',
        height: screenHeight / 18,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
        borderRadius: 100,
        marginTop: 10,
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
    },
    skipContainer: {
        marginVertical: screenHeight / 20,
        flexDirection: 'row',
    },
    skipText: {
        fontSize: screenWidth / 25,
        fontFamily: appFontBold,
        color: '#000',

        alignSelf: 'center',
        marginHorizontal: 5,
    },
    forgetTextcontiner: {
        marginVertical: 5,
        // width: screenWidth / 2,
        alignSelf: 'flex-start',
        marginHorizontal: '2.5%',
    },
    forgetText: {
        alignSelf: 'flex-start',
        fontSize: screenWidth / 30,
        fontFamily: appFont,
        color: Red,
    },
    container: {
        flex: 1,
        alignItems: 'center',
        height: screenHeight,
    },
    input: {
        height: screenHeight / 18,
        // flexDirection: 'row',
        justifyContent: 'space-between',
        width: '95%',
        marginBottom: 10,
        backgroundColor: White,
        paddingHorizontal: 10,
        alignSelf: 'center',
    },
    activeInput: {
        borderColor: Green,
        borderWidth: 1,
        height: screenHeight / 18,
        borderRadius: 5,
        flexDirection: 'row',
        justifyContent: 'center',
        width: '95%',
        marginBottom: 10,
        backgroundColor: White,
        paddingHorizontal: 10,
        alignSelf: 'center',
    },
    inputText: {
        color: Black,
        width: '100%',
        fontFamily: appFontBold,
        fontSize: screenWidth / 30,
        alignSelf: 'center',
        textAlign: I18nManager.isRTL ? 'right' : 'left',
        borderWidth: 1,
        borderColor: MediumGrey,
        borderRadius: 100,
        height: '100%', paddingHorizontal: 10
    },
    eyeContainer: {
        width: screenWidth / 10,
        alignItems: 'center',
        justifyContent: 'center',

    },
    eye: {
        width: '100%',
        resizeMode: 'contain',
        tintColor: MediumGrey,
    },
    loginOptionContainer: { width: screenWidth / 4, height: screenHeight / 18, backgroundColor: WhiteGery, borderRadius: 10, alignItems: 'center', justifyContent: 'center' },
    loginOptionImage: { width: '60%', height: '60%', resizeMode: 'contain' },
    star: { fontSize: screenWidth / 22, color: Red, fontFamily: appFontBold, marginStart: 5, },
    icon: {
        width: '5%',
        height: '40%',
        tintColor: DarkGrey,
        resizeMode: 'contain',
        marginHorizontal: '7%',
    },
});

export default Registration;
