import React, { Component, useEffect, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    PermissionsAndroid,
    SectionList,
    ActivityIndicator,
    RefreshControl,
    FlatList,
    BackHandler,
    Alert,
    Linking,
    Platform
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    MediumGreen,
    appColor1,
    Grey,
    DarkYellow,
    DarkBlue,
    Gold,
} from '../components/Styles';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import FooterDriver from '../components/FooterDriver';
import Toaster from '../components/Toaster';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import RequestContainer from '../components/RequestContainer';
import * as driverDailyTripsActions from '../../Store/Actions/driverDailyTrips';
import * as driverPackagesActions from '../../Store/Actions/driverPackages';
import * as driverVipTripsActions from '../../Store/Actions/driverVipTrips';
import * as driverCityTripsActions from '../../Store/Actions/driverCityTrips';
import * as driverProfileActions from '../../Store/Actions/driverProfile';
import * as authActions from '../../Store/Actions/auth';
import * as generalActions from '../../Store/Actions/general';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import DriverDailyTrip from '../components/DriverDailyTrip';
import DriverPackageTrip from '../components/DriverPackageTrip';
import Geolocation from 'react-native-geolocation-service';
import Geocoder from 'react-native-geocoding';
import VipTrip from '../components/VipTrip';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Loading from '../components/Loading';
import Sound from 'react-native-sound';
import { Button } from 'native-base';
// import { NativeModules } from 'react-native';
// const { NotificationHelper } = NativeModules;


export const CLEARNEWDAILYTRIP = 'CLEARNEWDAILYTRIP';
export const CLEARUPDATEDDAILYTRIP = 'CLEARUPDATEDDAILYTRIP';
export const CLEARDAILYTRIPCANCELLED = 'CLEARDAILYTRIPCANCELLED';
export const CLEARUPDATEDCITYTRIPDRIVER1 = 'CLEARUPDATEDCITYTRIPDRIVER1';
export const CLEARUPDATEDVIPTRIPDRIVER1 = 'CLEARUPDATEDVIPTRIPDRIVER1';

const ASPECT_RATIO = screenWidth / screenHeight

Geocoder.init("AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I"); // use a valid API key

const DriverRequests = props => {

    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [loadingDailyTrips, setLoadingDailyTrips] = useState(false);
    const [loadingPackagesTrips, setLoadingPackagesTrips] = useState(false);
    const [loadingTripsVip, setLoadingTripsVip] = useState(false);
    const [loadingVipTrips, setLoadingVipTrips] = useState(false);
    const [loadingBetweenCitiesTrips, setLoadingBetweenCitiesTrips] = useState(false);
    const [name, setName] = useState('');
    const [image, setImage] = useState('');


    const [Reload, setReload] = useState(false);

    const [connectedStatus, setConnectedStatus] = useState(false);
    const [connected, setConnected] = useState([
        {
            status: true,
            name: strings('lang.connected'),
        },
        {
            status: false,
            name: strings('lang.notConnected'),
        },
    ]);

    const [categories, setcategories] = useState([
        {
            id: 1,
            name: strings('lang.Dailyrides')
        },
        {
            id: 2,
            name: strings('lang.packages')
        },
        {
            id: 3,
            name: strings('lang.VIPrequests')
        },
        {
            id: 4,
            name: strings('lang.betweencities')
        },
    ]);
    const [categoryId, setCategoryId] = useState(1);

    const [dailyTrips, setDailyTrips] = useState([]);
    const [noResault, setNoResault] = useState('');
    const [page, setPage] = useState(0);
    const [lastPage, setLastPage] = useState(0);
    const [user, setUser] = useState({});

    const [packagesTrips, setPackagesTrips] = useState([]);
    const [noResault1, setNoResault1] = useState('');
    const [page1, setPage1] = useState(0);
    const [lastPage1, setLastPage1] = useState(0);

    const [vipRequests, setVipRequests] = useState([]);
    const [vipTrips, setVipTrips] = useState([]);
    const [noResault2, setNoResault2] = useState('');
    const [page2, setPage2] = useState(0);
    const [lastPage2, setLastPage2] = useState(0);

    const [betweenCitiesTrips, setBetweenCitiesTrips] = useState([]);
    const [noResault3, setNoResault3] = useState('');
    const [page3, setPage3] = useState(0);
    const [lastPage3, setLastPage3] = useState(0);


    const [region, SetRegion] = useState({
        latitude: 0,
        longitude: 0,
        longitudeDelta: 0.01 * ASPECT_RATIO,
        latitudeDelta: 0.01
    })
    const [lat, SetLat] = useState(0)
    const [lng, SetLng] = useState(0)
    const [addressDesription, setAddressDesription] = useState('');

    const [profile, setProfile] = useState({});
    const is_live = useSelector(state => state.general.is_live);

    const dispatch = useDispatch();
    const isFocused = useIsFocused();

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    const trips = useSelector((state) => state.trips);

    // const sound = new Sound('notificationsound.mp3', Sound.MAIN_BUNDLE, (error) => {
    //     if (error) {
    //         console.error('Failed to load the sound', error);
    //         return;
    //     }
    // });

    const handleBackButton = () => {
        BackHandler.exitApp();
        return true;
    };

    // requestPermission()
    //     .then(() => console.log("Permission Granted"))
    //     .catch(() => console.log("Permission is not granted"))

    // // Initialize bubble manage
    // initialize()
    //     .then(() => console.log("Initialized the bubble mange"))


    useEffect(() => {
        BackHandler.addEventListener('hardwareBackPress', handleBackButton);

        return () => {
            BackHandler.removeEventListener('hardwareBackPress', handleBackButton);
        };
    }, []);

    useEffect(() => {
        // setTimeout(() => {
        //     if (NotificationHelper && NotificationHelper.clearAllNotifications) {
        //         NotificationHelper.clearAllNotifications();
        //         console.log('NotificationHelper', NotificationHelper.clearAllNotifications());
        //     }
        // }, 500);
        if (trips.updatedDailyTrip.daily_trip) {
            let sound = new Sound('notificationsound.mp3', Sound.MAIN_BUNDLE, (error) => {
                if (error) {
                    console.log('failed to load the sound', error);
                } else {
                    sound.play(); // have to put the call to play() in the onload callback
                }
            });
            let updated = trips.updatedDailyTrip.daily_trip
            sound.play((success) => {
                if (success) {
                    console.log('Sound played successfully');
                } else {
                    console.error('Failed to play the sound');
                }
            });

            // Alert.alert(
            //     'Confirmation',
            //     'Do you want to proceed?',
            //     [
            //         { text: 'Cancel', onPress: () => console.log('Cancel Pressed'), },
            //         { text: 'OK', onPress: () => console.log('OK Pressed') },
            //     ],
            // );

            const Dailytrip = dailyTrips.map(obj => {
                if (obj.id === updated.id) {
                    console.log('mostafa');
                    return { ...obj, original_price: updated.original_price };
                }
                dispatch({ type: CLEARUPDATEDDAILYTRIP });
                return obj;

            });

            console.log(Dailytrip);
            setDailyTrips(Dailytrip)
            dispatch({ type: CLEARUPDATEDDAILYTRIP });
        }

        console.log('tripsio', trips);

        if (trips.dailyTrip.daily_trip) {
            let sound = new Sound('notificationsound.mp3', Sound.MAIN_BUNDLE, (error) => {
                if (error) {
                    console.log('failed to load the sound', error);
                } else {
                    sound.play(); // have to put the call to play() in the onload callback
                }
            });


            // console.log('new');
            setDailyTrips([...[trips.dailyTrip.daily_trip], ...dailyTrips])
            console.log('DailyTrips', [...[trips.dailyTrip.daily_trip], ...dailyTrips]);
            // setTimeout(() => {
            props.navigation.navigate('MapDriver', { request: 1, trip: trips.dailyTrip.daily_trip });
            // }, 1000);
            setNoResault('');
            dispatch({ type: CLEARNEWDAILYTRIP });
            sound.play((success) => {
                if (success) {
                    console.log('Sound played successfully');
                } else {
                    console.error('Failed to play the sound');
                }
            });

            if (trips.dailyTrip.daily_trip.status == "finished") {
                dispatch({ type: CLEARUPDATEDDAILYTRIP });
            }
        }

        if (trips.dailyTripCancelled.daily_trip) {
            // if (trips.dailyTripCancelled.daily_trip.id == trips.updatedDailyTrip.daily_trip.id) {
            if (trips.dailyTripCancelled.daily_trip.status == "cancelled") {
                const getDailyTrips = async () => {
                    // setLoadingMore(true)
                    try {
                        let response = await dispatch(driverDailyTripsActions.getDailyTrips());
                        if (response.success == true) {
                            if (response.data.length == 0) {
                                setNoResault(strings('lang.No_Results'));
                                setDailyTrips([])
                            } else {
                                setNoResault('');
                                setDailyTrips(response.data)
                                setLastPage(response.data.last_page);
                                setPage(1)
                            }
                            // setLoadingMore(false);
                        }
                        else {
                            if (response.message) {
                                Toaster(
                                    'top',
                                    'danger',
                                    Red,
                                    response.message,
                                    White,
                                    1500,
                                    screenHeight / 15,
                                );
                            }
                        }
                        // setLoadingMore(false);
                    }
                    catch (err) {
                        console.log('err', err)
                        // setLoadingMore(false);
                    }
                };
                getDailyTrips();
                dispatch({ type: CLEARDAILYTRIPCANCELLED });
            }
            // }
        }

        if (trips.updatedDailyTrip.daily_trip) {
            // if (trips.updatedDailyTrip.daily_trip.id == trips.dailyTripCancelled.daily_trip.id) {
            if (trips.updatedDailyTrip.daily_trip.status == "cancelled") {
                const getDailyTrips = async () => {
                    setLoading(true)
                    try {
                        let response = await dispatch(driverDailyTripsActions.getDailyTrips());
                        if (response.success == true) {
                            if (response.data.length == 0) {
                                setNoResault(strings('lang.No_Results'));
                                setDailyTrips([])
                            } else {
                                setNoResault('');
                                setDailyTrips(response.data)
                                setLastPage(response.data.last_page);
                                setPage(1)
                            }
                            setLoading(false);
                        }
                        else {
                            if (response.message) {
                                Toaster(
                                    'top',
                                    'danger',
                                    Red,
                                    response.message,
                                    White,
                                    1500,
                                    screenHeight / 15,
                                );
                            }
                        }
                        setLoading(false);
                    }
                    catch (err) {
                        console.log('err', err)
                        setLoading(false);
                    }
                };
                getDailyTrips();
                dispatch({ type: CLEARUPDATEDDAILYTRIP });
            }
            // }
        }
        if (trips.updatedVipTrip.vip_trip) {
            if (trips.updatedVipTrip.vip_trip.status == "finished") {
                dispatch({ type: CLEARUPDATEDVIPTRIPDRIVER1 });
                const getVipTrips = async () => {
                    // setLoading(true)
                    try {
                        let response = await dispatch(driverVipTripsActions.getVipTrips());
                        if (response.success == true) {
                            if (response.data.length == 0) {
                                setNoResault2(strings('lang.No_Results'));
                                setVipTrips([])
                            } else {
                                setNoResault2('');
                                setVipTrips(response.data)
                                setLastPage2(response.data.last_page);
                                setPage2(1)
                            }
                        }
                        else {
                            if (response.message) {
                                Toaster(
                                    'top',
                                    'danger',
                                    Red,
                                    response.message,
                                    White,
                                    1500,
                                    screenHeight / 15,
                                );
                            }
                        }
                        // setLoading(false);
                    }
                    catch (err) {
                        console.log('err', err)
                        // setLoading(false);
                    }
                };
                getVipTrips()
                console.log('new');
            }
        }
        if (trips.updatedCityTrip.city_trip) {
            if (trips.updatedCityTrip.city_trip.status == "finished") {
                dispatch({ type: CLEARUPDATEDCITYTRIPDRIVER1 });
                const getCityTrips = async () => {
                    // setLoading(true)
                    try {
                        let response = await dispatch(driverCityTripsActions.getCityTrips());
                        if (response.success == true) {
                            if (response.data.length == 0) {
                                setNoResault3(strings('lang.No_Results'));
                                setBetweenCitiesTrips([])
                            } else {
                                setNoResault3('');
                                setBetweenCitiesTrips(response.data)
                                setLastPage3(response.data.last_page);
                                setPage3(1)
                            }
                        }
                        else {
                            if (response.message) {
                                Toaster(
                                    'top',
                                    'danger',
                                    Red,
                                    response.message,
                                    White,
                                    1500,
                                    screenHeight / 15,
                                );
                            }
                        }
                        // setLoading(false);
                    }
                    catch (err) {
                        console.log('err', err)
                        // setLoading(false);
                    }
                };
                getCityTrips()
                console.log('new');
            }
        }
    }, [trips]);

    useEffect(() => {
        // setLoading(true);




        const RequestIosPermissio = () => {
            Geolocation.requestAuthorization('always').then((res) => {
                // console.log('res', res);
                if (res == 'denied') {
                    //  Alert.alert("Please allow location to continuo")/
                    // Linking.openURL('app-settings:');
                    // props.navigation.goBack()
                    showCustomAlert()
                }
            });
            // setLoading(false)
        }
        const showCustomAlert = () => {
            Alert.alert(
                'Permission Alert',
                'The app will not work properly without this permission ',
                [
                    {
                        text: 'Access permission from settings',
                        onPress: () => {
                            console.log('start permission');
                            Linking.openURL('app-settings:');
                            // get_Lat_User()
                        },
                    },
                    {
                        text: 'Cancel',
                        onPress: () => {
                            console.log('exit');
                            // props.navigation.navigate('Home')
                        },
                    },
                ],
                { cancelable: false }
            );
        };

        const get_Lat_User = async () => {
            setLoading(true)
            if (Platform.OS == 'ios') {
                RequestIosPermissio()
            }
            Geolocation.getCurrentPosition(
                async (position) => {
                    // console.log('position', position);
                    var lat = parseFloat(position.coords.latitude)
                    var longi = parseFloat(position.coords.longitude)
                    var initialRegion = {
                        latitude: lat,
                        longitude: longi,
                        longitudeDelta: 0.01 * ASPECT_RATIO,
                        latitudeDelta: 0.01
                    }
                    SetRegion(initialRegion)
                    SetLat(lat)
                    SetLng(longi)
                    Geocoder.init("AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I");
                    Geocoder.from(lat, longi)
                        .then(json => {
                            var addressComponent = json.results[0].formatted_address;
                            // console.log(addressComponent);
                            setAddressDesription(addressComponent)
                        })
                        .catch(error => console.log('error', error));
                    try {
                        let response = await dispatch(authActions.updateDriverLocation(lat, longi));
                    }
                    catch (err) {
                        console.log('err', err)
                        setLoading(false);
                    }
                    getDailyTrips();
                    getPackagesTrips();
                    getVipTrips();
                    getCityTrips();
                    getProfile();
                    setLoading(false)
                },
                (error) => {
                    console.log('error', error);
                    setLoading(false)

                },
                { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 1000 },
            );
            setLoading(false)
        }

        const requestLocationPermission = async () => {
            try {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                    {
                        'title': 'Location Permission',
                        'message': 'MyMapApp needs access to your location'
                    }
                )

                if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                    get_Lat_User()
                    // Alert.alert("Please allow location")
                } else {
                    // console.log("Location permission denied")
                    setLoading(false)

                }
            } catch (err) {
                console.warn(err)
                setLoading(false)
            }
        }

        if (Platform.OS == 'ios') {
            get_Lat_User()
        }
        else {
            requestLocationPermission();
        }

        const getDailyTrips = async () => {
            setLoading(true)
            try {
                let response = await dispatch(driverDailyTripsActions.getDailyTrips());
                if (response.success == true) {
                    if (response.data.length == 0) {
                        setNoResault(strings('lang.No_Results'));
                        setDailyTrips([])
                    } else {
                        setNoResault('');
                        setDailyTrips(response.data)
                        // props.navigation.navigate('MapDriver', { request: 1, trip: response.data[0] });
                        setLastPage(response.data.last_page);
                        setPage(1)
                    }
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getPackagesTrips = async () => {
            setLoading(true)
            try {
                let response = await dispatch(driverPackagesActions.getPackageTripe());
                if (response.success == true) {

                    let i = 0;
                    let array = []
                    for (let item of response.data) {
                        if (item.trips.length != 0) {
                            array = [...array, ...[{
                                'key': i,
                                'data': item.trips,
                                // 'day': item.day,
                                'day_name': item.day_name,
                                'date': item.date,
                            }]]
                            i++
                        }
                    }
                    if (array.length == 0) {
                        setNoResault1(strings('lang.No_Results'));
                        setPackagesTrips([])
                    }
                    else {
                        setNoResault1('');
                        setPackagesTrips(array)
                        setLastPage1(response.data.last_page);
                        setPage1(1)
                    }
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getVipTrips = async () => {
            setLoading(true)
            try {
                let response = await dispatch(driverVipTripsActions.getVipTrips());
                if (response.success == true) {
                    if (response.data.length == 0) {
                        setNoResault2(strings('lang.No_Results'));
                        setVipTrips([])
                    } else {
                        setNoResault2('');
                        setVipTrips(response.data)
                        setLastPage2(response.data.last_page);
                        setPage2(1)
                    }
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getCityTrips = async () => {
            setLoading(true)
            try {
                let response = await dispatch(driverCityTripsActions.getCityTrips());
                if (response.success == true) {
                    if (response.data.length == 0) {
                        setNoResault3(strings('lang.No_Results'));
                        setBetweenCitiesTrips([])
                    } else {
                        setNoResault3('');
                        setBetweenCitiesTrips(response.data)
                        setLastPage3(response.data.last_page);
                        setPage3(1)
                    }
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getProfile = async () => {
            setLoading(true)
            try {
                let response = await dispatch(driverProfileActions.getProfile());
                if (response.success == true) {
                    setProfile(response.data)
                    setConnectedStatus(response.data.is_connected)
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                // setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                // setLoading(false);
            }
            let name = await AsyncStorage.getItem('name')
            let image = await AsyncStorage.getItem('image')
            setName(name)
            setImage(image)
        };

        const getUser = async () => {
            setLoading(true)
            try {
                let response = await dispatch(generalActions.getUser());
                if (response.success == true) {
                    setUser(response.data)
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getUser()
        // setLoading(false);

    }, [Reload, isFocused]);

    // dailyTrips
    const onRefresh = async () => {
        setLoading(true)
        try {
            let response = await dispatch(driverDailyTripsActions.getDailyTrips());
            if (response.success == true) {
                if (response.data.length == 0) {
                    setNoResault(strings('lang.No_Results'));
                    setDailyTrips([])
                } else {
                    setNoResault('');
                    setDailyTrips(response.data)
                    setLastPage(response.data.last_page);
                    setPage(1)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const LoadMore = async () => {
        console.log('page', page);
        console.log('lastpage', lastPage);
        if (page < lastPage) {
            setLoadingTripsVip(true)
            try {
                let response = await dispatch(vipTripsActions.getTrips());
                if (response.success == true) {
                    setTripsOrdersVip(response.data.items)
                    setPage(page + 1);
                    setNoResault('')
                    setLoadingTripsVip(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingTripsVip(false);
                }
            } catch (err) {
                setLoadingTripsVip(false);
            }

        }
        else {
        }
    }

    // packageTrips
    const onRefreshPackage = async () => {
        setLoading(true)
        try {
            let response = await dispatch(driverPackagesActions.getPackageTripe());
            if (response.success == true) {

                let i = 0;
                let array = []
                for (let item of response.data) {
                    if (item.trips.length != 0) {
                        array = [...array, ...[{
                            'key': i,
                            'data': item.trips,
                            // 'day': item.day,
                            'day_name': item.day_name,
                            'date': item.date,
                        }]]
                        i++
                    }
                }
                if (array.length == 0) {
                    setNoResault1(strings('lang.No_Results'));
                    setPackagesTrips([])
                }
                else {
                    setNoResault1('');
                    setPackagesTrips(array)
                    setLastPage1(response.data.last_page);
                    setPage1(1)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const LoadMorePackage = async () => {
        console.log('page', page);
        console.log('lastpage', lastPage);
        if (page < lastPage) {
            setLoadingTripsVip(true)
            try {
                let response = await dispatch(vipTripsActions.getTrips());
                if (response.success == true) {
                    setTripsOrdersVip(response.data.items)
                    setPage(page + 1);
                    setNoResault('')
                    setLoadingTripsVip(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingTripsVip(false);
                }
            } catch (err) {
                setLoadingTripsVip(false);
            }

        }
        else {
        }
    }


    // vipTrips
    const onRefreshVip = async () => {
        setLoading(true)
        try {
            let response = await dispatch(driverVipTripsActions.getVipTrips());
            if (response.success == true) {
                if (response.data.length == 0) {
                    setNoResault2(strings('lang.No_Results'));
                    setVipTrips([])
                } else {
                    setNoResault2('');
                    setVipTrips(response.data)
                    setLastPage2(response.data.last_page);
                    setPage2(1)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const LoadMoreVip = async () => {
        console.log('page', page);
        console.log('lastpage', lastPage);
        if (page < lastPage) {
            setLoadingTripsVip(true)
            try {
                let response = await dispatch(vipTripsActions.getTrips());
                if (response.success == true) {
                    setTripsOrdersVip(response.data.items)
                    setPage(page + 1);
                    setNoResault('')
                    setLoadingTripsVip(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingTripsVip(false);
                }
            } catch (err) {
                setLoadingTripsVip(false);
            }

        }
        else {
        }
    }


    // citiesTrips
    const onRefreshCities = async () => {
        setLoading(true)
        try {
            let response = await dispatch(driverCityTripsActions.getCityTrips());
            if (response.success == true) {
                if (response.data.length == 0) {
                    setNoResault3(strings('lang.No_Results'));
                    setBetweenCitiesTrips([])
                } else {
                    setNoResault3('');
                    setBetweenCitiesTrips(response.data)
                    setLastPage3(response.data.last_page);
                    setPage3(1)
                }
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        }
        catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const LoadMoreCities = async () => {
        console.log('page', page);
        console.log('lastpage', lastPage);
        if (page < lastPage) {
            setLoadingTripsVip(true)
            try {
                let response = await dispatch(vipTripsActions.getTrips());
                if (response.success == true) {
                    setTripsOrdersVip(response.data.items)
                    setPage(page + 1);
                    setNoResault('')
                    setLoadingTripsVip(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingTripsVip(false);
                }
            } catch (err) {
                setLoadingTripsVip(false);
            }

        }
        else {
        }
    }


    const updateConnectedStatus = async (connectedStatus) => {
        setLoadingMore(true)
        try {
            let response = await dispatch(driverProfileActions.updateConnectedStatus(connectedStatus));
            if (response.success == true) {
                setConnectedStatus(response.data.is_connected)
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false);
        }
        catch (err) {
            console.log('err', err)
            setLoadingMore(false);
        }
    };

    if (!is_live) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White, justifyContent: 'center' }}>
                <Text style={{ fontFamily: appFontBold, color: Red, fontSize: screenWidth / 16 }}>{strings('lang.message22')}</Text>
                <Image source={require('../images/soonn.gif')} style={{ height: screenHeight / 4, resizeMode: 'contain', marginTop: 10 }} />

                {Platform.OS != 'ios'
                    ?
                    <Button
                        onPress={() => {
                            if (Platform.OS == 'ios') {
                            }
                            else {
                                handleBackButton()

                            }
                        }}
                        transparent style={{ marginTop: 20, borderRadius: 10, alignSelf: 'center', width: '90%', height: screenHeight / 18, backgroundColor: DarkBlue, justifyContent: "center", alignItems: "center" }}>
                        <Text style={{ fontFamily: appFontBold, color: White, fontSize: screenWidth / 30 }}>{strings('lang.CloseApp')}</Text>
                    </Button>
                    :
                    <></>
                }

            </View>
        );
    }
    else if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White, }}>
                <View style={{ width: '100%', height: screenHeight / 10, flexDirection: 'row', paddingEnd: '2.5%', marginTop: screenHeight / 25, alignItems: 'center' }}>
                    <View style={{ width: '20%', height: '80%', alignItems: 'center', justifyContent: 'center', }}>
                        <View style={{ width: screenHeight / 18, height: screenHeight / 18, borderRadius: screenHeight / 36, alignItems: 'center', justifyContent: 'center', backgroundColor: DarkBlue }}>
                            <Image source={require('../images/Group-97.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain' }} />
                        </View>
                    </View>
                    <View style={{ width: '40%', height: '80%', alignItems: 'center', justifyContent: 'center', }}>
                        <Text style={{ alignSelf: 'flex-start', fontFamily: appFontBold, fontSize: screenWidth / 30, color: Gold }}>{strings('lang.hello')}</Text>
                        <Text style={{ alignSelf: 'flex-start', fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black }}></Text>
                    </View>
                    <View style={styles.connectedContainer}>
                        {connected.map((item, index) => {
                            return (
                                <TouchableOpacity
                                    onPress={() => { setConnectedStatus(item.status) }}
                                    style={[styles.connected, { backgroundColor: item.status == connectedStatus ? Gold : White }]}>
                                    <Text style={[styles.connectedText, { color: item.status == connectedStatus ? White : Black }]}>{item.name}</Text>
                                </TouchableOpacity>
                            )
                        })}
                    </View>
                </View>
                <View style={{ height: screenHeight / 20, width: '95%', flexDirection: 'row', justifyContent: 'space-evenly', backgroundColor: WhiteGery, marginBottom: categoryId == 2 ? null : '2%' }}>
                    {categories.map((item, index) => {
                        return (
                            <Pressable
                                onPress={() => { setCategoryId(item.id) }}
                                style={item.id == categoryId ? styles.activeButton : styles.Button}
                            >
                                <Text style={item.id == categoryId ? styles.activeLabel : styles.label}>{item.name}</Text>
                                {item.id == 1 ?
                                    dailyTrips.length == 0 ?
                                        <></>
                                        :
                                        <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center', display: 'none' }}>
                                        </View>
                                    :
                                    <></>
                                }
                                {item.id == 2 ?
                                    packagesTrips.length == 0 ?
                                        <></>
                                        :
                                        <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: White }}>{packagesTrips.length}</Text>
                                        </View>
                                    :
                                    <></>
                                }
                                {item.id == 3 ?
                                    vipTrips.length == 0 ?
                                        <></>
                                        :
                                        <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center' }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: White }}>{vipTrips.length}</Text>
                                        </View>
                                    :
                                    <></>
                                }
                            </Pressable>
                        )
                    })
                    }
                </View>
                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    {[{}, {}, {}, {}, {}].map((item) => {
                        return <View style={{ paddingTop: screenHeight / 50 }}>
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 40,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '5%',
                                    alignSelf: "flex-start",
                                    marginRight: "3%",
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>

                            <View
                                style={{
                                    width: '80%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '3%',
                                    alignSelf: "center"
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>

                            <View
                                style={{
                                    width: '30%',
                                    height: screenHeight / 50,
                                    backgroundColor: WhiteGery,
                                    marginBottom: '3%',
                                    alignSelf: "flex-start",
                                    //  marginTop:"3%",
                                    marginVertical: "3%"
                                    //  borderRadius:'5%'
                                }}
                            >
                                <SkeletonPlaceholder
                                    highlightColor={MediumGrey}
                                    backgroundColor={WhiteGery}
                                    speed={1200}
                                >
                                    <View
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: WhiteGery,
                                        }}
                                    />
                                </SkeletonPlaceholder>
                            </View>


                        </View>
                    })}

                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>

                {loadingMore || loading
                    ?
                    <Loading />
                    :
                    <></>
                }
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    refreshControl={
                        <RefreshControl refreshing={Reload} onRefresh={() => {
                            onRefresh();
                            onRefreshPackage();
                            onRefreshVip();
                            onRefreshCities();
                        }} />
                    }
                >

                    <View style={{ width: '100%', height: screenHeight / 10, flexDirection: 'row', paddingEnd: '2.5%', marginTop: screenHeight / 25, alignItems: 'center' }}>
                        <View style={{ width: '20%', height: '80%', alignItems: 'center', justifyContent: 'center', }}>
                            <View style={{ width: screenHeight / 18, height: screenHeight / 18, borderRadius: screenHeight / 36, alignItems: 'center', justifyContent: 'center', backgroundColor: DarkBlue }}>
                                <Image source={image ? { uri: image } : require('../images/Group-97.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', borderRadius: 100 }} />
                            </View>
                        </View>
                        <View style={{ width: '40%', height: '80%', alignItems: 'center', justifyContent: 'center', }}>
                            <Text style={{ alignSelf: 'flex-start', fontFamily: appFontBold, fontSize: screenWidth / 30, color: Gold }}>{strings('lang.hello')}</Text>
                            <Text style={{ alignSelf: 'flex-start', fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, textAlign: 'left' }}>{name}</Text>
                        </View>
                        <View style={styles.connectedContainer}>
                            {connected.map((item, index) => {
                                return (
                                    <TouchableOpacity
                                        onPress={() => { updateConnectedStatus(!connectedStatus) }}
                                        style={[styles.connected, { backgroundColor: item.status == connectedStatus ? Gold : White }]}>
                                        <Text style={[styles.connectedText, { color: item.status == connectedStatus ? White : Black }]}>{item.name}</Text>
                                    </TouchableOpacity>
                                )
                            })}
                        </View>
                    </View>

                    <View style={{ height: screenHeight / 20, width: '100%', flexDirection: 'row', justifyContent: 'space-evenly', backgroundColor: WhiteGery, marginBottom: categoryId == 2 ? null : '2%', paddingHorizontal: '5%' }}>
                        {categories.map((item, index) => {
                            return (
                                <Pressable
                                    onPress={() => { setCategoryId(item.id) }}
                                    style={item.id == categoryId ? styles.activeButton : styles.Button}
                                >
                                    <Text style={item.id == categoryId ? styles.activeLabel : styles.label}>{item.name}</Text>
                                    {item.id == 1 ?
                                        dailyTrips.length == 0 ?
                                            <></>
                                            :
                                            <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center' }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: White }}>{dailyTrips.length}</Text>
                                            </View>
                                        :
                                        <></>
                                    }
                                    {item.id == 2 ?
                                        packagesTrips.length == 0 ?
                                            <></>
                                            :
                                            <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center' }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: White }}>{packagesTrips.length}</Text>
                                            </View>
                                        :
                                        <></>
                                    }
                                    {item.id == 3 ?
                                        vipTrips.length == 0 ?
                                            <></>
                                            :
                                            <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center' }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: White }}>{vipTrips.length}</Text>
                                            </View>
                                        :
                                        <></>
                                    }
                                    {item.id == 4 ?
                                        betweenCitiesTrips.length == 0 ?
                                            <></>
                                            :
                                            <View style={{ width: screenWidth / 20, height: screenWidth / 20, overflow: 'hidden', borderRadius: screenWidth / 40, backgroundColor: 'red', marginStart: 3, alignItems: 'center', justifyContent: 'center' }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 40, color: White }}>{betweenCitiesTrips.length}</Text>
                                            </View>
                                        :
                                        <></>
                                    }
                                </Pressable>
                            )
                        })
                        }
                    </View>
                    <View style={{ height: screenHeight / 50 }}></View>

                    {/* {connectedStatus == true ? */}

                    <>
                        {categoryId == 1 &&
                            <>
                                {
                                    noResault ? (
                                        <ScrollView style={{}}>
                                            <View style={{ width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center' }}>
                                                <Image source={require('../images/Group10500.png')} style={{ width: '100%', height: screenHeight / 5, resizeMode: 'contain', marginTop: screenHeight / 8 }} />

                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 24, color: Black, textAlign: 'center', marginVertical: screenHeight / 80 }}>{strings('lang.message10')}</Text>

                                                <Image source={require('../images/loading.png')} style={{ width: '100%', height: screenHeight / 20, resizeMode: 'contain' }} />
                                            </View>
                                        </ScrollView>
                                    ) : (
                                        <View></View>
                                    )
                                }

                                <FlatList
                                    data={dailyTrips}
                                    // refreshControl={
                                    //     <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                                    // }
                                    showsVerticalScrollIndicator={false}
                                    renderItem={({ item, key }) =>
                                        <DriverDailyTrip tripPress={() => {
                                            if (user.temp_stop == true) {
                                                Toaster(
                                                    'top',
                                                    'danger',
                                                    Red,
                                                    strings('lang.Youraccountistemporarilysuspended'),
                                                    White,
                                                    1500,
                                                    screenHeight / 15,
                                                );
                                            } else {
                                                props.navigation.navigate('MapDriver', { request: 1, trip: item })
                                            }
                                        }} item={item} driverLocation={addressDesription} />
                                    }
                                    // keyExtractor={item => item.id}
                                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth / 1.05, }}
                                />
                            </>
                        }

                        {categoryId == 2 &&
                            <>
                                {
                                    noResault1 ? (
                                        <ScrollView style={{}}>
                                            <View style={{ width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center' }}>
                                                <Image source={require('../images/Group10500.png')} style={{ width: '100%', height: screenHeight / 5, resizeMode: 'contain', marginTop: screenHeight / 8 }} />

                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 24, color: Black, textAlign: 'center', marginVertical: screenHeight / 80 }}>{strings('lang.message10')}</Text>

                                                <Image source={require('../images/loading.png')} style={{ width: '100%', height: screenHeight / 20, resizeMode: 'contain' }} />
                                            </View>
                                        </ScrollView>
                                    ) : (
                                        <View></View>
                                    )
                                }
                                <SectionList
                                    sections={packagesTrips}
                                    // refreshControl={
                                    //     <RefreshControl refreshing={Reload} onRefresh={onRefreshPackage} />
                                    // }
                                    showsVerticalScrollIndicator={false}
                                    keyExtractor={(item, index) => item + index}
                                    renderSectionHeader={({ section: { day_name, date } }) => (
                                        <View style={{ width: '95%', marginVertical: screenHeight / 100, alignSelf: 'center', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                <Image source={require('../images/Group10477.png')} style={{ height: screenHeight / 45, width: '20%', resizeMode: 'contain', marginEnd: '5%' }} />
                                                <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black }}>{day_name}</Text>
                                            </View>
                                            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black }}>{date}</Text>
                                        </View>
                                    )}
                                    renderItem={({ item }) =>
                                        <DriverPackageTrip tripPress={() => { props.navigation.navigate('MapDriver', { request: 2, trip: item }) }} item={item} />

                                    }
                                    stickySectionHeadersEnabled={false}
                                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth / 1.05, backgroundColor: White, }}
                                />
                            </>
                        }


                        {categoryId == 3 &&
                            <>
                                {
                                    noResault2 ? (
                                        <ScrollView style={{}}>
                                            <View style={{ width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center' }}>
                                                <Image source={require('../images/Group10500.png')} style={{ width: '100%', height: screenHeight / 5, resizeMode: 'contain', marginTop: screenHeight / 8 }} />

                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 24, color: Black, textAlign: 'center', marginVertical: screenHeight / 80 }}>{strings('lang.message10')}</Text>

                                                <Image source={require('../images/loading.png')} style={{ width: '100%', height: screenHeight / 20, resizeMode: 'contain' }} />
                                            </View>
                                        </ScrollView>
                                    ) : (
                                        <View></View>
                                    )
                                }
                                <FlatList
                                    data={vipTrips}
                                    // refreshControl={
                                    //     <RefreshControl refreshing={Reload} onRefresh={onRefreshVip} />
                                    // }
                                    showsVerticalScrollIndicator={false}
                                    renderItem={({ item, key }) =>
                                        <VipTrip tripPress={() => { props.navigation.navigate('MapDriver', { request: 3, trip: item }) }} item={item} noLabel />
                                    }
                                    // keyExtractor={item => item.id}
                                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth / 1.05, backgroundColor: White, }}
                                />
                            </>
                        }

                        {categoryId == 4 &&
                            <>
                                {
                                    noResault3 ? (
                                        <ScrollView style={{}}>
                                            <View style={{ width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center' }}>
                                                <Image source={require('../images/Group10500.png')} style={{ width: '100%', height: screenHeight / 5, resizeMode: 'contain', marginTop: screenHeight / 8 }} />

                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 24, color: Black, textAlign: 'center', marginVertical: screenHeight / 80 }}>{strings('lang.message10')}</Text>

                                                <Image source={require('../images/loading.png')} style={{ width: '100%', height: screenHeight / 20, resizeMode: 'contain' }} />
                                            </View>
                                        </ScrollView>
                                    ) : (
                                        <View></View>
                                    )
                                }
                                <FlatList
                                    data={betweenCitiesTrips}
                                    // refreshControl={
                                    //     <RefreshControl refreshing={Reload} onRefresh={onRefreshCities} />
                                    // }
                                    showsVerticalScrollIndicator={false}
                                    renderItem={({ item, key }) =>
                                        <RequestContainer item={item} mapPress={() => { props.navigation.navigate('MapDriver', { request: 4, trip: item }) }} noLabel />
                                    }
                                    // keyExtractor={item => item.id}
                                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth / 1.05, backgroundColor: White, }}
                                />
                            </>
                        }
                    </>


                    {/* :
                <ScrollView style={{}}>
                    <View style={{ width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center' }}>
                        <Image source={require('../images/Group10500.png')} style={{ width: '100%', height: screenHeight / 5, resizeMode: 'contain', marginTop: screenHeight / 8 }} />

                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 24, color: Black, textAlign: 'center', marginVertical: screenHeight / 80 }}>{strings('lang.message10')}</Text>

                        <Image source={require('../images/loading.png')} style={{ width: '100%', height: screenHeight / 20, resizeMode: 'contain' }} />
                    </View>
                </ScrollView>

            } */}

                    {/* </View> */}
                </ScrollView>

                <View style={{ height: 10 }}></View>

                <FooterDriver navigation={props.navigation} current={'DriverRequests'} />
            </View>
        );
    }
};

const styles = StyleSheet.create({
    connectedContainer: {
        flexDirection: 'row',
        alignItems: "center",
        justifyContent: 'center',
        alignSelf: 'center',
        width: '40%', height: screenHeight / 25,
        borderWidth: 1,
        borderColor: DarkYellow,
        borderRadius: 100,
        marginVertical: '10%', overflow: 'hidden'
    },
    connected: {
        width: '50%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center', borderRadius: 100,
    },

    connectedText: {
        fontSize: screenWidth / 38,
        fontFamily: appFont,
        // alignSelf: 'flex-start',
        // marginHorizontal: '2.5%',
        color: Black,
    },
    star: {
        fontFamily: appFont,
        fontSize: screenWidth / 20,
        color: Red
    },
    input: {
        height: 45, borderWidth: 1, borderRadius: 5, borderColor: MediumGrey, flexDirection: 'row', justifyContent: 'center',
        width: "100%", fontSize: screenWidth / 28, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left'
    },

    loginError: {
        color: Red, fontFamily: appFont, alignSelf: 'flex-start', fontSize: screenWidth / 33
    },
    textImg: {
        fontSize: screenWidth / 30,
        fontFamily: appFont,
        color: Black,
        marginTop: '2%',
    },
    request: {
        width: '95%',
        height: screenHeight / 8,
        borderWidth: 1, borderColor: Grey,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center'
    },
    locationImage: {
        width: '90%',
        resizeMode: 'contain'
    },
    activeButton: { flexDirection: 'row', height: '95%', width: '25%', alignItems: "center", justifyContent: "center", alignSelf: 'center', borderBottomColor: DarkBlue, borderBottomWidth: 3, borderRadius: 2 },
    activeLabel: { fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, },
    Button: { flexDirection: 'row', height: '95%', width: '25%', alignItems: "center", justifyContent: "center", alignSelf: 'center', borderBottomColor: WhiteGery, borderBottomWidth: 3 },
    label: { fontFamily: appFontBold, fontSize: screenWidth / 40, color: Black, },
});

export default DriverRequests;
