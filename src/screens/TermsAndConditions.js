import React, { Component, useEffect, useState } from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  StyleSheet,
  I18nManager,
  BackHandler,
} from 'react-native';
import {
  appFont,
  appFontBold,
  Green,
  WhiteGreen,
  screenWidth,
  White,
  DarkGreen,
  Blue,
  MediumGrey,
  DarkGrey,
  WhiteGery,
  Black,
  screenHeight,
  Red,
} from '../components/Styles';
import { Button } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useFocusEffect } from '@react-navigation/native';
// import Model from './Model';
// import Loading from '../components/Loading';
const TermsAndConditions = props => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const settings = useSelector(state => state.general.settings);
  const [termsAndConditionData, setTermsAndConditionData] = useState(settings.terms_and_conditions);

  console.log(I18nManager.isRTL);
  useEffect(() => {

  }, []);

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        props.navigation.goBack()
        return true;
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );

  if (loading) {

    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.terms_and_conditions')}
          drawerPress={() => {
            props.navigation.navigate('More');
          }}
          backPress={() => {
            props.navigation.goBack();
          }}
        />

        <ScrollView
          style={{ width: '100%', height: '100%', marginBottom: '2%' }}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.section}>
            <View style={styles.textContanier}>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>
              <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                  <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                </SkeletonPlaceholder>
              </View>



            </View>
          </View>
          <View style={{ height: screenHeight / 8 }}></View>
        </ScrollView>

      </View>
    );

  } else {
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.terms_and_conditions')}
          drawerPress={() => {
            props.navigation.navigate('More');
          }}
          backPress={() => {
            props.navigation.goBack();
          }}
        />

        <ScrollView
          style={{ width: '100%', height: '100%', marginBottom: '2%' }}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.section}>
            {/* <Text style={styles.label}>{strings('lang.terms_and_conditions')}</Text> */}
            <View style={styles.textContanier}>
              <Text style={styles.body}>{termsAndConditionData}</Text>
            </View>
          </View>
          <View style={{ height: screenHeight / 20 }}></View>
        </ScrollView>

      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: 'white',
  },
  section: { width: '90%', alignSelf: 'center' },
  labelContanier: {
    width: '100%',
    marginVertical: '3%',
    alignItems: 'flex-start',
  },
  textContanier: {
    width: '100%',
    marginVertical: '2%',
    alignItems: 'flex-start',
  },
  label: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 20, marginTop: screenHeight / 50, alignSelf: 'flex-start' },
  body: {
    fontFamily: appFontBold,
    color: DarkGrey,
    fontSize: screenWidth / 24,
    alignSelf: 'flex-start',
    textAlign: 'left'
  },
});

export default TermsAndConditions;
