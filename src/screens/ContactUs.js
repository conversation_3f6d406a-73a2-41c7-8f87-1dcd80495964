import React, { Component, useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  StyleSheet,
  I18nManager,
  Linking,
  BackHandler,
} from 'react-native';
import {
  appFont,
  appFontBold,
  Green,
  WhiteGreen,
  screenWidth,
  White,
  DarkGreen,
  Blue,
  MediumGrey,
  DarkGrey,
  WhiteGery,
  screenHeight,
  appColor2,
  appColor1,
  Black,
  Red,
  DarkYellow,
  LightBlue,
  DarkBlue,
} from '../components/Styles';
import { Button, Input, Textarea } from 'native-base';
import Header from '../components/Header';
import { ScrollView, TextInput } from 'react-native-gesture-handler';
import { strings } from './i18n';
import SelectDropdown from 'react-native-select-dropdown';
import Footer from '../components/Footer';
import RBSheet from 'react-native-raw-bottom-sheet';
import StarRating from 'react-native-star-rating';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useDispatch } from 'react-redux';
import Toaster from '../components/Toaster';
import Loading from '../components/Loading';
import * as generalActions from '../../Store/Actions/general';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ContactUs = props => {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [suggest, setSuggest] = useState('');
  const [massageId, setMassageId] = useState('');
  const [massageName, setMassageName] = useState('');
  const [Massages, setMassages] = useState([]);
  const [contactTypes, setContactTypes] = useState([]);
  const typeOfMsg = [1, 2, 3, 4, 5];
  const refRbSheet = useRef();
  const [appRate, setAppRate] = useState(5);
  const [serviceRate, setServiceRate] = useState(5);
  const [providerRate, setProviderRate] = useState(5);
  const [messageError, setMessageError] = useState('');
  const [emailError, setemailError] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [instgramLink, setInstagramLink] = useState('');
  const [twitterLink, setTwitterLink] = useState('');
  const [snapchatLink, setSnapchatLink] = useState('');
  const [whatsappNumber, setWhatsappNumber] = useState('');
  const [loadingMore, setLoadingMore] = useState(false);
  const [token, setToken] = useState('');

  const IsFocused = useIsFocused();

  const dispatch = useDispatch();

  const loginfirst = async () => {
    Toaster(
      'top',
      'danger',
      Red,
      strings('lang.mustLogin'),
      White,
      1500,
      screenHeight / 15,
    );
  }

  useEffect(() => {
    async function GetInitalData() {
      let token = await AsyncStorage.getItem('token')
      setToken(token)
    }
    GetInitalData()
  }, [props, IsFocused])

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        props.navigation.goBack()
        return true;
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );


  const sendMassage = async () => {
    let userType = await AsyncStorage.getItem('userType')

    if (!token) {
      loginfirst()
    }
    else if (!message) {
      setMessageError(strings('lang.Please_insert_field'))
    }
    else {
      setLoadingMore(true)
      try {
        let response = await dispatch(generalActions.supportMessages(message));
        if (response.success == true) {
          console.log(response);
          Toaster(
            'top',
            'success',
            DarkGreen,
            strings('lang.sendSuccessfully'),
            White,
            1500,
            screenHeight / 50,
          );
          if (userType == '1') {
            props.navigation.navigate('Home');
          } else {
            props.navigation.navigate('DriverRequests');
          }

        } else {
          if (response.message) {
            Toaster(
              'top',
              'danger',
              Red,
              response.message,
              White,
              1500,
              screenHeight / 50,
            );
          }
        }
        setLoadingMore(false)
      } catch (err) {
        console.log(err);
        setLoadingMore(false)
      }
    }
  }



  return (
    <View style={{ flex: 1, backgroundColor: White }}>
      <Header
        title={strings('lang.ContactUs')}
        backPress={() => {
          props.navigation.goBack();
        }}
      />

      {loadingMore
        ?
        <Loading />
        :
        <View></View>}

      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        style={{ width: '90%', alignSelf: 'center', paddingTop: 0 }}
      >


        <Text
          style={{
            fontFamily: appFontBold,
            color: Black,
            fontSize: screenWidth / 26,
            alignSelf: 'flex-start',
            marginVertical: 10,
          }}
        >
          {strings('lang.writeyourmessage')}
        </Text>
        <Textarea
          style={{
            borderWidth: 0.8,
            borderColor: MediumGrey,
            backgroundColor: White,
            borderRadius: 3,
            height: screenHeight / 3.5,
            fontFamily: appFont,
            fontSize: screenWidth / 30,
            paddingHorizontal: 10,
          }}
          value={message}
          onChangeText={text => {
            setMessage(text)
            setMessageError('')
          }}
        />
        {messageError ? (
          <Text
            style={{
              fontFamily: appFont,
              color: Red,
              fontSize: screenWidth / 37,
              alignSelf: 'flex-end',
              marginEnd: 25,
              // marginTop: -10,
            }}
          >
            {messageError}
          </Text>
        ) : (
          <View></View>
        )}

        <Button
          onPress={() => { sendMassage() }}
          // onPress={sendMassage}
          style={styles.buttonContainer}
        >
          <Text style={styles.buttonText}>{strings('lang.Send')}</Text>
        </Button>




      </KeyboardAwareScrollView>


      {/* <View style={{height: screenHeight / 10}}></View> */}
    </View>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    backgroundColor: DarkBlue,
    width: '100%',
    height: screenHeight / 18,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    borderRadius: 20,
    marginTop: screenHeight / 10,
    elevation: 10,
  },
  buttonText: {
    color: White,
    fontFamily: appFontBold,
    fontSize: screenWidth / 25,
  },
  text: {
    color: Black,
    fontFamily: appFontBold,
    fontSize: screenWidth / 25,
  },
  labelContainer: {
    width: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  textDarkGrey: {
    fontFamily: appFontBold,
    color: Black,
    fontSize: screenWidth / 30,
  },
  // textRed: { fontFamily: appFontBold, color: Red, fontSize: screenWidth / 26 },
  icon: {
    width: '4%',
    height: '18%',
    tintColor: appColor1,
    resizeMode: 'contain',
    marginHorizontal: '5%',
    // position: 'absolute',
    // right: I18nManager.isRTL ? 0 : '100%',
  },
  rowContainer: {
    width: '95%',
    height: screenHeight / 16,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  labelText: {
    color: Black,
    fontSize: screenWidth / 28,
    fontFamily: appFontBold,
  },
});

export default ContactUs;
