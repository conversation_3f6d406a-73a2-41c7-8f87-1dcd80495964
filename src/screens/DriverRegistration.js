import React, { Component, useEffect, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    PermissionsAndroid,
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    MediumGreen,
    appColor1,
    DarkBlue,
    DarkYellow,
} from '../components/Styles';
import { Button, Input } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { Slider } from '@miblanchard/react-native-slider';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
// import RNFS from 'react-native-fs';

const DriverRegistration = props => {
    const dispatch = useDispatch();
    const [termsAndConditionData, setTermsAndConditionData] = useState('');
    const [loading, setLoading] = useState(false);
    const [low, setLow] = useState(0);
    const [high, setHigh] = useState(0);
    const [minValue, setMinValue] = useState(0);
    const [maxValue, setMaxValue] = useState(0);
    const [error_userName, seterror_userName] = useState('')
    const [userName, setuserName] = useState('')
    const [email, setEmail] = useState('')
    const [error_email, seterror_email] = useState('')
    const [phone, setPhone] = useState('')
    const [error_phone, seterror_phone] = useState('')
    const [nationalAddress, setNationalAddress] = useState('')
    const [error_nationalAddress, seterror_nationalAddress] = useState('')
    const [postalCode, setPostalCode] = useState('')
    const [error_postalCode, seterror_postalCode] = useState('')
    const [cityName, setCityName] = useState('')
    const [error_cityName, seterror_cityName] = useState('')
    const [areaName, setAreaName] = useState('')
    const [error_areaName, seterror_areaName] = useState('')
    const [streetName, setStreetName] = useState('')
    const [error_streetName, seterror_streetName] = useState('')
    const [genderId, setGenderId] = useState('1');
    const [socialStatusId, setSocialStatusId] = useState('1');
    const [smokeId, setsmokeId] = useState('1');
    const [busyId, setBusyId] = useState('1');

    const [base64Image, setBase64Image] = useState('');
    const [cover, setCover] = useState();
    const [imageLeadership, setImageLeadership] = useState();
    const [imageCar, setImageCar] = useState();
    const [imageCriminalStatus, setImageCriminalStatus] = useState();

    console.log(I18nManager.isRTL);
    useEffect(() => {

    }, []);

    const requestCameraPermission = async () => {
        try {
            const granted = await PermissionsAndroid.request(
                PermissionsAndroid.PERMISSIONS.CAMERA,
                {
                    title: "App Camera Permission",
                    message: "App needs access to your camera ",
                    buttonNeutral: "Ask Me Later",
                    buttonNegative: "Cancel",
                    buttonPositive: "OK"
                }
            );
            if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                console.log("Camera permission given");
            } else {
                console.log("Camera permission denied");
            }
        } catch (err) {
            console.warn(err);
        }
    };

    const chooseImages = async (type) => {

        let options = {
            quality: 0.5,
            maxWidth: 500,
            maxheight: 400,
            title: 'Select Image',
            customButtons: [
                {
                    name: 'customOptionKey',
                    title: 'Choose Photo from Custom Option',
                },
            ],
            storageOptions: {
                skipBackup: true,
                path: 'images',
            },
        };
        launchImageLibrary(options, response => {
            console.log('Response = ', response);

            if (response.didCancel) {
                console.log('User cancelled image picker');
            } else if (response.error) {
                console.log('ImagePicker Error: ', response.error);
            } else if (response.customButton) {
                console.log('User tapped custom button: ', response.customButton);
                alert(response.customButton);
            } else {
                // let cover = response.assets[0];
                // You can also display the image using data:
                // let source = {
                //   uri: 'data:image/jpeg;base64,' + response.data
                // };
                let cover = {
                    uri: response.assets[0].uri,
                    type: response.assets[0].type,
                    name: response.assets[0].fileName,
                    link: '',
                };
                switch (type) {
                    case 'ID':
                        setCover(cover);
                        break;
                    case 'License':
                        setImageLeadership(cover);
                        break;
                    case 'Car':
                        setImageCar(cover);
                        break;
                    case 'Criminal':
                        setImageCriminalStatus(cover);
                        break;

                    default:
                        break;
                }
            }
        });
    };



    if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.driverregistration')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    } else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.driverregistration')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={{ width: '95%', alignSelf: 'center', }}>
                        {/* <Slider
                            value={[low, high]}
                            maximumValue={minValue}
                            minimumValue={maxValue}
                            // onValueChange={(value) => setValue(value)}
                            onValueChange={([low, high]) => {
                                setLow(low);
                                setHigh(high);
                            }}
                            thumbTintColor={MediumGreen}
                            maximumTrackTintColor={MediumGrey}
                            minimumTrackTintColor={MediumGreen}
                            disableRange={true}
                            floatingLabel={false}
                            allowLabelOverflow={false}
                        /> */}
                        <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 25, alignItems: 'center', justifyContent: 'center' }}>
                            <View style={{ width: 25, height: 25, borderRadius: 50, borderWidth: 1, borderColor: DarkYellow, }} ></View>
                            <View style={{ height: 3, width: '80%', backgroundColor: MediumGrey }}></View>
                            <View style={{ width: 25, height: 25, borderRadius: 50, backgroundColor: MediumGrey }} ></View>
                        </View>

                        <View style={{ justifyContent: 'space-between', alignItems: 'center', width: '100%', flexDirection: 'row' }}>
                            <Text style={styles.textImg}>{strings('lang.driverdata')}</Text>
                            <Text style={styles.textImg}>{strings('lang.cardata')}</Text>
                        </View>

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.Username')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={userName} onChangeText={(text) => {
                                    setuserName(text);
                                    seterror_userName('')
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.Username')}
                            />
                        </View>
                        {error_userName ?
                            <Text style={styles.loginError}>{error_userName}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        }

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.Phone')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={phone} onChangeText={(text) => {
                                    setPhone(text);
                                    seterror_phone('')
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.Phone')}
                            />
                        </View>
                        {error_phone ?
                            <Text style={styles.loginError}>{error_phone}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        }


                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.Gender')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View style={styles.contentContainer}>
                            <View style={[styles.textInput, { backgroundColor: WhiteGery }]}>
                                <Pressable onPress={() => { setGenderId('1') }} style={styles.genderContainer}>
                                    <View style={styles.gendercircleConatiner}>
                                        <View style={[styles.gendercircle, { backgroundColor: genderId == '1' ? DarkBlue : White }]}>
                                        </View>
                                    </View>
                                    <Text style={styles.genderText}>{strings('lang.male')}</Text>
                                </Pressable>
                                <Pressable onPress={() => { setGenderId('2') }} style={styles.genderContainer}>
                                    <View style={styles.gendercircleConatiner}>
                                        <View style={[styles.gendercircle, { backgroundColor: genderId == '2' ? DarkBlue : White }]}>
                                        </View>
                                    </View>
                                    <Text style={styles.genderText}>{strings('lang.female')}</Text>
                                </Pressable>
                            </View>
                        </View>

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.socialstatus')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View style={styles.contentContainer}>
                            <View style={[styles.textInput, { backgroundColor: WhiteGery }]}>
                                <Pressable onPress={() => { setSocialStatusId('1') }} style={styles.genderContainer}>
                                    <View style={styles.gendercircleConatiner}>
                                        <View style={[styles.gendercircle, { backgroundColor: socialStatusId == '1' ? DarkBlue : White }]}>
                                        </View>
                                    </View>
                                    <Text style={styles.genderText}>{strings('lang.bachelor')}</Text>
                                </Pressable>
                                <Pressable onPress={() => { setSocialStatusId('2') }} style={styles.genderContainer}>
                                    <View style={styles.gendercircleConatiner}>
                                        <View style={[styles.gendercircle, { backgroundColor: socialStatusId == '2' ? DarkBlue : White }]}>
                                        </View>
                                    </View>
                                    <Text style={styles.genderText}>{strings('lang.married')}</Text>
                                </Pressable>
                            </View>
                        </View>

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.Doyousmoke')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View style={styles.contentContainer}>
                            <View style={[styles.textInput, { backgroundColor: WhiteGery }]}>
                                <Pressable onPress={() => { setsmokeId('1') }} style={styles.genderContainer}>
                                    <View style={styles.gendercircleConatiner}>
                                        <View style={[styles.gendercircle, { backgroundColor: smokeId == '1' ? DarkBlue : White }]}>
                                        </View>
                                    </View>
                                    <Text style={styles.genderText}>{strings('lang.Yes')}</Text>
                                </Pressable>
                                <Pressable onPress={() => { setsmokeId('2') }} style={styles.genderContainer}>
                                    <View style={styles.gendercircleConatiner}>
                                        <View style={[styles.gendercircle, { backgroundColor: smokeId == '2' ? DarkBlue : White }]}>
                                        </View>
                                    </View>
                                    <Text style={styles.genderText}>{strings('lang.No')}</Text>
                                </Pressable>
                            </View>
                        </View>

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.sabbatical')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View style={styles.contentContainer}>
                            <View style={[styles.textInput, { backgroundColor: WhiteGery }]}>
                                <Pressable onPress={() => { setBusyId('1') }} style={styles.genderContainer}>
                                    <View style={styles.gendercircleConatiner}>
                                        <View style={[styles.gendercircle, { backgroundColor: busyId == '1' ? DarkBlue : White }]}>
                                        </View>
                                    </View>
                                    <Text style={styles.genderText}>{strings('lang.free')}</Text>
                                </Pressable>
                                <Pressable onPress={() => { setBusyId('2') }} style={styles.genderContainer}>
                                    <View style={styles.gendercircleConatiner}>
                                        <View style={[styles.gendercircle, { backgroundColor: busyId == '2' ? DarkBlue : White }]}>
                                        </View>
                                    </View>
                                    <Text style={styles.genderText}>{strings('lang.Unavailable')}</Text>
                                </Pressable>
                            </View>
                        </View>

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.Email')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={email} onChangeText={(text) => {
                                    setEmail(text);
                                    seterror_email('')
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.Email')}
                            />
                        </View>
                        {error_email ?
                            <Text style={styles.loginError}>{error_email}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        }
                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.nationaladdress')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={nationalAddress} onChangeText={(text) => {
                                    setNationalAddress(text);
                                    seterror_nationalAddress('')
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.nationaladdress')}
                            />
                        </View>
                        {error_nationalAddress ?
                            <Text style={styles.loginError}>{error_nationalAddress}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        }

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.Postalcode')}</Text>
                            <Text style={styles.star}> * </Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={postalCode} onChangeText={(text) => {
                                    setPostalCode(text);
                                    seterror_postalCode('')
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.Postalcode')}
                            />
                        </View>
                        {error_postalCode ?
                            <Text style={styles.loginError}>{error_postalCode}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        }

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.City')}</Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={cityName} onChangeText={(text) => {
                                    setCityName(text);
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.City')}
                            />
                        </View>

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.District')}</Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={areaName} onChangeText={(text) => {
                                    setAreaName(text);
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.District')}
                            />
                        </View>

                        <View style={styles.labelContainer}>
                            <Text style={styles.text}>{strings('lang.Streetname')}</Text>
                        </View>
                        <View>
                            <Input
                                style={styles.input}
                                value={streetName} onChangeText={(text) => {
                                    setStreetName(text);
                                }
                                }
                                placeholderTextColor={'#707070'} placeholder={strings('lang.Streetname')}
                            />
                        </View>

                        <Text style={styles.textImg}>{strings('lang.ImageoftheIDorIqamafromthefront')}</Text>
                        {cover
                            ?
                            (
                                <View style={styles.imageContainer}>
                                    < Button transparent block
                                        onPress={() => {
                                            setCover(null)
                                            setBase64Image('')
                                        }}
                                        style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: 5, backgroundColor: MediumGrey, position: "absolute", zIndex: 100, alignSelf: "flex-start", justifyContent: "center", end: 0, top: 0 }}>
                                        <Image source={require('../images/trash.png')} style={{ width: '130%', height: '130%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />
                                    </Button>
                                    <Image
                                        source={{ uri: cover.uri }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: '100%',
                                            height: '100%'
                                        }}
                                    />
                                </View>
                            )
                            :
                            (
                                <TouchableOpacity onPress={() => { chooseImages('ID') }}>
                                    <View style={[styles.imageContainer, { borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, }]}>
                                        <Image
                                            source={require('../images/img.png')}
                                            style={{ width: screenWidth / 4, height: screenHeight / 8, resizeMode: 'contain', tintColor: DarkGrey }}
                                        />
                                    </View>
                                </TouchableOpacity>
                            )
                        }

                        <Text style={styles.textImg}>{strings('lang.imageofthedriverslicensefromthefront')}</Text>
                        {imageLeadership
                            ?
                            (
                                <View style={styles.imageContainer}>
                                    < Button transparent block
                                        onPress={() => {
                                            setImageLeadership(null)
                                        }}
                                        style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: 5, backgroundColor: MediumGrey, position: "absolute", zIndex: 100, alignSelf: "flex-start", justifyContent: "center", end: 0, top: 0 }}>
                                        <Image source={require('../images/trash.png')} style={{ width: '130%', height: '130%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />
                                    </Button>
                                    <Image
                                        source={{ uri: imageLeadership.uri }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: '100%',
                                            height: '100%'
                                        }}
                                    />
                                </View>
                            )
                            :
                            (
                                <TouchableOpacity onPress={() => { chooseImages('License') }}>
                                    <View style={[styles.imageContainer, { borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, }]}>

                                        <Image
                                            source={require('../images/img.png')}
                                            style={{ width: screenWidth / 4, height: screenHeight / 8, resizeMode: 'contain', tintColor: DarkGrey }}
                                        />
                                    </View>
                                </TouchableOpacity>
                            )
                        }

                        <Text style={styles.textImg}>{strings('lang.imageofthevehicleregistrationformfromthefront')}</Text>
                        {imageCar
                            ?
                            (
                                <View style={styles.imageContainer}>
                                    < Button transparent block
                                        onPress={() => {
                                            setImageCar(null)
                                        }}
                                        style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: 5, backgroundColor: MediumGrey, position: "absolute", zIndex: 100, alignSelf: "flex-start", justifyContent: "center", end: 0, top: 0 }}>
                                        <Image source={require('../images/trash.png')} style={{ width: '130%', height: '130%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />
                                    </Button>
                                    <Image
                                        source={{ uri: imageCar.uri }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: '100%',
                                            height: '100%'
                                        }}
                                    />
                                </View>
                            )
                            :
                            (
                                <TouchableOpacity onPress={() => { chooseImages('Car') }}>
                                    <View style={[styles.imageContainer, { borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, }]}>

                                        <Image
                                            source={require('../images/img.png')}
                                            style={{ width: screenWidth / 4, height: screenHeight / 8, resizeMode: 'contain', tintColor: DarkGrey }}
                                        />
                                    </View>
                                </TouchableOpacity>
                            )
                        }

                        <Text style={styles.textImg}>{strings('lang.Criminalstatus')}</Text>
                        {imageCriminalStatus
                            ?
                            (
                                <View style={styles.imageContainer}>
                                    <Button transparent block
                                        onPress={() => {
                                            setImageCriminalStatus(null)
                                        }}
                                        style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: 5, backgroundColor: MediumGrey, position: "absolute", zIndex: 100, alignSelf: "flex-start", justifyContent: "center", end: 0, top: 0 }}>
                                        <Image source={require('../images/trash.png')} style={{ width: '130%', height: '130%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />
                                    </Button>
                                    <Image
                                        source={{ uri: imageCriminalStatus.uri }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: '100%',
                                            height: '100%'
                                        }}
                                    />
                                </View>
                            )
                            :
                            (
                                <TouchableOpacity onPress={() => { chooseImages('Criminal') }}>
                                    <View style={[styles.imageContainer, { borderStyle: 'dashed', borderWidth: 1, borderColor: MediumGrey, }]}>

                                        <Image
                                            source={require('../images/img.png')}
                                            style={{ width: screenWidth / 4, height: screenHeight / 8, resizeMode: 'contain', tintColor: DarkGrey }}
                                        />
                                    </View>
                                </TouchableOpacity>
                            )
                        }

                        <View style={{ width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', marginTop: 30, flexDirection: 'row' }}>
                            <Button
                                transparent
                                onPress={() => {
                                    props.navigation.navigate('DriverRegistration1');
                                }}
                                style={{ backgroundColor: DarkBlue, width: '68%', height: screenHeight / 18, alignItems: 'center', justifyContent: 'center', borderRadius: 100 }}
                            >
                                <Text style={{ color: White, fontSize: screenWidth / 25, fontFamily: appFont }}>
                                    {strings('lang.next')}
                                </Text>
                            </Button>

                            <Button
                                transparent
                                onPress={() => {
                                }}
                                style={{ backgroundColor: WhiteGery, height: screenHeight / 18, width: '28%', alignItems: 'center', justifyContent: 'center', borderRadius: 100 }}
                            >
                                <Text style={{ color: Black, fontSize: screenWidth / 25, fontFamily: appFont }}>
                                    {strings('lang.back')}
                                </Text>
                            </Button>
                        </View>
                    </View>
                    <View style={{ height: screenHeight / 20 }}></View>
                </ScrollView>

            </View>
        );
    }
};

const styles = StyleSheet.create({
    labelContainer: { flexDirection: 'row', alignItems: "center", marginBottom: 0, marginTop: screenHeight / 100 },

    text: {
        fontSize: screenWidth / 30,
        fontFamily: appFont,
        // alignSelf: 'flex-start',
        // marginHorizontal: '2.5%',
        marginBottom: 5,
        color: Black,
    },
    star: {
        fontFamily: appFont,
        fontSize: screenWidth / 20,
        color: Red
    },
    input: {
        height: screenHeight / 20, borderWidth: 1, borderRadius: 100, borderColor: MediumGrey, flexDirection: 'row', justifyContent: 'center', paddingStart: '2.5%',
        width: "100%", fontSize: screenWidth / 33, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left', textAlignVertical: 'center'
    },

    loginError: {
        color: Red, fontFamily: appFont, alignSelf: 'flex-start', fontSize: screenWidth / 33
    },
    textImg: {
        fontSize: screenWidth / 30,
        fontFamily: appFont,
        color: Black,
        marginTop: '2%', alignSelf: 'flex-start'
    },
    imageContainer: {
        flexDirection: 'row',
        width: '100%',
        alignSelf: 'center',
        height: screenHeight / 6,
        marginBottom: 10,
        borderWidth: 1,
        borderColor: MediumGrey,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    iconConatiner2: { height: '100%', width: '10%', alignItems: 'center', justifyContent: 'center', },
    genderContainer: { width: screenWidth / 2.5, height: screenHeight / 22, alignItems: 'center', justifyContent: 'flex-start', flexDirection: 'row', paddingHorizontal: '5%' },
    gendercircleConatiner: { width: screenHeight / 50, height: screenHeight / 50, borderRadius: screenHeight / 100, backgroundColor: White, borderWidth: .5, borderColor: MediumGrey, alignItems: 'center', justifyContent: 'center' },
    gendercircle: { width: screenHeight / 75, height: screenHeight / 75, borderRadius: screenHeight / 150, backgroundColor: White, },
    genderText: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 33, marginStart: 5 },
    contentContainer: {
        width: '100%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginBottom: '2%',
        height: screenHeight / 18,
        borderRadius: 5,
        paddingHorizontal: 0,
        flexDirection: 'row',
    },
    textInput: {
        width: '100%',
        flexDirection: 'row',
        alignSelf: 'center',
        borderRadius: 100,
        height: '100%',
        alignItems: 'center',
        justifyContent: 'space-between',
        textAlignVertical: 'center',
        fontFamily: appFont,
        fontSize: screenWidth / 32,
        textAlign: I18nManager.isRTL ? 'right' : 'left',
        paddingHorizontal: 10, overflow: 'hidden'
    },
});

export default DriverRegistration;
