import React, { Component, useEffect, useRef, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
    PermissionsAndroid,
    BackHandler,
    Linking,
    Alert,
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    appColor1,
    MediumGreen,
    DarkBlue,
    LightGreen,
    DarkYellow,
} from '../components/Styles';
import { Button, Textarea } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import RBSheet from 'react-native-raw-bottom-sheet';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import Geolocation from 'react-native-geolocation-service';
import MapView, { Marker } from 'react-native-maps';
import MapViewDirections from 'react-native-maps-directions';
import * as packagesActions from '../../Store/Actions/packages';
import * as cityTripsActions from '../../Store/Actions/cityTrips';
import * as vipTripsActions from '../../Store/Actions/vipTrips';
import * as dailyTripsActions from '../../Store/Actions/dailyTrips';
import Loading from '../components/Loading';
import { Modalize } from 'react-native-modalize';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import StarRating from 'react-native-star-rating';

const ASPECT_RATIO = screenWidth / screenHeight

const DetailsTrip = props => {
    const dispatch = useDispatch();
    const [termsAndConditionData, setTermsAndConditionData] = useState('');
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [tirpId, setTirpId] = useState(0);
    const [trip, setTrip] = useState({});
    // const [trips, setTrips] = useState([
    //     {
    //         id: 0,
    //         name: 'موقعك الحالى',
    //         title: 'مسجد قباء',
    //         price: '30',
    //         text: 'ملغى',
    //         image: '../images/Group191.png'
    //     },
    //     {
    //         id: 1,
    //         name: 'موقعك الحالى',
    //         title: 'مسجد قباء',
    //         price: '30',
    //         text: 'تمت',
    //         image: '../images/Group191.png'
    //     },

    // ]);
    const [region, SetRegion] = useState({
        latitude: 0,
        longitude: 0,
        longitudeDelta: 0.01 * ASPECT_RATIO,
        latitudeDelta: 0.01
    })
    // const [destination, setDestination] = useState({
    //     latitude: 31.255267258217355,
    //     longitude: 29.990218216529684,
    //     longitudeDelta: 0.01 * ASPECT_RATIO,
    //     latitudeDelta: 0.01
    // })
    const [lat, SetLat] = useState(0)
    const [lng, SetLng] = useState(0)
    const refRBSheet = useRef();
    const IsFocused = useIsFocused();
    const [starCount, setStarCount] = useState(0);
    const [review, setReview] = useState('');
    const [hiddenHeader, setHiddenHeader] = useState(false)
    const reasonss = useSelector(state => state.general.clientReasons);
    const [reasons, setReasons] = useState(reasonss);
    const [reasonsId, setReasonId] = useState(null);
    const [reasonText, setReasonText] = useState(strings('lang.message15'));
    const [newMessgae, setNewMessgae] = useState(false);

    const modalizeRefRat = useRef();
    const contentRefRat = useRef();
    const refRBSheetReasons = useRef();


    let origin = {};
    let destination = {};

    // if (props.route.params.type == 'betweenCities') {
    //     origin = { latitude: 0, longitude: 0 };
    //     destination = { latitude: 0, longitude: 0 };

    // }
    // else {
    if (props.route.params.type == 'packages') {
        if (props.route.params.item.destinations[0].from_address_id) {
            origin = { latitude: props.route.params.item.destinations[0].address_from.lat, longitude: props.route.params.item.destinations[0].address_from.lng };
        }
        else {
            origin = { latitude: props.route.params.item.destinations[0].from_lat, longitude: props.route.params.item.destinations[0].from_lng };
        }
        if (props.route.params.item.destinations[0].to_address_id) {
            destination = { latitude: props.route.params.item.destinations[0].address_to.lat, longitude: props.route.params.item.destinations[0].address_to.lng };
        }
        else {
            destination = { latitude: props.route.params.item.destinations[0].to_lat, longitude: props.route.params.item.destinations[0].to_lng };
        }
    }
    else {
        if (props.route.params.item.from_address_id) {
            origin = { latitude: props.route.params.item.address_from.lat, longitude: props.route.params.item.address_from.lng };
        }
        else {
            origin = { latitude: props.route.params.item.from_lat, longitude: props.route.params.item.from_lng };
        }
        if (props.route.params.item.to_address_id) {
            destination = { latitude: props.route.params.item.address_to.lat, longitude: props.route.params.item.address_to.lng };
        }
        else {
            destination = { latitude: props.route.params.item.to_lat, longitude: props.route.params.item.to_lng };
        }
    }
    // }

    const trips = useSelector((state) => state.trips);
    const newMessage = useSelector((state) => state.message);

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                // props.navigation.openDrawer();
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    useEffect(() => {
        console.log('Message reducer In Screen', newMessage);
        if (newMessage.message.chat_message) {
            setNewMessgae(true)
        }
    }, [newMessage]);

    useEffect(() => {
        console.log('tripsio', trips);
        if (trips.updatedVipTrip.vip_trip && props.route.params.type == 'orderVip') {
            setTrip(trips.updatedVipTrip.vip_trip)
            if (trips.updatedVipTrip.vip_trip.status == "finished") {
                modalizeRefRat.current.open();
            }
            if (
                trips.updatedVipTrip.vip_trip.status == "accepted" ||
                trips.updatedVipTrip.vip_trip.status == "waiting_for_client" ||
                trips.updatedVipTrip.vip_trip.status == "started" ||
                trips.updatedVipTrip.vip_trip.status == "finished"
            ) {
                setHiddenHeader(true)
            }
        }
        if (trips.updatedCityTrip.city_trip && props.route.params.type == 'betweenCities') {
            setTrip(trips.updatedCityTrip.city_trip)

            if (
                trips.updatedCityTrip.city_trip.status == "accepted" ||
                trips.updatedCityTrip.city_trip.status == "waiting_for_client" ||
                trips.updatedCityTrip.city_trip.status == "started" ||
                trips.updatedCityTrip.city_trip.status == "finished"
            ) {
                setHiddenHeader(true)
            }
            if (trips.updatedCityTrip.city_trip.status == "finished") {
                modalizeRefRat.current.open();
                setHiddenHeader(false)
            }
        }
        if (trips.updatedPackageTrip.user_package && props.route.params.type == 'packages') {
            setTrip(trips.updatedPackageTrip.user_package)

            if (
                trips.updatedPackageTrip.user_package.status == "accepted" ||
                trips.updatedPackageTrip.user_package.status == "waiting_for_client" ||
                trips.updatedPackageTrip.user_package.status == "started" ||
                trips.updatedPackageTrip.user_package.status == "finished"
            ) {
                setHiddenHeader(true)
            }
            if (trips.updatedPackageTrip.user_package.status == "finished") {
                modalizeRefRat.current.open();
                setHiddenHeader(false)
            }
        }
    }, [trips]);

    useEffect(() => {
        setLoadingMore(true)


        console.log(props.route.params);

        const onRegionChangeComplete = (region) => {

            var initial_Region = {
                longitudeDelta: region.longitudeDelta,
                latitudeDelta: region.latitudeDelta,
                longitude: region.longitude,
                latitude: region.latitude
            }

            onRegionChangeComplete.bind(initial_Region)

            console.log('region', region);

        }

        const requestLocationPermission = async () => {
            try {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                    {
                        'title': 'Location Permission',
                        'message': 'MyMapApp needs access to your location'
                    }
                )

                if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                    get_Lat_User()
                    // Alert.alert("Please allow location")
                } else {
                    console.log("Location permission denied")
                    setLoading(false)

                }
            } catch (err) {
                console.warn(err)
                setLoading(false)
            }
        }

        const RequestIosPermissio = () => {
            Geolocation.requestAuthorization('always').then((res) => {
                console.log('res', res);
                if (res == 'denied') {
                    //  Alert.alert("Please allow location to continuo")/
                    // Linking.openURL('app-settings:');
                    showCustomAlert()
                }
            });
            setLoading(false)
        }

        const get_Lat_User = async () => {
            // Geolocation.requestAuthorization();
            // Geolocation.setRNConfiguration({
            //   skipPermissionRequests: false,
            //   authorizationLevel: 'whenInUse',
            // });
            if (Platform.OS == 'ios') {
                RequestIosPermissio()

            }

            Geolocation.getCurrentPosition(
                (position) => {
                    console.log('position', position);
                    var lat = parseFloat(position.coords.latitude)
                    var longi = parseFloat(position.coords.longitude)
                    var initialRegion = {
                        latitude: props.route.params.item.from_lat ? props.route.params.item.from_lat : lat,
                        longitude: props.route.params.item.from_lng ? props.route.params.item.from_lng : longi,
                        longitudeDelta: 0.01 * ASPECT_RATIO,
                        latitudeDelta: 0.01
                    }
                    SetRegion(initialRegion)
                    SetLat(lat)
                    SetLng(longi)
                },
                (error) => {
                    console.log('error', error);

                },
                { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 1000 },
            );
            setLoading(false)
        }

        const getDetailsTrip = async () => {
            if (props.route.params.type == 'dailytTrip') {
                setLoading(true)
                try {
                    let response = await dispatch(dailyTripsActions.showTrip(props.route.params.item.id));
                    if (response.success == true) {
                        setTrip(response.data)

                    }
                    else {
                        if (response.message) {
                            Toaster(
                                'top',
                                'danger',
                                Red,
                                response.message,
                                White,
                                1500,
                                screenHeight / 15,
                            );
                        }
                    }
                    setLoading(false);
                }
                catch (err) {
                    console.log('err', err)
                    setLoading(false);
                }
            }
            else if (props.route.params.type == 'orderVip') {
                setLoading(true)
                try {
                    let response = await dispatch(vipTripsActions.showTrip(props.route.params.item.id));
                    if (response.success == true) {
                        setTrip(response.data)
                        // if (response.data.status == "finished") {
                        //     modalizeRefRat.current.open();
                        // }
                        if (
                            response.data.status == "accepted" ||
                            response.data.status == "waiting_for_client" ||
                            response.data.status == "started"
                            // response.data.status == "finished"
                        ) {
                            setHiddenHeader(true)
                        }
                    }
                    else {
                        if (response.message) {
                            Toaster(
                                'top',
                                'danger',
                                Red,
                                response.message,
                                White,
                                1500,
                                screenHeight / 15,
                            );
                        }
                    }
                    setLoading(false);
                }
                catch (err) {
                    console.log('err', err)
                    setLoading(false);
                }
            }
            else if (props.route.params.type == 'betweenCities') {
                setLoading(true)
                try {
                    let response = await dispatch(cityTripsActions.showTrip(props.route.params.item.id));
                    if (response.success == true) {
                        setTrip(response.data)
                        if (
                            response.data.status == "accepted" ||
                            response.data.status == "waiting_for_client" ||
                            response.data.status == "started"
                            // response.data.status == "finished"
                        ) {
                            setHiddenHeader(true)
                        }
                    }
                    else {
                        if (response.message) {
                            Toaster(
                                'top',
                                'danger',
                                Red,
                                response.message,
                                White,
                                1500,
                                screenHeight / 15,
                            );
                        }
                    }
                    setLoading(false);
                }
                catch (err) {
                    console.log('err', err)
                    setLoading(false);
                }
            }
            else if (props.route.params.type == 'packages') {
                setLoading(true)
                try {
                    let response = await dispatch(packagesActions.getMyPackage(props.route.params.item.id));
                    if (response.success == true) {
                        setTrip(response.data)
                        if (
                            response.data.status == "accepted" ||
                            response.data.status == "waiting_for_client" ||
                            response.data.status == "started"
                            // response.data.status == "finished"
                        ) {
                            setHiddenHeader(true)
                        }
                    }
                    else {
                        if (response.message) {
                            Toaster(
                                'top',
                                'danger',
                                Red,
                                response.message,
                                White,
                                1500,
                                screenHeight / 15,
                            );
                        }
                    }
                    setLoading(false);
                }
                catch (err) {
                    console.log('err', err)
                    setLoading(false);
                }
            }

        };

        const showCustomAlert = () => {
            Alert.alert(
                'Permission Alert',
                'The app will not work properly without this permission ',
                [
                    {
                        text: 'Access permission from settings',
                        onPress: () => {
                            console.log('start permission');
                            Linking.openURL('app-settings:');
                            // get_Lat_User()
                        },
                    },
                    {
                        text: 'Cancel',
                        onPress: () => {
                            console.log('exit');
                            props.navigation.navigate('Orders')
                        },
                    },
                ],
                { cancelable: false }
            );
        };

        getDetailsTrip();

        // const item = props.route.params.item
        // setItem(item)
        // if (item) {
        //     const region = {
        //         latitude: item.lat,
        //         longitude: item.lng,
        //         longitudeDelta: 0.01 * ASPECT_RATIO,
        //         latitudeDelta: 0.01
        //     }
        //     SetRegion(region)
        //     SetLat(item.lat)
        //     SetLng(item.lng)
        //     setLoading(true)
        // }
        // else {
        if (Platform.OS == 'ios') {
            get_Lat_User()
        }
        else {
            requestLocationPermission();
        }

        setLoadingMore(false)


    }, [props, IsFocused]);

    const cancelTrip = async () => {
        refRBSheetReasons.current.close();
        if (reasonsId == null && reasonText == '') {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.Pleasechooseorwriteareasonforcancellation'),
                White,
                1500,
                screenHeight / 50,
            );
        } else {
            if (props.route.params.type == 'orderVip') {
                try {
                    setLoadingMore(true)
                    let response = await dispatch(vipTripsActions.cancelTrip(
                        props.route.params.item.id,
                    ));

                    if (response.success == true) {
                        setLoadingMore(false)
                        Toaster(
                            'top',
                            'success',
                            DarkGreen,
                            strings('lang.StausChangedSuccessfully'),
                            White,
                            1500,
                            screenHeight / 15,
                        );
                        props.navigation.navigate('Orders');
                    }
                    else {
                        if (response.message) {
                            Toaster(
                                'top',
                                'danger',
                                Red,
                                response.message,
                                White,
                                1500,
                                screenHeight / 50,
                            );
                        }
                        setLoadingMore(false)
                    }
                } catch (err) {
                    console.log(err);
                    setLoadingMore(false)
                }
            }
            else if (props.route.params.type == 'betweenCities') {
                try {
                    setLoadingMore(true)
                    let response = await dispatch(cityTripsActions.cancelTrip(
                        props.route.params.item.id,
                    ));

                    if (response.success == true) {
                        setLoadingMore(false)
                        Toaster(
                            'top',
                            'success',
                            DarkGreen,
                            strings('lang.StausChangedSuccessfully'),
                            White,
                            1500,
                            screenHeight / 15,
                        );
                        props.navigation.navigate('Orders');
                    }
                    else {
                        if (response.message) {
                            Toaster(
                                'top',
                                'danger',
                                Red,
                                response.message,
                                White,
                                1500,
                                screenHeight / 50,
                            );
                        }
                        setLoadingMore(false)
                    }
                } catch (err) {
                    console.log(err);
                    setLoadingMore(false)
                }
            }
            // else if (props.route.params.type == 'packages') {
            //     try {
            //         setLoadingMore(true)
            //         let response = await dispatch(cityTripsActions.cancelTrip(
            //             props.route.params.item.id,
            //         ));

            //         if (response.success == true) {
            //             setLoadingMore(false)
            //             Toaster(
            //                 'top',
            //                 'success',
            //                 DarkGreen,
            //                 strings('lang.StausChangedSuccessfully'),
            //                 White,
            //                 1500,
            //                 screenHeight / 15,
            //             );
            //             props.navigation.navigate('Orders');
            //         }
            //         else {
            //             if (response.message) {
            //                 Toaster(
            //                     'top',
            //                     'danger',
            //                     Red,
            //                     response.message,
            //                     White,
            //                     1500,
            //                     screenHeight / 50,
            //                 );
            //             }
            //             setLoadingMore(false)
            //         }
            //     } catch (err) {
            //         console.log(err);
            //         setLoadingMore(false)
            //     }
            // }
        }
    }

    const rateDriver = async () => {
        let response = null;
        try {
            setLoadingMore(true)
            if (props.route.params.type == 'dailytTrip') {
                response = await dispatch(dailyTripsActions.rateDriver(
                    trip.id,
                    starCount,
                    review
                ));
            }
            else if (props.route.params.type == 'orderVip') {
                response = await dispatch(vipTripsActions.rateDriver(
                    trip.id,
                    starCount,
                    review
                ));
            }
            else if (props.route.params.type == 'betweenCities') {
                response = await dispatch(cityTripsActions.rateDriver(
                    trip.id,
                    starCount,
                    review
                ));
            }
            else if (props.route.params.type == 'packages') {
                response = await dispatch(packagesActions.rateDriver(
                    trip.id,
                    starCount,
                    review
                ));
            }

            if (response.success == true) {
                setLoadingMore(false)
                setTrip({})
                modalizeRefRat.current.close();
                props.navigation.navigate('Home')
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                }
                setLoadingMore(false)
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.detailsofthetrip')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.navigate('Orders');
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                {!hiddenHeader
                    ?
                    <Header
                        title={strings('lang.detailsofthetrip')}
                        // drawerPress={() => {
                        //     props.navigation.navigate('More');
                        // }}
                        backPress={() => {
                            props.navigation.navigate('Orders');
                        }}
                    />
                    :
                    <></>}


                {loadingMore
                    ?
                    <Loading />
                    :
                    <></>
                }

                <MapView
                    initialRegion={region}
                    region={region}
                    onRegionChangeComplete={null}
                    style={{ width: "100%", height: screenHeight / 2, zIndex: 1000 }}
                // pitchEnabled={false}
                // rotateEnabled={false}
                // scrollEnabled={false}
                // zoomEnabled={false}
                >
                    {trip && trip.status != "started" && trip.status != "finished" && trip.status != "cancelled" && trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lat
                        &&
                        <Marker
                            coordinate={{
                                latitude: trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lat,
                                longitude: trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lng
                            }}
                            style={{ width: 20, height: 20 }}
                            resizeMode="contain"
                            image={require('../images/carxx.png')}
                        />
                    }
                    {trip && trip.status != "started" && trip.status != "finished" && trip.status != "cancelled" && props.route.params.type == 'orderVip' && trip.driver_vip_trip && trip.driver_vip_trip.driver && trip.driver_vip_trip.driver.current_lat
                        &&
                        <Marker
                            coordinate={{
                                latitude: trip.driver_vip_trip && trip.driver_vip_trip.driver && trip.driver_vip_trip.driver.current_lat,
                                longitude: trip.driver_vip_trip && trip.driver_vip_trip.driver && trip.driver_vip_trip.driver.current_lng
                            }}
                            style={{ width: 20, height: 20 }}
                            resizeMode="contain"
                            image={require('../images/carxx.png')}
                        />
                    }
                    {trip && trip.status != "started" && trip.status != "finished" && trip.status != "cancelled" && props.route.params.type == 'betweenCities' && trip.driver_city_trip && trip.driver_city_trip.driver && trip.driver_city_trip.driver.current_lat
                        &&
                        <Marker
                            coordinate={{
                                latitude: trip.driver_city_trip && trip.driver_city_trip.driver && trip.driver_city_trip.driver.current_lat,
                                longitude: trip.driver_city_trip && trip.driver_city_trip.driver && trip.driver_city_trip.driver.current_lng
                            }}
                            style={{ width: 20, height: 20 }}
                            resizeMode="contain"
                            image={require('../images/carxx.png')}
                        />
                    }
                    {trip && trip.status != "started" && trip.status != "finished" && trip.status != "cancelled" && props.route.params.type == 'packages' && trip.driver_package_trip && trip.driver_package_trip.driver && trip.driver_package_trip.driver.current_lat
                        &&
                        <Marker
                            coordinate={{
                                latitude: trip.driver_package_trip && trip.driver_package_trip.driver && trip.driver_package_trip.driver.current_lat,
                                longitude: trip.driver_package_trip && trip.driver_package_trip.driver && trip.driver_package_trip.driver.current_lng
                            }}
                            style={{ width: 20, height: 20 }}
                            resizeMode="contain"
                            image={require('../images/carxx.png')}
                        />
                    }
                    <Marker
                        coordinate={{
                            latitude: origin.latitude,
                            longitude: origin.longitude
                        }}
                        style={{ width: 20, height: 20 }}
                        image={require('../images/Group-288.png')}
                    />
                    <Marker
                        coordinate={{
                            latitude: destination.latitude,
                            longitude: destination.longitude
                        }}
                        style={{ width: 20, height: 30 }}
                        image={require('../images/Group-277.png')}
                    />
                    {trip && trip.status != "started" && trip.status != "finished" && trip.status != "cancelled" && trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lat
                        &&
                        <MapViewDirections
                            origin={{
                                latitude:
                                    trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lat,
                                longitude:
                                    trip.driver_offer && trip.driver_offer.driver && trip.driver_offer.driver.current_lng
                            }}
                            destination={origin}
                            // origin={origin}
                            // destination={destination}
                            apikey={'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'}
                            strokeWidth={3}
                            strokeColor={DarkBlue}
                        />
                    }
                    {trip && trip.status != "started" && trip.status != "finished" && trip.status != "cancelled" && props.route.params.type == 'orderVip' && trip.driver_vip_trip && trip.driver_vip_trip.driver && trip.driver_vip_trip.driver.current_lat
                        &&
                        <MapViewDirections
                            origin={{
                                latitude:
                                    trip.driver_vip_trip && trip.driver_vip_trip.driver && trip.driver_vip_trip.driver.current_lat,
                                longitude:
                                    trip.driver_vip_trip && trip.driver_vip_trip.driver && trip.driver_vip_trip.driver.current_lng
                            }}
                            destination={origin}
                            // origin={origin}
                            // destination={destination}
                            apikey={'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'}
                            strokeWidth={3}
                            strokeColor={DarkBlue}
                        />
                    }
                    {trip && trip.status != "started" && trip.status != "finished" && trip.status != "cancelled" && props.route.params.type == 'betweenCities' && trip.driver_city_trip && trip.driver_city_trip.driver && trip.driver_city_trip.driver.current_lat
                        &&
                        <MapViewDirections
                            origin={{
                                latitude:
                                    trip.driver_city_trip && trip.driver_city_trip.driver && trip.driver_city_trip.driver.current_lat,
                                longitude:
                                    trip.driver_city_trip && trip.driver_city_trip.driver && trip.driver_city_trip.driver.current_lng
                            }}
                            destination={origin}
                            // origin={origin}
                            // destination={destination}
                            apikey={'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'}
                            strokeWidth={3}
                            strokeColor={DarkBlue}
                        />
                    }
                    {trip && trip.status != "started" && trip.status != "finished" && trip.status != "cancelled" && props.route.params.type == 'packages' && trip.driver_package_trip && trip.driver_package_trip.driver && trip.driver_package_trip.driver.current_lat
                        &&
                        <MapViewDirections
                            origin={{
                                latitude:
                                    trip.driver_package_trip && trip.driver_package_trip.driver && trip.driver_package_trip.driver.current_lat,
                                longitude:
                                    trip.driver_package_trip && trip.driver_package_trip.driver && trip.driver_package_trip.driver.current_lng
                            }}
                            destination={origin}
                            // origin={origin}
                            // destination={destination}
                            apikey={'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'}
                            strokeWidth={3}
                            strokeColor={DarkBlue}
                        />
                    }
                    <MapViewDirections
                        origin={origin}
                        destination={destination}
                        apikey={'AIzaSyAPmQveTdRRJXd6sIn66AwqtXOfedshZ3I'}
                        strokeWidth={3}
                        strokeColor={DarkYellow}
                    />
                </MapView>

                <View style={{ width: '100%', height: screenHeight / 1.7, alignSelf: 'center', position: 'absolute', bottom: 0, zIndex: 1000, backgroundColor: White }}>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        {trip.driver_offer && trip.driver_offer.driver
                            ?
                            <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'flex-start', paddingVertical: screenHeight / 50, alignSelf: 'center' }}>
                                <View style={{ width: screenWidth / 8, height: screenWidth / 8, marginEnd: '2%' }}>
                                    <Image source={trip.driver_offer.driver.image ? { uri: trip.driver_offer.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '100%' }} />
                                </View>
                                <View style={{ width: '60%', alignItems: 'flex-start', alignSelf: 'center' }}>
                                    {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text> */}
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_offer.driver.name}</Text>
                                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                        <Image source={{ uri: trip.driver_offer.driver.vehicle.car_brand.image }} style={{ resizeMode: 'contain', width: screenWidth / 15, height: screenWidth / 15, marginEnd: 5, alignSelf: 'center' }} />
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_offer.driver.vehicle.car_model && trip.driver_offer.driver.vehicle.car_model.name} - {trip.driver_offer.driver.vehicle.year && trip.driver_offer.driver.vehicle.year}</Text>
                                        <View style={{ width: screenWidth / 22, height: screenWidth / 22, borderRadius: screenWidth / 24, borderWidth: .8, borderColor: MediumGrey, backgroundColor: trip.driver_offer.driver.vehicle.color && trip.driver_offer.driver.vehicle.color, marginStart: 5 }}></View>
                                    </View>
                                    <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 30, alignItems: 'center', justifyContent: 'space-between' }}>
                                        <View style={{ flexDirection: 'row', width: '80%', height: screenHeight / 30, alignItems: 'center' }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{trip.driver_offer.driver.vehicle.plate_number + ' ' + trip.driver_offer.driver.vehicle.plate_letter_right + ' ' + trip.driver_offer.driver.vehicle.plate_letter_middle + ' ' + trip.driver_offer.driver.vehicle.plate_letter_left}</Text>
                                            <View style={{ height: '80%', alignSelf: 'center', width: 1, backgroundColor: MediumGrey, marginHorizontal: 10 }}></View>
                                            <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '5%', marginEnd: 5, alignSelf: 'center' }} />
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {trip.driver_offer.driver.avg_rating}</Text>
                                        </View>
                                        {/* <Button transparent style={{ backgroundColor: DarkBlue, width: '30%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}>{strings('lang.rate')}</Text>
                                </Button> */}
                                    </View>
                                </View>
                                {trip.status != 'finished' && trip.status != "cancelled"
                                    ?
                                    <View style={{ width: '20%', alignItems: 'center', justifyContent: 'space-between' }}>
                                        <Button transparent
                                            onPress={() => {
                                                Linking.openURL(`tel:${'+966'}${trip.driver_offer.driver.mobile_number.startsWith("0") ?
                                                    trip.driver_offer.driver.mobile_number.slice("0".length)
                                                    :
                                                    trip.driver_offer.driver.mobile_number}`)
                                            }}
                                            style={{ backgroundColor: WhiteGreen, width: '100%', flexDirection: 'row', height: screenHeight / 30, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                            <Image source={require('../images/call.png')} style={{ resizeMode: 'contain', width: '10%', marginHorizontal: '2%' }} />
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 55, color: Black, marginHorizontal: 2 }}>{strings('lang.Call')}</Text>
                                        </Button>

                                        <Button transparent
                                            onPress={() => {
                                                // Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${trip.driver_offer.driver.mobile_number.startsWith("0") ?
                                                //     trip.driver_offer.driver.mobile_number.slice("0".length)
                                                //     :
                                                //     trip.driver_offer.driver.mobile_number}`)
                                                setNewMessgae(false)
                                                props.navigation.push('Chat', { receiver_type: 'driver', receiver_id: trip.driver_offer.driver.id, subject_id: trip.id, subject_type: 'DailyTrip', screen: 'DetailsTrip', chat: true, trip: trip })
                                            }}
                                            style={{ backgroundColor: White, width: '100%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, marginTop: '5%', height: screenHeight / 30, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                            {newMessgae
                                                ?
                                                <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: Red, position: 'absolute', top: -2, end: 0 }}></View>
                                                :
                                                <></>
                                            }
                                            <Image source={require('../images/Group98722.png')} style={{ resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 55, color: Black, marginHorizontal: 2 }}>{strings('lang.Chatt')}</Text>
                                        </Button>
                                    </View>
                                    :
                                    <></>
                                }
                            </View>
                            :
                            props.route.params.type == 'orderVip' && trip.driver_vip_trip && trip.driver_vip_trip.driver
                                ?
                                <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'flex-start', paddingVertical: screenHeight / 50, alignSelf: 'center' }}>
                                    <View style={{ width: screenWidth / 8, height: screenWidth / 8, marginEnd: '2%' }}>
                                        <Image source={trip.driver_vip_trip.driver.image ? { uri: trip.driver_vip_trip.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '100%' }} />
                                    </View>
                                    <View style={{ width: '60%', alignItems: 'flex-start', alignSelf: 'center' }}>
                                        {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text> */}
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_vip_trip.driver.name}</Text>
                                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                            <Image source={{ uri: trip.driver_vip_trip.driver.vehicle.car_brand.image }} style={{ resizeMode: 'contain', width: screenWidth / 15, height: screenWidth / 15, marginEnd: 5, alignSelf: 'center' }} />
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_vip_trip.driver.vehicle.car_model && trip.driver_vip_trip.driver.vehicle.car_model.name} - {trip.driver_vip_trip.driver.vehicle.year && trip.driver_vip_trip.driver.vehicle.year}</Text>
                                            <View style={{ width: screenWidth / 22, height: screenWidth / 22, borderRadius: screenWidth / 24, borderWidth: .8, borderColor: MediumGrey, backgroundColor: trip.driver_vip_trip.driver.vehicle.color && trip.driver_vip_trip.driver.vehicle.color, marginStart: 5 }}></View>
                                        </View>
                                        <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 30, alignItems: 'center', justifyContent: 'space-between' }}>
                                            <View style={{ flexDirection: 'row', width: '80%', height: screenHeight / 30, alignItems: 'center' }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{trip.driver_vip_trip.driver.vehicle.plate_number + ' ' + trip.driver_vip_trip.driver.vehicle.plate_letter_right + ' ' + trip.driver_vip_trip.driver.vehicle.plate_letter_middle + ' ' + trip.driver_vip_trip.driver.vehicle.plate_letter_left}</Text>
                                                <View style={{ height: '80%', alignSelf: 'center', width: 1, backgroundColor: MediumGrey, marginHorizontal: 10 }}></View>
                                                <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '10%', alignSelf: 'center' }} />
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {trip.driver_vip_trip.driver.avg_rating}</Text>
                                            </View>
                                            {/* <Button transparent style={{ backgroundColor: DarkBlue, width: '30%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}>{strings('lang.rate')}</Text>
                                    </Button> */}
                                        </View>
                                    </View>
                                    {trip.status != 'finished' && trip.status != "cancelled"
                                        ?
                                        <View style={{ width: '20%', alignItems: 'center', justifyContent: 'space-between' }}>
                                            <Button transparent
                                                onPress={() => {
                                                    Linking.openURL(`tel:${'+966'}${trip.driver_vip_trip.driver.mobile_number.startsWith("0") ?
                                                        trip.driver_vip_trip.driver.mobile_number.slice("0".length)
                                                        :
                                                        trip.driver_vip_trip.driver.mobile_number
                                                        }`)
                                                }}
                                                style={{ backgroundColor: WhiteGreen, width: '100%', flexDirection: 'row', height: screenHeight / 30, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                                <Image source={require('../images/call.png')} style={{ resizeMode: 'contain', width: '12%', marginHorizontal: '2%' }} />
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 55, color: Black, marginHorizontal: 2 }}>{strings('lang.Call')}</Text>
                                            </Button>

                                            <Button transparent
                                                onPress={() => {
                                                    // Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${trip.driver_vip_trip.driver.mobile_number.startsWith("0") ?
                                                    //     trip.driver_vip_trip.driver.mobile_number.slice("0".length)
                                                    //     :
                                                    //     trip.driver_vip_trip.driver.mobile_number
                                                    //     }`)
                                                    setNewMessgae(false)
                                                    props.navigation.push('Chat', { receiver_type: 'driver', receiver_id: trip.driver_vip_trip.driver.id, subject_id: trip.id, subject_type: 'VipTrip', screen: 'DetailsTrip', chat: true, trip: trip })
                                                }}
                                                style={{ backgroundColor: White, width: '100%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, marginTop: '5%', height: screenHeight / 30, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                                {newMessgae
                                                    ?
                                                    <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: Red, position: 'absolute', top: -2, end: 0 }}></View>
                                                    :
                                                    <></>
                                                }
                                                <Image source={require('../images/Group98722.png')} style={{ resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 55, color: Black, marginHorizontal: 2 }}>{strings('lang.Chatt')}</Text>
                                            </Button>
                                        </View>
                                        :
                                        <></>
                                    }
                                </View>
                                :
                                props.route.params.type == 'betweenCities' && trip.driver_city_trip && trip.driver_city_trip.driver && trip.driver_city_trip.driver.vehicle
                                    ?
                                    <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'flex-start', paddingVertical: screenHeight / 50, alignSelf: 'center' }}>
                                        <View style={{ width: screenWidth / 8, height: screenWidth / 8, marginEnd: '2%' }}>
                                            <Image source={trip.driver_city_trip.driver.image ? { uri: trip.driver_city_trip.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '100%' }} />
                                        </View>
                                        <View style={{ width: '60%', alignItems: 'flex-start', alignSelf: 'center' }}>
                                            {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text> */}
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_city_trip.driver.name}</Text>
                                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                <Image source={{ uri: trip.driver_city_trip.driver.vehicle.car_brand.image }} style={{ resizeMode: 'contain', width: screenWidth / 15, height: screenWidth / 15, marginEnd: 5, alignSelf: 'center' }} />
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_city_trip.driver.vehicle.car_model && trip.driver_city_trip.driver.vehicle.car_model.name} - {trip.driver_city_trip.driver.vehicle.year && trip.driver_city_trip.driver.vehicle.year}</Text>
                                                <View style={{ width: screenWidth / 22, height: screenWidth / 22, borderRadius: screenWidth / 24, borderWidth: .8, borderColor: MediumGrey, backgroundColor: trip.driver_city_trip.driver.vehicle.color && trip.driver_city_trip.driver.vehicle.color, marginStart: 5 }}></View>
                                            </View>
                                            <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 30, alignItems: 'center', justifyContent: 'space-between' }}>
                                                <View style={{ flexDirection: 'row', width: '80%', height: screenHeight / 30, alignItems: 'center' }}>
                                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{trip.driver_city_trip.driver.vehicle.plate_number + ' ' + trip.driver_city_trip.driver.vehicle.plate_letter_right + ' ' + trip.driver_city_trip.driver.vehicle.plate_letter_middle + ' ' + trip.driver_city_trip.driver.vehicle.plate_letter_left}</Text>
                                                    <View style={{ height: '80%', alignSelf: 'center', width: 1, backgroundColor: MediumGrey, marginHorizontal: 10 }}></View>
                                                    <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '10%', alignSelf: 'center' }} />
                                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {trip.driver_city_trip.driver.avg_rating}</Text>
                                                </View>
                                                {/* <Button transparent style={{ backgroundColor: DarkBlue, width: '30%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}>{strings('lang.rate')}</Text>
                                    </Button> */}
                                            </View>
                                        </View>
                                        {trip.status != 'finished' && trip.status != "cancelled"
                                            ?
                                            <View style={{ width: '20%', alignItems: 'center', justifyContent: 'space-between' }}>
                                                <Button transparent
                                                    onPress={() => {
                                                        Linking.openURL(`tel:${'+966'}${trip.driver_city_trip.driver.mobile_number.startsWith("0") ?
                                                            trip.driver_city_trip.driver.mobile_number.slice("0".length)
                                                            :
                                                            trip.driver_city_trip.driver.mobile_number
                                                            }`)
                                                    }}
                                                    style={{ backgroundColor: WhiteGreen, width: '100%', flexDirection: 'row', height: screenHeight / 30, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                                    <Image source={require('../images/call.png')} style={{ resizeMode: 'contain', width: '12%', marginHorizontal: '2%' }} />
                                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 55, color: Black, marginHorizontal: 2 }}>{strings('lang.Call')}</Text>
                                                </Button>

                                                <Button transparent
                                                    onPress={() => {
                                                        // Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${trip.driver_city_trip.driver.mobile_number.startsWith("0") ?
                                                        //     trip.driver_city_trip.driver.mobile_number.slice("0".length)
                                                        //     :
                                                        //     trip.driver_city_trip.driver.mobile_number}`)
                                                        setNewMessgae(false)
                                                        props.navigation.push('Chat', { receiver_type: 'driver', receiver_id: trip.driver_city_trip.driver.id, subject_id: trip.id, subject_type: 'CityTrip', screen: 'DetailsTrip', chat: true, trip: trip })
                                                    }}
                                                    style={{ backgroundColor: White, width: '100%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, marginTop: '5%', height: screenHeight / 30, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                                    {newMessgae
                                                        ?
                                                        <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: Red, position: 'absolute', top: -2, end: 0 }}></View>
                                                        :
                                                        <></>
                                                    }
                                                    <Image source={require('../images/Group98722.png')} style={{ resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 55, color: Black, marginHorizontal: 2 }}>{strings('lang.Chatt')}</Text>
                                                </Button>
                                            </View>
                                            :
                                            <></>
                                        }
                                    </View>
                                    :
                                    props.route.params.type == 'packages' && trip.driver_package_trip && trip.driver_package_trip.driver && trip.driver_package_trip.driver.vehicle
                                        ?
                                        <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'flex-start', paddingVertical: screenHeight / 50, alignSelf: 'center' }}>
                                            <View style={{ width: screenWidth / 8, height: screenWidth / 8, marginEnd: '2%' }}>
                                                <Image source={trip.driver_package_trip.driver.image ? { uri: trip.driver_package_trip.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '100%' }} />
                                            </View>
                                            <View style={{ width: '60%', alignItems: 'flex-start', alignSelf: 'center' }}>
                                                {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text> */}
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_package_trip.driver.name}</Text>
                                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                    <Image source={{ uri: trip.driver_package_trip.driver.vehicle.car_brand.image }} style={{ resizeMode: 'contain', width: screenWidth / 15, height: screenWidth / 15, marginEnd: 5, alignSelf: 'center' }} />
                                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_package_trip.driver.vehicle.car_model && trip.driver_package_trip.driver.vehicle.car_model.name} - {trip.driver_package_trip.driver.vehicle.year && trip.driver_package_trip.driver.vehicle.year}</Text>
                                                    <View style={{ width: screenWidth / 22, height: screenWidth / 22, borderRadius: screenWidth / 24, borderWidth: .8, borderColor: MediumGrey, backgroundColor: trip.driver_package_trip.driver.vehicle.color && trip.driver_package_trip.driver.vehicle.color, marginStart: 5 }}></View>
                                                </View>
                                                <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 30, alignItems: 'center', justifyContent: 'space-between' }}>
                                                    <View style={{ flexDirection: 'row', width: '80%', height: screenHeight / 30, alignItems: 'center' }}>
                                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{trip.driver_package_trip.driver.vehicle.plate_number + ' ' + trip.driver_package_trip.driver.vehicle.plate_letter_right + ' ' + trip.driver_package_trip.driver.vehicle.plate_letter_middle + ' ' + trip.driver_package_trip.driver.vehicle.plate_letter_left}</Text>
                                                        <View style={{ height: '80%', alignSelf: 'center', width: 1, backgroundColor: MediumGrey, marginHorizontal: 10 }}></View>
                                                        <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '10%', alignSelf: 'center' }} />
                                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {trip.driver_package_trip.driver.avg_rating}</Text>
                                                    </View>
                                                    {/* <Button transparent style={{ backgroundColor: DarkBlue, width: '30%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}>{strings('lang.rate')}</Text>
                                    </Button> */}
                                                </View>
                                            </View>
                                            {trip.status != 'finished' && trip.status != "cancelled"
                                                ?
                                                <View style={{ width: '20%', alignItems: 'center', justifyContent: 'space-between' }}>
                                                    <Button transparent
                                                        onPress={() => {
                                                            Linking.openURL(`tel:${'+966'}${trip.driver_package_trip.driver.mobile_number.startsWith("0") ?
                                                                trip.driver_package_trip.driver.mobile_number.slice("0".length)
                                                                :
                                                                trip.driver_package_trip.driver.mobile_number
                                                                }`)
                                                        }}
                                                        style={{ backgroundColor: WhiteGreen, width: '100%', flexDirection: 'row', height: screenHeight / 30, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                                        <Image source={require('../images/call.png')} style={{ resizeMode: 'contain', width: '12%', marginHorizontal: '2%' }} />
                                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 55, color: Black, marginHorizontal: 2 }}>{strings('lang.Call')}</Text>
                                                    </Button>

                                                    <Button transparent
                                                        onPress={() => {
                                                            // Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${trip.driver_package_trip.driver.mobile_number.startsWith("0") ?
                                                            //     trip.driver_package_trip.driver.mobile_number.slice("0".length)
                                                            //     :
                                                            //     trip.driver_package_trip.driver.mobile_number}`)
                                                            setNewMessgae(false)
                                                            props.navigation.push('Chat', { receiver_type: 'driver', receiver_id: trip.driver_package_trip.driver.id, subject_id: trip.id, subject_type: 'UserPackage', screen: 'DetailsTrip', chat: true, trip: trip })
                                                        }}
                                                        style={{ backgroundColor: White, width: '100%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, marginTop: '5%', height: screenHeight / 30, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                                        {newMessgae
                                                            ?
                                                            <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: Red, position: 'absolute', top: -2, end: 0 }}></View>
                                                            :
                                                            <></>
                                                        }
                                                        <Image source={require('../images/Group98722.png')} style={{ resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 55, color: Black, marginHorizontal: 2 }}>{strings('lang.Chatt')}</Text>
                                                    </Button>
                                                </View>
                                                :
                                                <></>
                                            }
                                        </View>
                                        :
                                        <></>
                        }
                        <View style={{ width: '90%', height: .5, backgroundColor: MediumGrey, alignSelf: 'center', marginVertical: 0 }}></View>


                        {trip.notes
                            ?
                            <>
                                <View style={{ flexDirection: 'row', minHeight: screenHeight / 16, width: '100%', alignItems: 'center', paddingVertical: 5 }}>
                                    <Image source={require('../images/Group98722.png')} style={{ height: '30%', resizeMode: 'contain', width: '10%', }} />
                                    <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black, marginEnd: screenWidth / 5, textAlign: 'left' }}>{trip.notes}</Text>
                                </View>
                                <View style={{ width: '90%', height: .5, backgroundColor: MediumGrey, alignSelf: 'center', marginVertical: 0 }}></View>
                            </>
                            :
                            <></>
                        }

                        {trip.date
                            ?
                            <>
                                <View style={{ flexDirection: 'row', height: screenHeight / 16, width: '100%', alignItems: 'center', }}>
                                    <Image source={require('../images/222.png')} style={{ height: '30%', resizeMode: 'contain', width: '10%', }} />
                                    <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black }}>{trip.date} {' '} {trip.time}</Text>
                                </View>
                                <View style={{ width: '90%', height: .5, backgroundColor: MediumGrey, alignSelf: 'center', marginVertical: 0 }}></View>
                            </>
                            :
                            <></>
                        }

                        {trip.price
                            ?
                            <>
                                <View style={{ flexDirection: 'row', height: screenHeight / 16, width: '100%', alignItems: 'center', }}>
                                    <Image source={require('../images/Group9877.png')} style={{ height: '30%', resizeMode: 'contain', width: '10%', }} />
                                    <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black }}>{trip.price} {strings('lang.SR')}</Text>
                                </View>

                                <View style={{ width: '90%', height: .5, backgroundColor: MediumGrey, alignSelf: 'center', marginVertical: 0 }}></View>
                            </>
                            :
                            <></>
                        }
                        {/* <View style={{ flexDirection: 'row', width: '100%', height: screenHeight / 6 }}>
                        <View style={{ width: '10%', alignItems: 'center', height: '100%', marginTop: screenHeight / 90 }}>
                            <View style={{ height: screenHeight / 40, width: screenHeight / 40, }}>
                                <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                            </View>

                            <View style={{ height: screenHeight / 17, width: 1, borderStyle: 'dashed', borderWidth: 1, borderColor: Green, zIndex: 0, }}></View>

                            <View style={{ height: screenHeight / 40, width: screenHeight / 40, }}>
                                <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                            </View>
                        </View>

                        <View style={{ width: '90%', alignSelf: 'flex-end', height: '99%', marginTop: screenHeight / 50 }}>
                            <View
                                style={styles.contentContainer}>
                                <View style={styles.textInput}>
                                    <Text style={{
                                        color: Black, textAlignVertical: 'center',
                                        fontFamily: appFont,
                                        fontSize: screenWidth / 32,
                                    }}>
                                        {strings('lang.destination')}
                                    </Text>
                                    <Text style={{
                                        color: MediumGrey, textAlignVertical: 'center',
                                        fontFamily: appFont,
                                        fontSize: screenWidth / 32,
                                    }}>
                                        هذا النص هو مثال لنص يمكن أن يستبدل في نفس
                                    </Text>
                                </View>
                            </View>

                            <View
                                style={styles.contentContainer}>
                                <View style={styles.textInput}>
                                    <Text style={{
                                        color: Black, textAlignVertical: 'center',
                                        fontFamily: appFont,
                                        fontSize: screenWidth / 32,
                                    }}>
                                        {strings('lang.destination')}
                                    </Text>
                                    <Text style={{
                                        color: MediumGrey, textAlignVertical: 'center',
                                        fontFamily: appFont,
                                        fontSize: screenWidth / 32,
                                    }}>
                                        هذا النص هو مثال لنص يمكن أن يستبدل في نفس
                                    </Text>
                                </View>

                            </View>

                        </View>
                    </View> */}


                        <View
                            style={[styles.contentContainer, { width: '100%', justifyContent: 'flex-start', alignItems: 'center', marginVertical: '1%', height: props.route.params.type == 'betweenCities' ? screenHeight / 23 : screenHeight / 17 }]}>
                            <View style={{ width: '8%', alignItems: 'center', justifyContent: 'flex-start' }}>
                                <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                            </View>
                            <View style={{ width: '90%', height: '90%' }}>
                                {props.route.params.type == 'betweenCities'
                                    ?
                                    <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.from_city ? trip.from_city.name : ''}</Text>
                                    :
                                    props.route.params.type == 'packages'
                                        ?
                                        trip.destinations &&
                                        <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.destinations[0].from_address_id ? trip.destinations[0].address_from.address : trip.destinations[0].from_address}</Text>
                                        :
                                        <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.from_address_id ? trip.address_from.address : trip.from_address}</Text>
                                }
                            </View>
                        </View>
                        <View
                            style={[styles.contentContainer, { width: '100%', justifyContent: 'flex-start', alignItems: 'center', marginTop: screenHeight / 100, height: props.route.params.type == 'betweenCities' ? screenHeight / 23 : screenHeight / 17 }]}>
                            <View style={{ width: '8%', alignItems: 'center', justifyContent: 'flex-start' }}>
                                <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                            </View>
                            <View style={{ width: '90%', height: '90%' }}>
                                {props.route.params.type == 'betweenCities'
                                    ?
                                    <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.to_city ? trip.to_city.name : ''}</Text>
                                    :
                                    props.route.params.type == 'packages'
                                        ?
                                        trip.destinations &&
                                        <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.destinations[0].to_address_id ? trip.destinations[0].address_to.address : trip.destinations[0].to_address}</Text>
                                        :
                                        <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{trip.to_address_id ? trip.address_to.address : trip.to_address}</Text>
                                }
                                {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginStart: '1%', alignSelf: 'flex-start' }}>هذا النص هو مثال لنص يمكن أن يستبدل في نفس</Text> */}
                            </View>
                        </View>

                        {trip.status != 'finished' && trip.status != "cancelled"
                            ?
                            <Text style={{
                                fontFamily: appFontBold,
                                fontSize: screenWidth / 33,
                                color: Red,
                                alignSelf: 'center',
                                marginVertical: '2%'
                            }}>{strings('lang.Pleasecheckyourpersonalaccessories')}</Text>
                            :
                            <></>
                        }



                        {props.route.params.type == 'orderVip' || props.route.params.type == 'betweenCities'
                            ?
                            <View style={styles.buttonsContainer}>
                                {trip.status == "waiting_for_payment" && trip.price
                                    ?
                                    <Button onPress={() => { props.navigation.navigate('PaymentMethods', { subscription: false, type: props.route.params.type, item: trip }) }} style={styles.acceptButton}>
                                        <Text style={styles.buttonText}>{strings('lang.Pay')}</Text>
                                    </Button>
                                    :
                                    <></>
                                }
                                {trip.status != 'finished' && trip.status != "cancelled"
                                    ?
                                    <Button onPress={() => { refRBSheetReasons.current.open() }} style={[styles.button1, { width: (trip.status == "waiting_for_payment" && trip.price) ? '30%' : '100%' }]}>
                                        <Text style={styles.buttonText}>{strings('lang.cancel')}</Text>
                                    </Button>
                                    :
                                    <></>
                                }

                            </View>
                            :
                            <></>
                        }
                        {/* {trip.status == 'finished'
                        &&
                        <View
                            style={{
                                width: '100%',
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignSelf: 'center',
                                marginTop: screenHeight / 50,
                            }}
                        >
                            <Button
                                onPress={() => { }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '95%',
                                    height: screenHeight / 20,
                                    backgroundColor: DarkBlue,
                                    borderRadius: 100,
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 28,
                                        color: White,
                                    }}
                                >
                                    {strings('lang.Contactsupport')}
                                </Text>
                            </Button>
                        </View>
                    } */}
                        <View style={{ height: screenHeight / 40 }}></View>
                    </ScrollView>
                </View>

                {/* rating  */}
                <Modalize
                    ref={modalizeRefRat}
                    contentRef={contentRefRat}
                    disableScrollIfPossible={true}
                    modalHeight={screenHeight / 1.2}
                    // modalTopOffset={screenHeight / 1}
                    // alwaysOpen={screenHeight / 2.2}
                    // snapPoint={100}
                    handlePosition={'inside'}
                    // adjustToContentHeight={true}
                    // useNativeDriver={false}
                    panGestureEnabled={false}
                    keyboardAvoidingBehavior={'padding'}
                    // dragToss={0.05}
                    // threshold={150}
                    // velocity={2800}
                    // withReactModal={false}
                    withOverlay={false}
                    withHandle={true}
                    // scrollViewProps={screenHeight}
                    modalStyle={styles.modalize__content}
                    handleStyle={styles.handle__shape}
                    closeOnOverlayTap={true}
                    tapGestureEnabled={false}
                    panGestureComponentEnabled={true}
                    closeSnapPointStraightEnabled={false}
                >
                    <ScrollView
                        style={{ width: '100%', alignSelf: 'center', paddingBottom: '3%' }}
                        showsVerticalScrollIndicator={false}
                    >

                        <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 16, marginTop: '5%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', }}>
                            <Pressable onPress={() => { modalizeRefRat.current.close(); props.navigation.navigate('Home') }}>
                                <Image source={require('../images/xx.png')} style={{ width: 10, height: 10, resizeMode: 'contain' }} />
                            </Pressable>
                        </View>

                        {trip.driver_offer && trip.driver_offer.driver
                            ?
                            <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'center', paddingVertical: screenHeight / 50, alignSelf: 'center', borderBottomWidth: .8, borderColor: MediumGrey }}>
                                <View style={{ width: '20%', height: screenHeight / 15 }}>
                                    <Image source={trip.driver_offer.driver.image ? { uri: trip.driver_offer.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '90%' }} />
                                </View>
                                <View style={{ width: '80%', alignItems: 'flex-start', alignSelf: 'center' }}>
                                    {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text> */}
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_offer.driver.name}</Text>
                                    <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 30, alignItems: 'center', justifyContent: 'space-between' }}>
                                        <View style={{ flexDirection: 'row', width: '80%', height: screenHeight / 30, alignItems: 'center' }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{trip.driver_offer.driver.vehicle.plate_number + ' ' + trip.driver_offer.driver.vehicle.plate_letter_right + ' ' + trip.driver_offer.driver.vehicle.plate_letter_middle + ' ' + trip.driver_offer.driver.vehicle.plate_letter_left}</Text>
                                            <View style={{ height: '80%', alignSelf: 'center', width: 1, backgroundColor: MediumGrey, marginHorizontal: 10 }}></View>
                                            <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '10%', alignSelf: 'center' }} />
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {trip.driver_offer.driver.avg_rating}</Text>
                                        </View>
                                        {/* <Button transparent style={{ backgroundColor: DarkBlue, width: '30%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}>{strings('lang.rate')}</Text>
                                </Button> */}
                                    </View>
                                </View>
                            </View>
                            :
                            props.route.params.type == 'orderVip' && trip.driver_vip_trip && trip.driver_vip_trip.driver
                                ?
                                <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'center', paddingVertical: screenHeight / 50, alignSelf: 'center', borderBottomWidth: .8, borderColor: MediumGrey }}>
                                    <View style={{ width: '20%', height: screenHeight / 15 }}>
                                        <Image source={trip.driver_vip_trip.driver.image ? { uri: trip.driver_vip_trip.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '90%' }} />
                                    </View>
                                    <View style={{ width: '80%', alignItems: 'flex-start', alignSelf: 'center' }}>
                                        {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text> */}
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_vip_trip.driver.name}</Text>
                                        <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 30, alignItems: 'center', justifyContent: 'space-between' }}>
                                            <View style={{ flexDirection: 'row', width: '80%', height: screenHeight / 30, alignItems: 'center' }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{trip.driver_vip_trip.driver.vehicle.plate_number + ' ' + trip.driver_vip_trip.driver.vehicle.plate_letter_right + ' ' + trip.driver_vip_trip.driver.vehicle.plate_letter_middle + ' ' + trip.driver_vip_trip.driver.vehicle.plate_letter_left}</Text>
                                                <View style={{ height: '80%', alignSelf: 'center', width: 1, backgroundColor: MediumGrey, marginHorizontal: 10 }}></View>
                                                <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '10%', alignSelf: 'center' }} />
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {trip.driver_vip_trip.driver.avg_rating}</Text>
                                            </View>
                                            {/* <Button transparent style={{ backgroundColor: DarkBlue, width: '30%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}>{strings('lang.rate')}</Text>
                                    </Button> */}
                                        </View>
                                    </View>
                                </View>
                                :
                                props.route.params.type == 'betweenCities' && trip.driver_city_trip && trip.driver_city_trip.driver && trip.driver_city_trip.driver.vehicle
                                    ?
                                    <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'center', paddingVertical: screenHeight / 50, alignSelf: 'center', borderBottomWidth: .8, borderColor: MediumGrey }}>
                                        <View style={{ width: '20%', height: screenHeight / 15 }}>
                                            <Image source={trip.driver_city_trip.driver.image ? { uri: trip.driver_city_trip.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '90%' }} />
                                        </View>
                                        <View style={{ width: '80%', alignItems: 'flex-start', alignSelf: 'center' }}>
                                            {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text> */}
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_city_trip.driver.name}</Text>
                                            <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 30, alignItems: 'center', justifyContent: 'space-between' }}>
                                                <View style={{ flexDirection: 'row', width: '80%', height: screenHeight / 30, alignItems: 'center' }}>
                                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{trip.driver_city_trip.driver.vehicle.plate_number + ' ' + trip.driver_city_trip.driver.vehicle.plate_letter_right + ' ' + trip.driver_city_trip.driver.vehicle.plate_letter_middle + ' ' + trip.driver_city_trip.driver.vehicle.plate_letter_left}</Text>
                                                    <View style={{ height: '80%', alignSelf: 'center', width: 1, backgroundColor: MediumGrey, marginHorizontal: 10 }}></View>
                                                    <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '10%', alignSelf: 'center' }} />
                                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {trip.driver_city_trip.driver.avg_rating}</Text>
                                                </View>
                                                {/* <Button transparent style={{ backgroundColor: DarkBlue, width: '30%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}>{strings('lang.rate')}</Text>
                                    </Button> */}
                                            </View>
                                        </View>
                                    </View>
                                    :
                                    props.route.params.type == 'packages' && trip.driver_package_trip && trip.driver_package_trip.driver && trip.driver_package_trip.driver.vehicle
                                        ?
                                        <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'center', paddingVertical: screenHeight / 50, alignSelf: 'center', borderBottomWidth: .8, borderColor: MediumGrey }}>
                                            <View style={{ width: '20%', height: screenHeight / 15 }}>
                                                <Image source={trip.driver_package_trip.driver.image ? { uri: trip.driver_package_trip.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '90%' }} />
                                            </View>
                                            <View style={{ width: '80%', alignItems: 'flex-start', alignSelf: 'center' }}>
                                                {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text> */}
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{trip.driver_package_trip.driver.name}</Text>
                                                <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 30, alignItems: 'center', justifyContent: 'space-between' }}>
                                                    <View style={{ flexDirection: 'row', width: '80%', height: screenHeight / 30, alignItems: 'center' }}>
                                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{trip.driver_package_trip.driver.vehicle.plate_number + ' ' + trip.driver_package_trip.driver.vehicle.plate_letter_right + ' ' + trip.driver_package_trip.driver.vehicle.plate_letter_middle + ' ' + trip.driver_package_trip.driver.vehicle.plate_letter_left}</Text>
                                                        <View style={{ height: '80%', alignSelf: 'center', width: 1, backgroundColor: MediumGrey, marginHorizontal: 10 }}></View>
                                                        <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '10%', alignSelf: 'center' }} />
                                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {trip.driver_package_trip.driver.avg_rating}</Text>
                                                    </View>
                                                    {/* <Button transparent style={{ backgroundColor: DarkBlue, width: '30%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}>{strings('lang.rate')}</Text>
                                    </Button> */}
                                                </View>
                                            </View>
                                        </View>
                                        :
                                        <></>
                        }

                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 25, color: Black, alignSelf: 'center', marginTop: '5%' }}>{strings('lang.Giveyourratingtothedriver')}</Text>
                        <View style={{ width: '95%', alignItems: 'center', justifyContent: 'center' }}>
                            <StarRating
                                disabled={false}
                                maxStars={5}
                                starSize={screenHeight / 25}
                                starStyle={{ marginEnd: 2, }}
                                rating={starCount}
                                containerStyle={{ alignContent: 'flex-start', }}
                                buttonStyle={{ flexDirection: 'row', left: 'auto' }}
                                // emptyStar={'ios-star-outline'}
                                // fullStar={'ios-star'}
                                // iconSet={'Ionicons'}
                                emptyStarColor={DarkYellow}
                                fullStarColor={DarkYellow}
                                selectedStar={(rating) => setStarCount(rating)}
                            />
                        </View>

                        <View style={styles.input1}>
                            <Textarea
                                onChangeText={text => setReview(text)}
                                value={review}
                                placeholder={strings('lang.Enternotesaboutthedriver')}
                                placeholderTextColor={MediumGrey}
                                style={{
                                    width: '100%',
                                    alignSelf: 'center',
                                    // borderRadius: 15,
                                    paddingHorizontal: '5%',
                                    borderColor: MediumGrey,
                                    color: review ? Black : DarkGrey,
                                    // borderWidth: 1,
                                    fontFamily: appFontBold,
                                    height: '100%',
                                    textAlignVertical: 'top',
                                    fontSize: screenWidth / 30,
                                    textAlign: I18nManager.isRTL ? 'right' : 'left',
                                }}
                            />
                        </View>
                        <View
                            style={{
                                width: '95%',
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignSelf: 'center',
                                marginVertical: screenHeight / 40,
                                flexDirection: 'row'
                            }}
                        >
                            <Button
                                transparent
                                disabled={starCount == 0 ? true : false}
                                onPress={() => {
                                    rateDriver()
                                }}
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '100%',
                                    height: screenHeight / 22,
                                    backgroundColor: DarkBlue,
                                    borderRadius: 20,
                                    opacity: starCount == 0 ? .5 : 1
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 28,
                                        color: White,
                                    }}
                                >
                                    {strings('lang.send')}
                                </Text>
                            </Button>

                        </View>
                    </ScrollView>
                </Modalize>

                <RBSheet
                    ref={refRBSheetReasons}
                    height={screenHeight / 1.3}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            width: '100%',

                            alignSelf: 'center',
                            borderTopStartRadius: 15,
                            borderTopEndRadius: 15
                        },
                        wrapper: {

                        },
                        draggableIcon: {

                        }
                    }}
                // onClose={() => { props.navigation.navigate('Home') }}
                // closeOnDragDown={true}
                >
                    <ScrollView
                        style={{ alignSelf: 'center', width: '100%', paddingVertical: screenHeight / 50 }}
                        showsVerticalScrollIndicator={false}
                    >
                        {/* <View style={{ alignItems: 'center', alignSelf: 'center', justifyContent: 'flex-start', width: '100%', paddingVertical: '3%', }}> */}
                        {/* <Image source={require('../images/modrek/animal.png')} style={{ resizeMode: 'contain', width: '60%', height: '30%', alignItems: 'center', tintColor: Red, marginTop: '20%', marginBottom: '10%' }} /> */}
                        <View style={{ height: screenHeight / 25, width: '60%', alignItems: 'center', alignSelf: 'center', justifyContent: 'center', }}>
                            <Text style={{ fontSize: screenWidth / 25, fontFamily: appFontBold, color: DarkGrey, }}>{strings('lang.Whatisthereasonforcancelingthetrip')}</Text>
                        </View>
                        {reasons.map((item, index) => {
                            return <Pressable
                                onPress={() => { setReasonId(item.id) }}
                                style={[styles.reasonContainer, {
                                    backgroundColor: item.id == reasonsId ? DarkBlue : WhiteGery,
                                    borderColor: item.id == reasonsId ? DarkBlue : MediumGrey,
                                }]}>
                                <Text style={{
                                    fontSize: screenWidth / 30,
                                    fontFamily: appFontBold,
                                    color: item.id == reasonsId ? White : Black,
                                }}>{item.reason}</Text>
                            </Pressable>
                        })}


                        <View style={{ width: '95%', height: screenHeight / 7, alignSelf: 'center', marginVertical: '2.5%' }}>
                            {/* <Text style={{ fontSize: screenWidth / 26, fontFamily: appFont, color: Black, alignSelf: 'flex-start' }}>{strings('lang.Pleasewritethereason')}</Text> */}
                            <Textarea
                                placeholder={strings('lang.Pleasewritethereason')}
                                onChangeText={text => setReasonText(text)}
                                value={reasonText}
                                style={{
                                    width: '100%',
                                    alignSelf: 'center',
                                    borderRadius: 10,
                                    paddingHorizontal: '5%',
                                    borderColor: MediumGrey,
                                    color: DarkGrey,
                                    borderWidth: 1,
                                    fontFamily: appFontBold,
                                    height: '100%',
                                    textAlignVertical: 'top',
                                    fontSize: screenWidth / 30,
                                    textAlign: I18nManager.isRTL ? 'right' : 'left',
                                }}
                            />
                        </View>
                        <View style={{ height: screenHeight / 20 }}></View>
                        {/* </View> */}
                    </ScrollView>
                    <View style={{ alignItems: "center", justifyContent: 'space-between', width: '95%', alignSelf: "center", height: screenHeight / 20, flexDirection: 'row', }}>
                        <Button onPress={() => {
                            cancelTrip()
                        }} style={{ width: '45%', alignSelf: "center", height: '100%', backgroundColor: DarkBlue, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.Confirm')}</Text>
                        </Button>
                        <Button onPress={() => { refRBSheetReasons.current.close(); }}
                            style={{ width: '45%', alignSelf: "center", height: '100%', backgroundColor: WhiteGery, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Black, }}>{strings('lang.back')}</Text>
                        </Button>
                    </View>
                    <View style={{ height: screenHeight / 20 }}></View>

                </RBSheet>

                <View style={{ height: 10 }}></View>
            </View>
        );
    }
};

const styles = StyleSheet.create({
    buttonsContainer: {
        width: '95%',
        height: screenHeight / 18,
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center'
    },
    acceptButton: {
        backgroundColor: DarkBlue,
        width: '66%',
        height: screenHeight / 20,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 20,
    },
    button1: {
        backgroundColor: Red,
        width: '30%',
        height: screenHeight / 20,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 20,
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
    },
    container: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white',
    },
    contentContainer: {
        width: '95%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2.5%',
        borderRadius: 10,
        paddingHorizontal: 0,
        flexDirection: 'row'
    },
    textInput: {
        width: '100%',
        alignSelf: 'center',
        borderRadius: 5,
        height: '100%',
        alignItems: 'flex-start',
        justifyContent: 'flex-end'
    },
    locationImage: {
        resizeMode: 'contain',
        width: '50%',
        height: '50%'
    },
    handle__shape: {
        alignSelf: 'center',
        width: screenWidth / 6,
        height: 6,
        borderRadius: 10,
        // backgroundColor: Black,
    },
    input1: {
        height: screenHeight / 10,
        borderRadius: 20,
        width: '95%',
        backgroundColor: White,
        borderWidth: .8,
        flexDirection: 'row',
        paddingStart: '2%',
        borderColor: MediumGrey,
        marginVertical: '5%',
        alignSelf: 'center'
    },
    modalize__content: {
        marginTop: 'auto',
        borderTopLeftRadius: 30,
        borderTopRightRadius: 30,
        zIndex: 10000000000000, shadowColor: '#000000', shadowOffset: { width: 5, height: 5 }, shadowRadius: 8, shadowOpacity: 0.3,
        elevation: 5, paddingBottom: screenHeight / 10,
    },
    reasonContainer: {
        height: screenHeight / 18,
        borderRadius: 20,
        width: '95%',
        backgroundColor: WhiteGery,
        borderWidth: .8,
        borderColor: MediumGrey,
        marginVertical: '2%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
    },
});

export default DetailsTrip;
