import React, { Component, useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  StyleSheet,
  I18nManager,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import {
  appFont,
  appFontBold,
  Green,
  WhiteGreen,
  screenWidth,
  White,
  DarkGreen,
  Blue,
  MediumGrey,
  DarkGrey,
  WhiteGery,
  Black,
  screenHeight,
  Red,
  appColor1,
  MediumGreen,
  DarkYellow,
  DarkBlue,
  Gold,
} from '../components/Styles';
import { Button } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonEquipment from '../components/SkeletonEquipment';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import RBSheet from 'react-native-raw-bottom-sheet';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import * as paymentActions from '../../Store/Actions/payment';
import * as dailyTripsActions from '../../Store/Actions/dailyTrips';
import * as packagesActions from '../../Store/Actions/packages';
import * as vipTripsActions from '../../Store/Actions/vipTrips';
import * as cityTripsActions from '../../Store/Actions/cityTrips';
import Loading from '../components/Loading';


const PaymentMethods2 = props => {
  const dispatch = useDispatch();
  const refRbSheet = useRef();
  const [termsAndConditionData, setTermsAndConditionData] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [paymentMethodId, setPaymentMethodId] = useState(0);
  const [isSubscription, setIsSubscription] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState([]);

  const [userPackage, setUserPackage] = useState({});
  const [trip, setTrip] = useState({});
  const [tripType, setTripType] = useState('');

  useEffect(() => {




  }, []);

  if (loading) {
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.Walletcharging')}
          drawerPress={() => {
            props.navigation.navigate('More');
          }}
          backPress={() => {
            props.navigation.goBack();
          }}
        />

        <ScrollView
          style={{ width: '100%', height: '100%', marginBottom: '2%' }}
          showsVerticalScrollIndicator={false}
        >
          {[{ height: 6 }, { height: 6.5 }].map((item) => {
            return <View style={{ width: '90%', height: screenHeight / item.height, backgroundColor: WhiteGery, alignSelf: "center", marginTop: "5%", marginBottom: "2%" }} >
              <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                <View style={{ width: '100%', height: '100%', backgroundColor: WhiteGery, }} />
              </SkeletonPlaceholder>
            </View>
          })}
          <View style={{ height: screenHeight / 8 }}></View>
        </ScrollView>

      </View>
    );

  }
  else {
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
        <Header
          title={strings('lang.Walletcharging')}
          // drawerPress={() => {
          //   props.navigation.navigate('More');
          // }}
          backPress={() => {
            props.navigation.goBack();
          }}
        />
        {loadingMore
          ?
          <Loading />
          :
          <></>
        }

        <ScrollView
          style={{ width: '90%', height: '100%', alignSelf: 'center', marginBottom: '2%' }}
          showsVerticalScrollIndicator={false}
        >
          <View style={{ height: screenHeight / 40 }}></View>



          <Text style={styles.label}>{strings('lang.Choosethewalletchargingmethod')}</Text>
          <View style={styles.paymentContainer}>
            {paymentMethods.map((item) => {
              return <Pressable
                onPress={() => { setPaymentMethodId(item.id) }}
                style={styles.Payment}>
                <View style={item.id == paymentMethodId ? styles.activeIconContainer : styles.iconContainer}>
                  <Image
                    source={{ uri: item.image }}
                    // source={require('../images/Group191.png')}
                    style={{ width: '70%', height: '70%', resizeMode: 'contain', }} />
                </View>
                <Text style={styles.paymentText}>{item.name}</Text>
              </Pressable>
            })}

          </View>

        </ScrollView>



        <View style={{ height: screenHeight / 15 }}></View>

      </View>
    );
  }
};

const styles = StyleSheet.create({


  paymentContainer: {
    width: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    overflow: 'hidden'
  },
  Payment: {
    alignItems: 'center',
    justifyContent: 'center',
    // height: screenHeight / 7,
    width: '45%',
    marginBottom: '2%'
  },
  activeIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: screenWidth / 3,
    width: '100%',
    borderRadius: 20,
    backgroundColor: White,
    borderColor: Gold,
    borderWidth: 1
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: screenWidth / 3,
    width: '100%',
    borderRadius: 20,
    backgroundColor: WhiteGery,
  },
  paymentText: { color: Black, fontSize: screenWidth / 40, fontFamily: appFont },


  label: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 26, alignSelf: 'flex-start', marginBottom: screenHeight / 100 },


});

export default PaymentMethods2;
