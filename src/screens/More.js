import React, { useEffect, useRef, useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager, BackHandler } from "react-native";
import { appColor, sFontSize, screenHeight, DarkBlue, screenWidth, MediumGrey, WhiteGery, DarkGrey, White, MediumBlue, appFont, appFontBold, SKY, Red, Green, mSize, DarkGreen, Blue, Black, appColor1, Gold } from "../components/Styles";
import { Button } from 'native-base';
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Header from '../components/Header';
// import Header from '../components/Header';
import RBSheet from 'react-native-raw-bottom-sheet';
import RNRestart from 'react-native-restart';
import I18n from 'react-native-i18n';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as authActions from '../../Store/Actions/auth';
import Footer from '../components/Footer';
import FooterDriver from '../components/FooterDriver';
import { disconnectSocket } from '../../Store/Actions/socket';
import Toaster from '../components/Toaster';
import { useFocusEffect } from '@react-navigation/native';


const More = props => {

    const [name, setName] = useState('');
    const [touchId, setTouchId] = useState('0');
    const refRbSheet = useRef();
    const refRbSheet1 = useRef();
    const [lang, setLang] = useState('');
    const [lan, setLan] = useState('ar');
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [userType, setUserType] = useState('1');
    const [token, setToken] = useState('');
    const [deletion, setDeletion] = useState(false);

    const dispatch = useDispatch();

    // const user = useSelector(state => state.auth.user)

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                props.navigation.goBack()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    useEffect(() => {
        async function GetInitalData() {
            let userType = await AsyncStorage.getItem('userType')
            let token = await AsyncStorage.getItem('token')
            setUserType(userType)
            setToken(token)

            let lan = await AsyncStorage.getItem('lan')
            setLang(lan)
            if (lan == 'ar') {
                setTouchId('0')
            }
            else if (lan == 'en') {
                setTouchId('1')
            }
        }
        GetInitalData()
    }, []);

    const loginfirst = async () => {
        Toaster(
            'top',
            'danger',
            Red,
            strings('lang.mustLogin'),
            White,
            1500,
            screenHeight / 15,
        );
    }

    const set_ar = async () => {
        // let lan = await AsyncStorage.getItem("lan")
        // if (lan == 'en') {
        // this.setState({
        //   lan: 'ar',
        //   modalDeleteVisible: false
        // })
        AsyncStorage.setItem('lan', 'ar')
        AsyncStorage.setItem('stat', '1')
        I18n.locale = "ar";
        I18nManager.forceRTL(true);
        RNRestart.Restart();
        // }
    }

    const set_en = async () => {
        // let lan = await AsyncStorage.getItem("lan")
        // if (lan == 'ar') {
        // this.setState({
        //   lan: 'en',
        // })
        AsyncStorage.setItem('lan', 'en')
        I18n.locale = "en";
        I18nManager.forceRTL(false);
        RNRestart.Restart();
        // }
    }

    const logout = async () => {
        dispatch(authActions.logout());
        dispatch(disconnectSocket());
        await AsyncStorage.removeItem('token')
        await AsyncStorage.removeItem('userType')
        await AsyncStorage.removeItem('id')
        await AsyncStorage.removeItem('name')
        await AsyncStorage.removeItem('email')
        await AsyncStorage.removeItem('image')
        await AsyncStorage.removeItem('dial_code')
        await AsyncStorage.removeItem('phone')
        await AsyncStorage.removeItem('gender')
        props.navigation.navigate('Login')
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart') }} title={strings('lang.Profile')} backPress={() => { props.navigation.goBack() }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenWidth / 3,
                                    height: screenWidth / 3,
                                    borderRadius: screenWidth / 6,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '40%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '35%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 2,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <Footer current={'More'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header title={strings('lang.More')} backPress={() => { props.navigation.goBack() }} />

                <View style={{ width: screenWidth / 3, height: screenWidth / 3, marginBottom: '5%', overflow: 'hidden', alignSelf: "center", alignItems: "center", justifyContent: "center", }}>
                    <Image style={{ width: '70%', height: '100%', resizeMode: "contain", }} source={require('../images/Group10544.png')} />
                </View>
                <ScrollView showsVerticalScrollIndicator={false} style={{ flexDirection: "column", alignSelf: "center", width: "100%", }}>
                    <View style={{ flexDirection: "column", alignSelf: "center", justifyContent: "flex-start", width: "100%", alignItems: 'center', }}>
                        <View style={{ width: '95%', backgroundColor: WhiteGery, borderRadius: 20, height: '100%', paddingBottom: screenHeight / 40 }}>
                            <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                if (!token) {
                                    loginfirst()
                                }
                                else {
                                    if (userType == '2') {
                                        props.navigation.navigate('MyProfileDriver')
                                    }
                                    else {
                                        props.navigation.navigate('MyProfile')
                                    }
                                }
                            }}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/svgexport-9.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.Profile')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            {userType == '1'
                                &&
                                <>
                                    <TouchableOpacity style={styles.optionContainer} onPress={() => props.navigation.navigate('Orders')}>
                                        <View style={styles.optionSubContainer}>
                                            <View style={styles.iconContainer}>
                                                <Image style={styles.icon} source={require('../images/date.png')} />
                                            </View>
                                            <View style={styles.labelContainer}>
                                                <Text style={styles.label}>{strings('lang.Triplog')}</Text>
                                            </View>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={styles.optionContainer} onPress={() => props.navigation.navigate('Packages')}>
                                        <View style={styles.optionSubContainer}>
                                            <View style={styles.iconContainer}>
                                                <Image style={styles.icon} source={require('../images/Group-30.png')} />
                                            </View>
                                            <View style={styles.labelContainer}>
                                                <Text style={styles.label}>{strings('lang.socialist1')}</Text>
                                            </View>
                                        </View>
                                    </TouchableOpacity>
                                </>
                            }





                            <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                // if (!token) {
                                //     loginfirst()
                                // }
                                // else {
                                props.navigation.navigate('Safety')
                                // }
                            }}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/Group10060.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.Securityandsafety')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>


                            <TouchableOpacity style={styles.optionContainer} onPress={() => { refRbSheet.current.open(); }}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/earth.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.Changethelanguage')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            {/* <TouchableOpacity style={styles.optionContainer} onPress={() => { }}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/setting.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.setting')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity> */}

                            <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                if (!token) {
                                    loginfirst()
                                }
                                else {
                                    props.navigation.navigate('ContactUs')
                                }
                            }}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/Group-130.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.Technicalsupport')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                if (!token) {
                                    loginfirst()
                                }
                                else {
                                    props.navigation.navigate('Notifications')
                                }
                            }}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/Group-130.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.Notifications')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>


                            <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                // if (!token) {
                                //     loginfirst()
                                // }
                                // else {
                                props.navigation.navigate('TermsAndConditions')
                                // }
                            }}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/Group264.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.terms_and_conditions')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            {token
                                ?
                                <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                    setDeletion(true)
                                    refRbSheet1.current.open()
                                }}>
                                    <View style={styles.optionSubContainer}>
                                        <View style={styles.iconContainer}>
                                            <Image style={{ ...styles.icon, ...{ width: '50%' } }} source={require('../images/xx.png')} />
                                        </View>
                                        <View style={styles.labelContainer}>
                                            <Text style={styles.label}>{strings('lang.deleteAccount')}</Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                                :
                                <></>
                            }

                            <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                if (!token) {
                                    props.navigation.navigate('Login')
                                }
                                else {
                                    setDeletion(false)
                                    refRbSheet1.current.open()
                                }
                            }}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/Group-132.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{token ? strings('lang.Exit') : strings('lang.Login')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            {/* {userType == '2'
                            ?
                            <Button onPress={() => { logout() }} style={styles.buttonContainer}>
                                <Text style={styles.buttonText}>{strings('lang.Switchtotraveler')}</Text>
                            </Button>
                            :
                            <Button onPress={() => { logout() }} style={styles.buttonContainer}>
                                <Text style={styles.buttonText}>{strings('lang.Switchtodriver')}</Text>
                            </Button>
                        } */}



                        </View>
                    </View>


                </ScrollView >

                <RBSheet
                    ref={refRbSheet}
                    height={screenHeight / 2}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: 'center',
                            borderTopRightRadius: 30,
                            borderTopLeftRadius: 30,
                        },
                        wrapper: {},
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: DarkGrey,
                        },
                    }}
                // closeOnDragDown={true}
                >
                    <ScrollView
                        style={{
                            width: '92%',
                            alignSelf: 'center',
                            height: '81%',
                            paddingVertical: '2%',
                        }}
                    >
                        <View
                            style={{
                                flexDirection: 'row',
                                width: '65%',
                                height: screenHeight / 20,
                                alignSelf: 'flex-end',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                            }}
                        >
                            <Text style={{ ...styles.labelModal, ...{ marginStart: '-8%' } }}>
                                {strings('lang.Changethelanguage')}
                            </Text>
                            <Button
                                onPress={() => {
                                    refRbSheet1.current.close();
                                }}
                                style={{
                                    width: screenWidth / 15,
                                    height: screenWidth / 15,
                                    alignItems: 'center',
                                    alignSelf: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: White,
                                    elevation: 0,
                                }}
                            >
                                <Image source={require('../images/xx.png')} style={{ resizeMode: 'contain', width: '75%', height: '75%' }} />
                            </Button>
                        </View>
                        <View
                            style={{
                                flexDirection: 'column',
                                marginVertical: '5%',
                                alignItems: 'center',
                                alignSelf: 'flex-start',
                                justifyContent: 'center',
                            }}
                        >
                            <View style={styles.radiocontainer}>
                                {touchId == '0' ? (
                                    <Button style={styles.activeradio}>
                                        <View style={styles.activeview}></View>
                                    </Button>
                                ) : (
                                    <Button
                                        style={styles.radio}
                                        onPress={() => setTouchId('0')}
                                    ></Button>
                                )}
                                <View style={{ marginStart: '4%' }}>
                                    <Text style={styles.label1}>
                                        {strings('lang.arabicLanguage')}
                                    </Text>
                                </View>
                            </View>
                            <View style={styles.radiocontainer}>
                                {touchId == '1' ? (
                                    <Button style={styles.activeradio}>
                                        <View style={styles.activeview}></View>
                                    </Button>
                                ) : (
                                    <Button
                                        style={styles.radio}
                                        onPress={() => setTouchId('1')}
                                    ></Button>
                                )}
                                <View style={{ marginStart: '4%' }}>
                                    <Text style={styles.label1}>
                                        {strings('lang.englishLanguage')}
                                    </Text>
                                </View>
                            </View>

                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '100%',
                                    alignSelf: 'center',
                                    marginTop: '20%',
                                }}
                            >
                                <Button
                                    style={{
                                        width: '47%',
                                        alignSelf: 'center',
                                        height: 45,
                                        backgroundColor: DarkBlue,
                                        alignItems: 'center',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                        borderRadius: 10,
                                        flexDirection: 'row',
                                    }}
                                    onPress={() => {
                                        refRbSheet.current.close();
                                        if (touchId == '0' && lang != 'ar') {
                                            set_ar()
                                        }
                                        else if (touchId == '1' && lang != 'en') {
                                            set_en()
                                        }

                                    }}
                                >
                                    <Text
                                        style={{
                                            fontSize: screenWidth / 28,
                                            fontFamily: appFontBold,
                                            color: White,
                                        }}
                                    >
                                        {strings('lang.Confirm')}
                                    </Text>
                                </Button>
                            </View>
                        </View>
                    </ScrollView>
                </RBSheet>

                <RBSheet
                    ref={refRbSheet1}
                    height={screenHeight / 3}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: 'center',
                            borderTopRightRadius: 30,
                            borderTopLeftRadius: 30,
                        },
                        wrapper: {},
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: DarkGrey,
                        },
                    }}
                // closeOnDragDown={true}
                >
                    <View style={{ width: '90%', alignSelf: 'center', }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                width: '65%',
                                height: screenHeight / 20,
                                alignSelf: 'flex-end',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                            }}
                        >
                            <Text style={{ ...styles.labelModal, ...{ marginStart: '2.5%', } }}>
                                {deletion ? strings('lang.DeleteAccount') : strings('lang.Logout')}
                            </Text>
                            <Button
                                onPress={() => {
                                    refRbSheet1.current.close();
                                }}
                                style={{
                                    width: screenWidth / 15,
                                    height: screenWidth / 15,
                                    alignItems: 'center',
                                    alignSelf: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: White,
                                    elevation: 0,
                                }}
                            >
                                <Image source={require('../images/xx.png')} style={{ resizeMode: 'contain', width: '75%', height: '75%' }} />
                            </Button>
                        </View>

                        <Text style={{ ...styles.labelModal, ...{ alignSelf: 'center', marginStart: '0%', fontSize: screenWidth / 20, marginTop: '10%', color: Red } }}>
                            {deletion ? strings('lang.message20') : strings('lang.message14')}
                        </Text>

                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: '10%' }}>
                            <Button
                                style={{
                                    width: '47%',
                                    alignSelf: 'center',
                                    height: 45,
                                    backgroundColor: Red,
                                    alignItems: 'center',
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    borderRadius: 10,
                                    flexDirection: 'row',
                                }}
                                onPress={() => {
                                    refRbSheet1.current.close();
                                    logout();
                                    authActions.logout()
                                }}
                            >
                                <Text
                                    style={{
                                        fontSize: screenWidth / 28,
                                        fontFamily: appFontBold,
                                        color: White,
                                    }}
                                >
                                    {strings('lang.Yes')}
                                </Text>
                            </Button>
                            <Button
                                style={{
                                    width: '47%',
                                    alignSelf: 'center',
                                    height: 45,
                                    backgroundColor: DarkBlue,
                                    alignItems: 'center',
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    borderRadius: 10,
                                    flexDirection: 'row',
                                }}
                                onPress={() => {
                                    refRbSheet1.current.close();

                                }}
                            >
                                <Text
                                    style={{
                                        fontSize: screenWidth / 28,
                                        fontFamily: appFontBold,
                                        color: White,
                                    }}
                                >
                                    {strings('lang.No')}
                                </Text>
                            </Button>
                        </View>


                    </View>

                </RBSheet>


                {
                    userType == '2'
                        ?
                        <FooterDriver current={'More'} navigation={props.navigation} />
                        :
                        <Footer current={'More'} navigation={props.navigation} />

                }


            </View >
        )
    }
};


export default More;
const styles = StyleSheet.create({
    radiocontainer: {
        flexDirection: 'row',
        alignSelf: 'flex-start',
        alignItems: 'center',
        marginVertical: '2%',
    },

    activeradio: {
        width: screenHeight / 30,
        height: screenHeight / 30,
        borderRadius: screenHeight / 60,
        borderColor: DarkBlue,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: White,
    },
    activeview: {
        width: screenHeight / 50,
        height: screenHeight / 50,
        borderRadius: screenHeight / 100,
        backgroundColor: DarkBlue,
    },
    radio: {
        width: screenHeight / 30,
        height: screenHeight / 30,
        borderRadius: screenHeight / 60,
        borderColor: DarkGrey,
        borderWidth: 1,
        backgroundColor: White,
    },
    label1: { color: DarkGrey, fontSize: screenWidth / 26, fontFamily: appFontBold, alignSelf: 'flex-start' },


    input: {
        height: 45, borderWidth: 1, borderRadius: 2, borderColor: DarkGrey, flexDirection: 'row', justifyContent: 'center',
        width: "85%", alignSelf: 'center'
    },
    inputText: {
        textAlign: I18nManager.isRTL ? 'right' : 'left', color: DarkGrey, width: '100%', fontFamily: appFont,
        fontSize: screenWidth / 30, alignSelf: 'center'
    },
    loginError: {
        color: Red,
        fontFamily: appFont,
        alignSelf: 'flex-start',
        fontSize: screenWidth / 25,
        marginVertical: 3,
        marginHorizontal: '10%'
    },
    buttonContainer: {
        width: '90%',
        height: screenHeight / 20,
        borderRadius: 20,
        backgroundColor: DarkBlue,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: screenHeight / 15
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    optionContainer: { alignSelf: 'center', width: '100%', height: screenHeight / 21, borderWidth: 0, marginTop: screenHeight / 100, },
    optionContainer2: { alignSelf: 'center', width: '95%', height: 50, borderWidth: 0.5, marginTop: screenHeight / 20, backgroundColor: DarkBlue, borderRadius: 10, borderColor: MediumGrey, },
    optionSubContainer: { alignSelf: 'center', width: '95%', height: '100%', flexDirection: 'row', alignItems: 'center' },
    iconContainer: { width: screenWidth / 15, height: screenWidth / 15, borderRadius: screenWidth / 30, alignItems: 'center', justifyContent: 'center', backgroundColor: Gold },
    icon: { width: '75%', height: '55%', resizeMode: 'contain', tintColor: White },
    labelContainer: { height: '100%', justifyContent: 'center', marginStart: '4%' },
    label: { color: Black, fontSize: screenWidth / 26, fontFamily: appFontBold, alignSelf: 'flex-start' },
    labelModal: { color: Black, fontSize: screenWidth / 26, fontFamily: appFontBold, alignSelf: 'center', marginStart: '13%' },
    textBlue: { color: DarkBlue, fontSize: screenWidth / 22, fontFamily: appFontBold }
});