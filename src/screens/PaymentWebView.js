import { View, Text, Image, I18nManager, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import React, { useEffect, useState } from 'react';
import Header from '../components/Header';
import { strings } from './i18n';
import {
    appColor1,
    appColor2,
    appFont,
    appFontBold,
    Black,
    DarkGreen,
    DarkGrey,
    MediumGrey,
    Red,
    screenHeight,
    screenWidth,
    White,
    WhiteGery,
} from '../components/Styles';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import { Button } from 'native-base';
import { useDispatch, useSelector } from 'react-redux';
import { useIsFocused } from '@react-navigation/native';
import Toaster from '../components/Toaster';
import WebView from 'react-native-webview';
import Loading from '../components/Loading';
import * as packagesActions from '../../Store/Actions/packages';

const PaymentWebView = props => {
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [modalVisible, setModalVisible] = useState(false)
    const dispatch = useDispatch();
    const isFocused = useIsFocused();
    const [Url, setUrl] = useState('')

    useEffect(() => {
        console.log(props.route.params.type);
        console.log(props.route.params.price);
        console.log(props.route.params.paymentMethodId);
        if (props.route.params.type == 'Wallet') {
            setUrl(`https://step-db.com/payment/checkout?model_id=${props.route.params.id}&model_type=${props.route.params.type}&payment_method_id=${props.route.params.paymentMethodId}&price=${props.route.params.price}`)

        } else {
            setUrl(`https://step-db.com/payment/checkout?model_id=${props.route.params.id}&model_type=${props.route.params.type}&payment_method_id=${props.route.params.paymentMethodId}`)
        }

    }, []);

    const acceptOffer = async () => {
        setLoadingMore(true)

        let response = await dispatch(packagesActions.acceptOffer(props.route.params.paymentMethodId, props.route.params.id))
        if (response.success == true) {
            setLoadingMore(false)
            Toaster(
                'top',
                'success',
                DarkGreen,
                strings('lang.Paid'),
                White,
                1500,
                screenHeight / 15,
            );
            props.navigation.push("Packages")
        }
        else {
            setLoadingMore(false)
            Toaster(
                'top',
                'danger',
                Red,
                response.message,
                White,
                1500,
                screenHeight / 15,
            );
        }
        setLoadingMore(false)
    }


    const _onNavigationStateChange = async (webViewState) => {

        console.log('webViewState', webViewState)
        console.log('webViewState.url', webViewState.url)
        if (webViewState.url.includes('success')) {
            console.log("finish")
            Toaster(
                'top',
                'success',
                DarkGreen,
                strings('lang.Paid'),
                White,
                1500,
                screenHeight / 50,
            );
            if (props.route.params.type == 'DailyTrip') {
                props.navigation.navigate('MapAddress', { screen: 'Home', cityTrip: false, pending: true, pendingTrip: { ...props.route.params.trip, is_paid: true } })
            }
            else if (props.route.params.type == 'VipTrip') {
                props.navigation.push('DetailsTrip', { item: props.route.params.trip, type: 'orderVip' })
            }
            else if (props.route.params.type == 'CityTrip') {
                props.navigation.push('DetailsTrip', { item: props.route.params.trip, type: 'betweenCities' })
            }
            else if (props.route.params.type == 'UserPackage') {
                acceptOffer();
            }
            else if (props.route.params.type == 'Wallet') {
                props.navigation.push("Portfolio")
            }
            // checkPayOnline()

        }
        else if (webViewState.url.includes('failed')) {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.Unpaid'),
                White,
                1500,
                screenHeight / 50,
            );
            if (props.route.params.type == 'Wallet') {
                setUrl(`https://step-db.com/payment/checkout?model_id=${props.route.params.id}&model_type=${props.route.params.type}&payment_method_id=${props.route.params.paymentMethodId}&price=${props.route.params.price}`)

            } else {
                setUrl(`https://step-db.com/payment/checkout?model_id=${props.route.params.id}&model_type=${props.route.params.type}&payment_method_id=${props.route.params.paymentMethodId}`)
            }
        }

        else {
        }
    }

    return (
        <View style={{ flex: 1, backgroundColor: White }}>
            <Header
                title={strings('lang.Payment')}
                backPress={() => {
                    props.navigation.goBack();
                }}
            />

            {loadingMore
                ?
                <Loading />
                :
                <View></View>
            }

            {Url ?
                <WebView
                    // source={{ uri: '[domain.com]/payment/index.php?page=myfatoorah&arabia_hook_id='+this.state.system_hook_id+'&arabia_rent_request_id='+this.state.rent_request_id+'&arabia_json_responses=1' }}
                    style={{ height: screenHeight / 1.2, width: screenWidth, }}
                    source={{ uri: Url }}
                    onNavigationStateChange={_onNavigationStateChange}
                    javaScriptEnabled={true}
                    domStorageEnabled={true}
                    startInLoadingState={false}
                />
                :
                <View></View>
            }
        </View >
    );
};

const styles = StyleSheet.create({


});

export default PaymentWebView;

