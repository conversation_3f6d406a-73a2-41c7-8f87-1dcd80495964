import React, { Component, useEffect, useState } from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  StyleSheet,
  ScrollView,
  I18nManager,
  Pressable,
  PermissionsAndroid,
  BackHandler,
} from 'react-native';
import {
  appFont,
  appFontBold,
  Green,
  WhiteGreen,
  screenWidth,
  White,
  DarkGreen,
  Blue,
  MediumGrey,
  DarkGrey,
  WhiteGery,
  screenHeight,
  Black,
  DarkBlue,
  appColor2,
  appColor1,
  Red,
  Grey,
} from '../components/Styles';
import { Button, Textarea } from 'native-base';
import Header from '../components/Header';
// import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
// import Model from './Model';
// import Loading from '../components/Loading';
import { TextInput, TouchableOpacity, TouchableWithoutFeedback } from 'react-native-gesture-handler';
import Footer from '../components/Footer';
import { useDispatch, useSelector } from 'react-redux';
import Toaster from '../components/Toaster';
import SelectDropdown from 'react-native-select-dropdown';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker'; // Migration from 2.x.x to 3.x.x => showImagePicker API is removed.
import ImageResizer from 'react-native-image-resizer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Loading from '../components/Loading';
import * as authActions from '../../Store/Actions/auth';
import { useFocusEffect } from '@react-navigation/native';

const MyProfile = props => {

  const [getClientProfileData, setGetClientProfileData] = useState({});
  const [profile, setProfile] = useState({});
  const [taxationNo, setTaxationNo] = useState('');
  const [CommercialRegistrationNo, setCommercialRegistrationNo] = useState('');
  const [changeTaxNumber, setChangeTaxNumber] = useState(false);
  const [changeCommercialRegistrationNo, setChangeCommercialRegistrationNo,] = useState(false);
  const [changeCity, setChangeCity] = useState(false);
  const [cityId, setCityId] = useState('');
  const [cityName, setCityName] = useState('');
  const [data, setData] = useState([
    {
      id: 0,
      name: strings('lang.personalinformation')
    },
    {
      id: 1,
      name: strings('lang.cardata')
    },
  ]);
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [gender, setGender] = useState('male');
  const [email, setEmail] = useState('');
  const [image, setImage] = useState('');
  const [socialState, setSocialState] = useState(strings('lang.bachelor'));
  const [smoker, setSmoker] = useState(strings('lang.Yes'));
  const [busy, setBusy] = useState(strings('lang.free'));
  const [startTime, setStartTime] = useState('4:00 PM');
  const [endTime, setEndTime] = useState('1:00 AM');
  const [postalcode, setPostalcode] = useState('1234');
  const [city, setCity] = useState('مثال لنص');
  const [district, setDistrict] = useState('مثال لنص');
  const [streetname, setStreetname] = useState('مثال لنص');
  const [address, setAddress] = useState('هذا النص هو مثال لنص يمكن أن يستبدل في نفس');
  const [typeCar, setTypeCar] = useState('تيوتا');
  const [model, setModel] = useState('كورولا');
  const [year, setYear] = useState('2021');
  const [colorCar, setColorCar] = useState('احمر');
  const [numberCar, setNumberCar] = useState('556155');
  const [dataId, setDataId] = useState(0);
  const [cover, setCover] = useState();
  const [imageUrl, setImageUrl] = useState('');
  const [imageId, setImageId] = useState();
  const [imageLeadership, setImageLeadership] = useState();
  const [imageCar, setImageCar] = useState();
  const [imageCriminalStatus, setImageCriminalStatus] = useState();
  const [imageCarFront, setImageCarFront] = useState();
  const [imageCarBack, setImageCarBack] = useState();
  const [imageCarRight, setImageCarRight] = useState();
  const [imageCarLeft, setImageCarLeft] = useState();
  const [imageCarInside, setImageCarInside] = useState();

  const [coverCar, setCoverCar] = useState();
  const [coverCar1, setCoverCar1] = useState();
  const [activeData, setActiveData] = useState(false);
  const [loading, setLoading] = useState(false);
  const [genderId, setGenderId] = useState('1');
  const [socialStatusId, setSocialStatusId] = useState('1');
  const [smokeId, setsmokeId] = useState('1');
  const [busyId, setBusyId] = useState('1');
  const [dialCode, setDialCode] = useState('');
  const dispatch = useDispatch();

  const profilee = useSelector(state => state.auth.profile);
  console.log('profilee', profilee);

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        props.navigation.goBack()
        return true;
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );

  useEffect(() => {

    const GetInitalData = () => {
      setProfile(profilee);
      setName(profilee.name)
      setDialCode(profilee.dial_code)
      setPhone(profilee.phone)
      setEmail(profilee.email)
      setGender(profilee.gender)
      setGenderId(profilee.gender == 'male' ? '1' : '2')
      setCityId(profilee.city_id)
      setImageUrl(profilee.image);
    };
    GetInitalData();

  }, []);


  const editProfile = async () => {
    if (dataId == 0) {
      setLoading(true)
      try {
        let response = await dispatch(authActions.updateProfile(
          name,
          dialCode,
          phone,
          email,
          cover,
        ));

        if (response.data) {
          Toaster(
            'top',
            'success',
            DarkGreen,
            strings('lang.profileupdatedsuccessfully'),
            White,
            1500,
            screenHeight / 50,
          );
          await AsyncStorage.setItem("name", response.data.name ? response.data.name : '');
          await AsyncStorage.setItem("email", response.data.email ? response.data.email : '');
          await AsyncStorage.setItem("image", response.data.image ? response.data.image : '');
          await AsyncStorage.setItem("phone", response.data.phone ? response.data.phone : '');
          setActiveData(false);
          setLoading(false)
        } else {
          if (response.message) {
            Toaster(
              'top',
              'danger',
              Red,
              response.message,
              White,
              1500,
              screenHeight / 50,
            );
            console.log(response);
            setLoading(false)
          }
        }
      } catch (err) {
        console.log(err);
        setLoading(false)
      }
    }
    else {

    }
  };


  const chooseImagesCover = async () => {

    let options = {
      quality: 0.5,
      maxWidth: 500,
      maxheight: 400,
      title: 'Select Image',
      customButtons: [
        {
          name: 'customOptionKey',
          title: 'Choose Photo from Custom Option',
        },
      ],
      storageOptions: {
        skipBackup: true,
        path: 'images',
      },
    };
    launchImageLibrary(options, response => {
      console.log('Response = ', response);

      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.error) {
        console.log('ImagePicker Error: ', response.error);
      } else if (response.customButton) {
        console.log('User tapped custom button: ', response.customButton);
        alert(response.customButton);
      } else {
        // let cover = response.assets[0];
        // You can also display the image using data:
        // let source = {
        //   uri: 'data:image/jpeg;base64,' + response.data
        // };
        let cover = {
          uri: response.assets[0].uri,
          type: response.assets[0].type,
          name: response.assets[0].fileName,
          link: '',
        };

        setCover(cover);

      }
    });
  };


  return (
    <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
      <Header
        title={strings('lang.MyAccount')}
        backPress={() => {
          props.navigation.goBack();
        }}
      />
      {loading ? <Loading /> : <></>}
      <ScrollView showsVerticalScrollIndicator={false} style={{ height: screenHeight / 1.2 }}>

        <View
          style={{
            alignSelf: 'center',
            width: screenWidth,
            height: screenHeight / 6,
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
          }}
        >
          <TouchableOpacity onPress={() => { if (activeData) { chooseImagesCover() } }} style={{ width: screenWidth / 4, height: screenWidth / 4, alignSelf: 'center', borderRadius: screenWidth / 8, overflow: 'hidden' }}>
            <Image
              source={cover ? { uri: cover.uri } : imageUrl ? { uri: imageUrl } : require('../images/Group10019.png')}
              style={{ width: '100%', height: '100%', resizeMode: 'cover' }}
            />
          </TouchableOpacity>
          <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: Black }}>
            {name}
          </Text>
        </View>

        <View style={{ height: screenHeight / 50 }}></View>

        <Text numberOfLines={1} style={styles.label}> {strings('lang.Username')}</Text>
        {activeData
          ?
          <TextInput
            onChangeText={text => {
              setName(text);
            }}
            value={name}
            placeholderTextColor={Black}
            style={styles.textInput}
          />
          :
          <View style={styles.activeData}>
            <Text numberOfLines={1} style={styles.labelGrey}>
              {name}
            </Text>
          </View>
        }

        <Text numberOfLines={1} style={styles.label}> {strings('lang.Phone')}</Text>
        {/* {activeData
          ?
          <TextInput
            onChangeText={text => {
              setPhone(text);
            }}
            value={phone}
            keyboardType={'number-pad'}
            placeholderTextColor={Black}
            style={styles.textInput}
          />
          : */}
        <View style={styles.activeData}>
          <Text numberOfLines={1} style={styles.labelGrey}>
            {phone}
          </Text>
        </View>
        {/* } */}

        {/* <Text numberOfLines={1} style={styles.label}> {strings('lang.Gender')}</Text>
        {activeData
          ?
          <View style={styles.contentContainer}>
            <View style={[styles.textInput1, { backgroundColor: WhiteGery }]}>
              <Pressable onPress={() => { setGenderId('1'); setGender('male') }} style={styles.genderContainer}>
                <View style={styles.gendercircleConatiner}>
                  <View style={[styles.gendercircle, { backgroundColor: genderId == '1' ? DarkBlue : White }]}>
                  </View>
                </View>
                <Text style={styles.genderText}>{strings('lang.male')}</Text>
              </Pressable>
              <Pressable onPress={() => { setGenderId('2'); setGender('female') }} style={styles.genderContainer}>
                <View style={styles.gendercircleConatiner}>
                  <View style={[styles.gendercircle, { backgroundColor: genderId == '2' ? DarkBlue : White }]}>
                  </View>
                </View>
                <Text style={styles.genderText}>{strings('lang.female')}</Text>
              </Pressable>
            </View>
          </View>
          :
          <View style={styles.activeData}>
            <Text numberOfLines={1} style={styles.labelGrey}>
              {gender ? strings(`lang.${gender}`) : ''}
            </Text>
          </View>
        } */}




        <Text numberOfLines={1} style={styles.label}> {strings('lang.Email')}</Text>
        {activeData
          ?
          <TextInput
            onChangeText={text => {
              setEmail(text);
            }}
            value={email}
            keyboardType={'email-address'}
            placeholderTextColor={Black}
            style={styles.textInput}
          />
          :
          <View style={styles.activeData}>
            <Text numberOfLines={1} style={styles.labelGrey}>
              {email}
            </Text>
          </View>
        }

      </ScrollView>

      {activeData ?
        <Pressable
          onPress={() => { setActiveData(false); editProfile() }}
          style={styles.buttonContainer}
        >
          <Text style={styles.buttonText}>{strings('lang.Save')}</Text>
        </Pressable>
        :
        <Pressable
          onPress={() => { setActiveData(true) }}
          style={styles.buttonContainer}
        >
          <Text style={styles.buttonText}>{strings('lang.Datamodification')}</Text>
        </Pressable>
      }


      {/* <Footer current={'list'} navigation={props.navigation} /> */}
    </View>
  );
};

const styles = StyleSheet.create({
  activeData: {
    width: '95%',
    height: screenHeight / 20,
    backgroundColor: WhiteGery,
    alignItems: 'flex-start',
    alignSelf: 'center',
    justifyContent: 'center',
    paddingHorizontal: '2.5%',
    marginBottom: 10,
    borderRadius: 100,
    // marginTop: 5
  },
  textInput: {
    minWidth: '95%',
    maxWidth: '95%',
    height: screenHeight / 20,
    alignItems: 'flex-start',
    alignSelf: 'center',
    justifyContent: 'center',
    paddingHorizontal: '2.5%',
    marginBottom: 10,
    // borderWidth: 1,
    // borderColor: Grey,
    borderRadius: 100,
    fontFamily: appFontBold,
    fontSize: screenWidth / 30,
    color: Black,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
    backgroundColor: WhiteGery
  },
  textImg: {
    fontSize: screenWidth / 30,
    fontFamily: appFont,
    color: Black,
    marginTop: '2%',
    marginStart: '2%'
  },
  labelContainer: {
    width: '90%',
    alignSelf: 'center',
    height: screenHeight / 18,
    justifyContent: 'center',
    paddingHorizontal: 5,
    borderBottomColor: DarkGrey,
    borderBottomWidth: 1,
  },
  itemContainer: {
    width: '90%',
    alignSelf: 'center',
    height: screenHeight / 20,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputsContainer: {
    width: '100%',
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  label: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 28,
    color: Black,
    alignSelf: 'flex-start',
    marginStart: '2.5%'
  },
  labelWhite: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 30,
    color: White,
  },
  labelGrey: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 30,
    color: Black,
    textAlign: I18nManager.isRTL ? 'left' : 'left',
  },
  button: {
    width: '90%',
    height: 40,
    backgroundColor: appColor1,
    alignSelf: 'center',
    marginHorizontal: 5,
    justifyContent: 'center',
    borderRadius: 5,
    alignItems: 'center',
    padding: 5,
    marginBottom: 0,
    zIndex: 3,
  },
  Changebutton: {
    width: '90%',
    height: 40,
    backgroundColor: Red,
    alignSelf: 'center',
    marginHorizontal: 5,
    justifyContent: 'center',
    borderRadius: 5,
    alignItems: 'center',
    padding: 5,
    marginBottom: 0,
    zIndex: 3,
  },
  changeData: {
    width: screenWidth,
    height: screenHeight / 18,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'space-evenly',
    marginBottom: 10,
    backgroundColor: WhiteGery,
    flexDirection: 'row',
    paddingHorizontal: '5%'
  },
  unActive: {
    width: screenWidth / 2.8,
    height: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  active: {
    width: screenWidth / 2.8,
    height: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 3,
    borderBottomLeftRadius: 5,
    borderBottomRightRadius: 5,
    borderBottomColor: DarkBlue
  },
  textData: {
    fontFamily: appFont,
    fontSize: screenWidth / 33
  },
  btnContainer: { width: '20%', height: '100%', justifyContent: 'center' },
  dataContainer: { flex: 1, },
  iconContainer: { borderRadius: 10, width: screenHeight / 18, height: screenHeight / 18, alignItems: 'center', justifyContent: 'center', backgroundColor: WhiteGery },
  icon: {
    width: '60%',
    height: '60%',
    resizeMode: 'contain',
    tintColor: appColor1,
    // backgroundColor:'red'
  },
  input: {
    height: 45,
    borderRadius: 10,
    color: Black,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    width: '90%',
    marginBottom: 10,
    backgroundColor: WhiteGery,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: WhiteGery,
  },

  inputText: {
    color: DarkGrey,
    fontFamily: appFontBold,
    fontSize: screenWidth / 33,
    alignSelf: 'flex-start',
    textAlign: 'left',
    // textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  dropdownInput: {
    height: screenHeight / 18,
    borderRadius: 5,
    flexDirection: 'row',
    marginBottom: 10,
    backgroundColor: WhiteGery,
    width: screenWidth / 1.6,
    paddingHorizontal: 10,
    textAlign: 'right',
  },
  // icon: {
  //   width: '5%',
  //   height: '15%',
  //   tintColor: appColor1,
  //   resizeMode: 'contain',
  //   marginHorizontal: '5%',
  //   // position: 'absolute',
  //   // right: I18nManager.isRTL ? 0 : '100%',
  // },
  buttonContainer: {
    backgroundColor: DarkBlue,
    width: '95%',
    height: screenHeight / 17,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    borderRadius: 100,
    elevation: 10,
    marginTop: 10,
    marginBottom: screenHeight / 50,
  },
  buttonText: {
    color: White,
    fontFamily: appFontBold,
    fontSize: screenWidth / 28,
  },
  buttonContainer1: {
    backgroundColor: WhiteGery,
    width: '95%',
    height: 45,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    borderRadius: 25,
    elevation: 10,
    borderColor: appColor1,
    borderWidth: 0.8,
  },
  buttonText1: {
    color: appColor1,
    fontFamily: appFontBold,
    fontSize: screenWidth / 25,
  },
  textImg: {
    fontSize: screenWidth / 30,
    fontFamily: appFont,
    color: Black,
    marginTop: '2%', alignSelf: 'flex-start'
  },
  imageContainer: {
    flexDirection: 'row',
    width: '100%',
    alignSelf: 'center',
    height: screenHeight / 6,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: MediumGrey,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconConatiner2: { height: '100%', width: '10%', alignItems: 'center', justifyContent: 'center', },
  genderContainer: { width: screenWidth / 2.5, height: screenHeight / 22, alignItems: 'center', justifyContent: 'flex-start', flexDirection: 'row', paddingHorizontal: '5%' },
  gendercircleConatiner: { width: screenHeight / 50, height: screenHeight / 50, borderRadius: screenHeight / 100, backgroundColor: White, borderWidth: .5, borderColor: MediumGrey, alignItems: 'center', justifyContent: 'center' },
  gendercircle: { width: screenHeight / 75, height: screenHeight / 75, borderRadius: screenHeight / 150, backgroundColor: White, },
  genderText: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 33, marginStart: 5 },
  contentContainer: {
    width: '95%',
    alignSelf: 'center',
    justifyContent: 'center',
    marginBottom: 10,
    height: screenHeight / 20,
    borderRadius: 5,
    paddingHorizontal: 0,
    flexDirection: 'row',
  },
  textInput1: {
    width: '100%',
    flexDirection: 'row',
    alignSelf: 'center',
    borderRadius: 100,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'space-between',
    textAlignVertical: 'center',
    fontFamily: appFont,
    fontSize: screenWidth / 32,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
    paddingHorizontal: 10, overflow: 'hidden'
  },
});

export default MyProfile;
